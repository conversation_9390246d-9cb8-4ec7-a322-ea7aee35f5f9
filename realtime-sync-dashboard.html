<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 لوحة المزامنة اللحظية - النسور الماسية</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .sync-dashboard {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
        }

        .sync-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
        }

        .sync-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .status-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            text-align: center;
        }

        .status-card.online {
            border-left: 5px solid #28a745;
        }

        .status-card.offline {
            border-left: 5px solid #dc3545;
        }

        .status-card.syncing {
            border-left: 5px solid #ffc107;
        }

        .data-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .data-type-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .data-type-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
        }

        .sync-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }

        .sync-indicator.active {
            background: #28a745;
            animation: pulse 2s infinite;
        }

        .sync-indicator.inactive {
            background: #dc3545;
        }

        .sync-indicator.pending {
            background: #ffc107;
            animation: blink 1s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .sync-controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .sync-log {
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-entry.success {
            color: #28a745;
        }

        .log-entry.error {
            color: #dc3545;
        }

        .log-entry.warning {
            color: #ffc107;
        }

        .log-entry.info {
            color: #17a2b8;
        }

        .permissions-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .btn.primary {
            background: #007bff;
            color: white;
        }

        .btn.success {
            background: #28a745;
            color: white;
        }

        .btn.warning {
            background: #ffc107;
            color: #212529;
        }

        .btn.danger {
            background: #dc3545;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="sync-dashboard">
        <!-- Header -->
        <div class="sync-header">
            <h1><i class="fas fa-sync-alt"></i> لوحة المزامنة اللحظية</h1>
            <p>مراقبة وإدارة المزامنة السحابية للبيانات</p>
        </div>

        <!-- Permissions Info -->
        <div class="permissions-info" id="permissionsInfo">
            <h4><i class="fas fa-user-shield"></i> معلومات الصلاحيات</h4>
            <p id="userInfo">جاري تحميل معلومات المستخدم...</p>
            <div id="permissionsList"></div>
        </div>

        <!-- Sync Status -->
        <div class="sync-status">
            <div class="status-card" id="connectionStatus">
                <h3><i class="fas fa-wifi"></i> حالة الاتصال</h3>
                <div id="connectionIndicator">جاري الفحص...</div>
            </div>
            <div class="status-card" id="firebaseStatus">
                <h3><i class="fas fa-database"></i> Firebase</h3>
                <div id="firebaseIndicator">جاري الفحص...</div>
            </div>
            <div class="status-card" id="syncStatus">
                <h3><i class="fas fa-sync"></i> المزامنة</h3>
                <div id="syncIndicator">جاري الفحص...</div>
            </div>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-value" id="totalSyncs">0</div>
                <div class="stat-label">إجمالي المزامنات</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="successfulSyncs">0</div>
                <div class="stat-label">مزامنات ناجحة</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="failedSyncs">0</div>
                <div class="stat-label">مزامنات فاشلة</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="lastSyncTime">-</div>
                <div class="stat-label">آخر مزامنة</div>
            </div>
        </div>

        <!-- Data Types -->
        <div class="data-types" id="dataTypes">
            <!-- Will be populated by JavaScript -->
        </div>

        <!-- Global Controls -->
        <div class="sync-controls" style="justify-content: center; margin: 20px 0;">
            <button class="btn success" onclick="startRealtimeSync()">
                <i class="fas fa-play"></i> تشغيل المزامنة اللحظية
            </button>
            <button class="btn warning" onclick="pauseRealtimeSync()">
                <i class="fas fa-pause"></i> إيقاف مؤقت
            </button>
            <button class="btn danger" onclick="stopRealtimeSync()">
                <i class="fas fa-stop"></i> إيقاف المزامنة
            </button>
            <button class="btn primary" onclick="forceFullSync()">
                <i class="fas fa-sync-alt"></i> مزامنة شاملة
            </button>
            <button class="btn" onclick="clearSyncLog()">
                <i class="fas fa-trash"></i> مسح السجل
            </button>
        </div>

        <!-- Sync Log -->
        <div class="sync-log" id="syncLog">
            <div class="log-entry info">[INFO] مرحباً بك في لوحة المزامنة اللحظية</div>
            <div class="log-entry info">[INFO] جاري تهيئة النظام...</div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>
    <script src="firebase-config.js"></script>
    <script src="permissions-system.js"></script>
    <script src="realtime-sync-system.js"></script>
    
    <script>
        // Dashboard state
        let syncStats = {
            total: 0,
            successful: 0,
            failed: 0,
            lastSync: null
        };

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 تهيئة لوحة المزامنة اللحظية...');
            
            setTimeout(() => {
                initializeDashboard();
            }, 2000);
        });

        // Initialize dashboard
        function initializeDashboard() {
            loadUserPermissions();
            checkSystemStatus();
            setupDataTypeCards();
            startStatusMonitoring();
            
            addToLog('✅ تم تهيئة لوحة المزامنة اللحظية', 'success');
        }

        // Load user permissions
        function loadUserPermissions() {
            try {
                if (window.permissionsSystem) {
                    const status = window.permissionsSystem.getPermissionStatus();
                    
                    document.getElementById('userInfo').innerHTML = `
                        <strong>المستخدم:</strong> ${status.user} | 
                        <strong>الدور:</strong> ${status.role} | 
                        <strong>الصلاحيات:</strong> ${status.permissionsCount}
                    `;
                    
                    const permissionsList = window.permissionsSystem.getUserPermissionsList();
                    const permissionsHtml = permissionsList.map(p => 
                        `<span class="badge">${p.name}</span>`
                    ).join(' ');
                    
                    document.getElementById('permissionsList').innerHTML = permissionsHtml;
                }
            } catch (error) {
                console.error('❌ خطأ في تحميل الصلاحيات:', error);
            }
        }

        // Check system status
        function checkSystemStatus() {
            // Connection status
            updateConnectionStatus();
            
            // Firebase status
            updateFirebaseStatus();
            
            // Sync status
            updateSyncStatus();
        }

        // Update connection status
        function updateConnectionStatus() {
            const isOnline = navigator.onLine;
            const statusCard = document.getElementById('connectionStatus');
            const indicator = document.getElementById('connectionIndicator');
            
            if (isOnline) {
                statusCard.className = 'status-card online';
                indicator.innerHTML = '<i class="fas fa-check-circle"></i> متصل';
            } else {
                statusCard.className = 'status-card offline';
                indicator.innerHTML = '<i class="fas fa-times-circle"></i> غير متصل';
            }
        }

        // Update Firebase status
        function updateFirebaseStatus() {
            const statusCard = document.getElementById('firebaseStatus');
            const indicator = document.getElementById('firebaseIndicator');
            
            if (window.firebaseService && window.firebaseService.isFirebaseReady()) {
                statusCard.className = 'status-card online';
                indicator.innerHTML = '<i class="fas fa-check-circle"></i> متصل';
            } else {
                statusCard.className = 'status-card offline';
                indicator.innerHTML = '<i class="fas fa-times-circle"></i> غير متصل';
            }
        }

        // Update sync status
        function updateSyncStatus() {
            const statusCard = document.getElementById('syncStatus');
            const indicator = document.getElementById('syncIndicator');
            
            if (window.realtimeSyncSystem && window.realtimeSyncSystem.isInitialized) {
                statusCard.className = 'status-card online';
                indicator.innerHTML = '<i class="fas fa-sync-alt"></i> نشط';
            } else {
                statusCard.className = 'status-card offline';
                indicator.innerHTML = '<i class="fas fa-pause-circle"></i> متوقف';
            }
        }

        // Add to log
        function addToLog(message, type = 'info') {
            const log = document.getElementById('syncLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.innerHTML = `[${timestamp}] ${message}`;
            
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        // Clear sync log
        function clearSyncLog() {
            document.getElementById('syncLog').innerHTML = '';
            addToLog('تم مسح السجل', 'info');
        }

        // Start realtime sync
        function startRealtimeSync() {
            addToLog('🚀 بدء المزامنة اللحظية...', 'info');
            
            if (window.realtimeSyncSystem) {
                window.realtimeSyncSystem.syncEnabled = true;
                addToLog('✅ تم تشغيل المزامنة اللحظية', 'success');
            } else {
                addToLog('❌ نظام المزامنة غير متاح', 'error');
            }
        }

        // Pause realtime sync
        function pauseRealtimeSync() {
            addToLog('⏸️ إيقاف المزامنة مؤقتاً...', 'warning');
            
            if (window.realtimeSyncSystem) {
                window.realtimeSyncSystem.syncEnabled = false;
                addToLog('⏸️ تم إيقاف المزامنة مؤقتاً', 'warning');
            }
        }

        // Stop realtime sync
        function stopRealtimeSync() {
            addToLog('🛑 إيقاف المزامنة...', 'warning');
            
            if (window.realtimeSyncSystem) {
                window.realtimeSyncSystem.syncEnabled = false;
                // Stop all listeners
                window.realtimeSyncSystem.listeners.forEach(unsubscribe => {
                    if (typeof unsubscribe === 'function') {
                        unsubscribe();
                    }
                });
                addToLog('🛑 تم إيقاف المزامنة', 'warning');
            }
        }

        // Force full sync
        async function forceFullSync() {
            addToLog('🔄 بدء المزامنة الشاملة...', 'info');
            
            try {
                if (window.realtimeSyncSystem) {
                    await window.realtimeSyncSystem.performPeriodicSync();
                    addToLog('✅ تمت المزامنة الشاملة بنجاح', 'success');
                    updateStats();
                } else {
                    addToLog('❌ نظام المزامنة غير متاح', 'error');
                }
            } catch (error) {
                addToLog(`❌ فشل في المزامنة الشاملة: ${error.message}`, 'error');
            }
        }

        // Update statistics
        function updateStats() {
            syncStats.total++;
            syncStats.successful++;
            syncStats.lastSync = new Date().toLocaleTimeString('ar-SA');
            
            document.getElementById('totalSyncs').textContent = syncStats.total;
            document.getElementById('successfulSyncs').textContent = syncStats.successful;
            document.getElementById('failedSyncs').textContent = syncStats.failed;
            document.getElementById('lastSyncTime').textContent = syncStats.lastSync;
        }

        // Start status monitoring
        function startStatusMonitoring() {
            setInterval(() => {
                checkSystemStatus();
            }, 5000); // Check every 5 seconds
        }

        // Setup data type cards
        function setupDataTypeCards() {
            const dataTypesContainer = document.getElementById('dataTypes');
            const dataTypes = [
                { key: 'products', name: 'المنتجات', icon: 'fas fa-box', permission: 'products_read' },
                { key: 'customers', name: 'العملاء', icon: 'fas fa-users', permission: 'customers_read' },
                { key: 'users', name: 'المستخدمين', icon: 'fas fa-user-friends', permission: 'users_read' },
                { key: 'settings', name: 'الإعدادات', icon: 'fas fa-cog', permission: 'settings_read' }
            ];
            
            dataTypes.forEach(dataType => {
                if (!window.hasPermission || window.hasPermission(dataType.permission)) {
                    const card = createDataTypeCard(dataType);
                    dataTypesContainer.appendChild(card);
                }
            });
        }

        // Create data type card
        function createDataTypeCard(dataType) {
            const card = document.createElement('div');
            card.className = 'data-type-card';
            card.innerHTML = `
                <div class="data-type-header">
                    <h4><i class="${dataType.icon}"></i> ${dataType.name}</h4>
                    <span class="sync-indicator active" id="${dataType.key}Indicator"></span>
                </div>
                <div class="sync-controls">
                    <button class="btn primary" onclick="syncDataType('${dataType.key}')">
                        <i class="fas fa-sync"></i> مزامنة
                    </button>
                    <button class="btn" onclick="viewDataType('${dataType.key}')">
                        <i class="fas fa-eye"></i> عرض
                    </button>
                </div>
            `;
            return card;
        }

        // Sync specific data type
        async function syncDataType(dataType) {
            addToLog(`🔄 مزامنة ${dataType}...`, 'info');
            
            try {
                if (window.realtimeSyncSystem) {
                    await window.realtimeSyncSystem.syncDataType(dataType);
                    addToLog(`✅ تمت مزامنة ${dataType} بنجاح`, 'success');
                } else {
                    addToLog(`❌ فشل في مزامنة ${dataType}`, 'error');
                }
            } catch (error) {
                addToLog(`❌ خطأ في مزامنة ${dataType}: ${error.message}`, 'error');
            }
        }

        // View data type
        function viewDataType(dataType) {
            addToLog(`👁️ عرض بيانات ${dataType}`, 'info');
            // Implementation for viewing data
        }

        // Network status listeners
        window.addEventListener('online', () => {
            addToLog('🌐 تم استعادة الاتصال', 'success');
            updateConnectionStatus();
        });

        window.addEventListener('offline', () => {
            addToLog('📴 انقطع الاتصال', 'warning');
            updateConnectionStatus();
        });
    </script>
</body>
</html>
