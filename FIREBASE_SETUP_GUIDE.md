# 🔥 دليل إعداد Firebase للنسور الماسية

## 📋 الخطوات المطلوبة:

### 1. إنشاء مشروع Firebase
1. اذهب إلى [Firebase Console](https://console.firebase.google.com)
2. اضغط "إنشاء مشروع" أو "Create a project"
3. أدخل اسم المشروع: `diamond-eagles-store`
4. اختر إعدادات Google Analytics (اختياري)
5. اضغط "إنشاء المشروع"

### 2. إعداد Firestore Database
1. في لوحة تحكم Firebase، اذهب إلى "Firestore Database"
2. اضغط "إنشاء قاعدة بيانات" أو "Create database"
3. اختر "Start in test mode" (للبداية)
4. اختر موقع الخادم (اختر الأقرب لك)
5. اضغط "تم" أو "Done"

### 3. إعداد تطبيق الويب
1. في لوحة تحكم Firebase، اضغط على أيقونة الويب `</>`
2. أدخل اسم التطبيق: `Diamond Eagles Store`
3. اختر "إعداد Firebase Hosting" (اختياري)
4. اضغط "تسجيل التطبيق"

### 4. نسخ إعدادات Firebase
ستظهر لك إعدادات مثل هذه:
```javascript
const firebaseConfig = {
  apiKey: "AIzaSyC...",
  authDomain: "diamond-eagles-store.firebaseapp.com",
  projectId: "diamond-eagles-store",
  storageBucket: "diamond-eagles-store.appspot.com",
  messagingSenderId: "123456789",
  appId: "1:123456789:web:abc123"
};
```

### 5. تحديث ملف firebase-config.js
1. افتح ملف `firebase-config.js`
2. استبدل الإعدادات الموجودة بإعداداتك:

```javascript
const firebaseConfig = {
    apiKey: "YOUR_API_KEY_HERE",           // ضع apiKey هنا
    authDomain: "YOUR_PROJECT_ID.firebaseapp.com",
    projectId: "YOUR_PROJECT_ID",          // ضع projectId هنا
    storageBucket: "YOUR_PROJECT_ID.appspot.com",
    messagingSenderId: "YOUR_SENDER_ID",   // ضع messagingSenderId هنا
    appId: "YOUR_APP_ID"                   // ضع appId هنا
};
```

### 6. إعداد قواعد الأمان (اختياري)
في Firestore Database > Rules، يمكنك استخدام هذه القواعد للبداية:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // السماح بالقراءة والكتابة للجميع (للاختبار فقط)
    match /{document=**} {
      allow read, write: if true;
    }
  }
}
```

⚠️ **تحذير:** هذه القواعد تسمح للجميع بالوصول. في الإنتاج، استخدم قواعد أكثر أماناً.

## 🧪 اختبار الإعداد:

### 1. تحديث الملفات
- تأكد من حفظ `firebase-config.js` بالإعدادات الصحيحة
- تأكد من تحميل الصفحة بدون أخطاء

### 2. اختبار الاتصال
1. افتح التطبيق
2. اذهب إلى الإعدادات
3. اضغط "فحص حالة Firebase"
4. يجب أن تظهر رسالة "Firebase محمل: نعم"

### 3. اختبار المزامنة
1. أضف منتج أو عميل جديد
2. اضغط "رفع البيانات للسحابة"
3. تحقق من Firebase Console > Firestore Database
4. يجب أن ترى البيانات في المجموعات (collections)

## 🔧 استكشاف الأخطاء:

### خطأ: "Firebase غير محمل"
- تأكد من الاتصال بالإنترنت
- تأكد من صحة إعدادات Firebase
- تحقق من وحدة التحكم (F12) للأخطاء

### خطأ: "Permission denied"
- تحقق من قواعد Firestore
- تأكد من أن القواعد تسمح بالقراءة والكتابة

### خطأ: "Project not found"
- تأكد من صحة projectId في الإعدادات
- تأكد من أن المشروع موجود في Firebase Console

## 🎯 النتيجة المتوقعة:

بعد الإعداد الصحيح:
- ✅ مزامنة تلقائية فورية بين جميع الأجهزة
- ✅ حفظ البيانات في السحابة
- ✅ وصول للبيانات من أي مكان
- ✅ نسخ احتياطية تلقائية

## 📞 الدعم:
إذا واجهت مشاكل، تحقق من:
- [Firebase Documentation](https://firebase.google.com/docs)
- وحدة التحكم في المتصفح (F12)
- Firebase Console للأخطاء

---
**تم إعداد هذا الدليل لشركة النسور الماسية للتجارة**
