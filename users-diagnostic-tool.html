<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 أداة تشخيص المستخدمين - النسور الماسية</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .diagnostic-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
        }

        .diagnostic-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px;
        }

        .diagnostic-section {
            background: white;
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .status-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .status-card.success {
            border-left-color: #28a745;
            background: #d4edda;
        }

        .status-card.warning {
            border-left-color: #ffc107;
            background: #fff3cd;
        }

        .status-card.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }

        .users-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .users-table th,
        .users-table td {
            padding: 12px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
        }

        .users-table th {
            background: #f8f9fa;
            font-weight: bold;
        }

        .users-table tr:hover {
            background: #f5f5f5;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn.primary {
            background: #007bff;
            color: white;
        }

        .btn.success {
            background: #28a745;
            color: white;
        }

        .btn.warning {
            background: #ffc107;
            color: #212529;
        }

        .btn.danger {
            background: #dc3545;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .log-container {
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-entry.success {
            color: #28a745;
        }

        .log-entry.error {
            color: #dc3545;
        }

        .log-entry.warning {
            color: #ffc107;
        }

        .log-entry.info {
            color: #17a2b8;
        }

        .badge {
            display: inline-block;
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
            margin: 2px;
        }

        .badge.active {
            background: #28a745;
            color: white;
        }

        .badge.inactive {
            background: #dc3545;
            color: white;
        }

        .badge.admin {
            background: #6f42c1;
            color: white;
        }

        .badge.manager {
            background: #fd7e14;
            color: white;
        }

        .badge.employee {
            background: #20c997;
            color: white;
        }

        .badge.viewer {
            background: #6c757d;
            color: white;
        }
    </style>
</head>
<body>
    <div class="diagnostic-container">
        <!-- Header -->
        <div class="diagnostic-header">
            <h1><i class="fas fa-user-md"></i> أداة تشخيص المستخدمين</h1>
            <p>فحص وتشخيص مشاكل المستخدمين والمزامنة</p>
        </div>

        <!-- Status Overview -->
        <div class="diagnostic-section">
            <h3><i class="fas fa-chart-line"></i> نظرة عامة على الحالة</h3>
            <div class="status-grid" id="statusGrid">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="diagnostic-section">
            <h3><i class="fas fa-tools"></i> إجراءات سريعة</h3>
            <div style="text-align: center;">
                <button class="btn primary" onclick="runFullDiagnostic()">
                    <i class="fas fa-search"></i> فحص شامل
                </button>
                <button class="btn success" onclick="syncUsersFromFirebase()">
                    <i class="fas fa-download"></i> تحميل من Firebase
                </button>
                <button class="btn warning" onclick="syncUsersToFirebase()">
                    <i class="fas fa-upload"></i> رفع إلى Firebase
                </button>
                <button class="btn" onclick="createTestUser()">
                    <i class="fas fa-user-plus"></i> إنشاء مستخدم تجريبي
                </button>
                <button class="btn danger" onclick="clearLog()">
                    <i class="fas fa-trash"></i> مسح السجل
                </button>
            </div>
        </div>

        <!-- Local Users -->
        <div class="diagnostic-section">
            <h3><i class="fas fa-users"></i> المستخدمون المحليون</h3>
            <div id="localUsersContainer">
                <p>جاري تحميل المستخدمين المحليين...</p>
            </div>
        </div>

        <!-- Firebase Users -->
        <div class="diagnostic-section">
            <h3><i class="fas fa-cloud"></i> المستخدمون في Firebase</h3>
            <div id="firebaseUsersContainer">
                <p>جاري تحميل المستخدمين من Firebase...</p>
            </div>
        </div>

        <!-- Diagnostic Log -->
        <div class="diagnostic-section">
            <h3><i class="fas fa-file-alt"></i> سجل التشخيص</h3>
            <div class="log-container" id="diagnosticLog">
                <div class="log-entry info">[INFO] مرحباً بك في أداة تشخيص المستخدمين</div>
                <div class="log-entry info">[INFO] جاري تهيئة النظام...</div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>
    <script src="firebase-config.js"></script>
    <script src="user-management.js"></script>
    
    <script>
        // Initialize diagnostic tool
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 تهيئة أداة تشخيص المستخدمين...');
            
            setTimeout(() => {
                initializeDiagnostic();
            }, 2000);
        });

        // Initialize diagnostic
        async function initializeDiagnostic() {
            addToLog('🚀 بدء تشخيص المستخدمين...', 'info');
            
            try {
                // Check Firebase status
                await checkFirebaseStatus();
                
                // Load and display users
                await loadLocalUsers();
                await loadFirebaseUsers();
                
                // Update status overview
                updateStatusOverview();
                
                addToLog('✅ تم تهيئة أداة التشخيص بنجاح', 'success');
                
            } catch (error) {
                addToLog(`❌ خطأ في تهيئة أداة التشخيص: ${error.message}`, 'error');
            }
        }

        // Check Firebase status
        async function checkFirebaseStatus() {
            addToLog('🔍 فحص حالة Firebase...', 'info');
            
            if (window.firebaseService && window.firebaseService.isFirebaseReady()) {
                addToLog('✅ Firebase متصل ويعمل', 'success');
                return true;
            } else {
                addToLog('❌ Firebase غير متاح أو غير متصل', 'error');
                return false;
            }
        }

        // Load local users
        async function loadLocalUsers() {
            addToLog('📱 تحميل المستخدمين المحليين...', 'info');
            
            try {
                const localUsersData = localStorage.getItem('systemUsers');
                let localUsers = [];
                
                if (localUsersData) {
                    localUsers = JSON.parse(localUsersData);
                }
                
                addToLog(`📊 تم العثور على ${localUsers.length} مستخدم محلي`, 'info');
                displayUsers(localUsers, 'localUsersContainer', 'محلي');
                
                return localUsers;
                
            } catch (error) {
                addToLog(`❌ خطأ في تحميل المستخدمين المحليين: ${error.message}`, 'error');
                return [];
            }
        }

        // Load Firebase users
        async function loadFirebaseUsers() {
            addToLog('☁️ تحميل المستخدمين من Firebase...', 'info');
            
            try {
                if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
                    addToLog('⚠️ Firebase غير متاح', 'warning');
                    document.getElementById('firebaseUsersContainer').innerHTML = '<p class="text-warning">Firebase غير متاح</p>';
                    return [];
                }
                
                const firebaseUsers = await window.firebaseService.loadUsers();
                
                if (firebaseUsers && firebaseUsers.length > 0) {
                    addToLog(`📊 تم العثور على ${firebaseUsers.length} مستخدم في Firebase`, 'success');
                    displayUsers(firebaseUsers, 'firebaseUsersContainer', 'Firebase');
                    return firebaseUsers;
                } else {
                    addToLog('ℹ️ لا توجد مستخدمين في Firebase', 'info');
                    document.getElementById('firebaseUsersContainer').innerHTML = '<p class="text-info">لا توجد مستخدمين في Firebase</p>';
                    return [];
                }
                
            } catch (error) {
                addToLog(`❌ خطأ في تحميل المستخدمين من Firebase: ${error.message}`, 'error');
                document.getElementById('firebaseUsersContainer').innerHTML = `<p class="text-danger">خطأ: ${error.message}</p>`;
                return [];
            }
        }

        // Display users in table
        function displayUsers(users, containerId, source) {
            const container = document.getElementById(containerId);
            
            if (!users || users.length === 0) {
                container.innerHTML = `<p class="text-info">لا توجد مستخدمين في ${source}</p>`;
                return;
            }
            
            let html = `
                <p><strong>عدد المستخدمين:</strong> ${users.length}</p>
                <table class="users-table">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>البريد الإلكتروني</th>
                            <th>الدور</th>
                            <th>الحالة</th>
                            <th>تاريخ الإنشاء</th>
                            <th>آخر دخول</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            users.forEach(user => {
                const isActive = user.isActive ? 'نشط' : 'غير نشط';
                const badgeClass = user.isActive ? 'active' : 'inactive';
                const roleBadge = `<span class="badge ${user.role}">${user.role}</span>`;
                const createdDate = user.createdAt ? new Date(user.createdAt).toLocaleDateString('ar-SA') : '-';
                const lastLogin = user.lastLogin ? new Date(user.lastLogin).toLocaleDateString('ar-SA') : '-';
                
                html += `
                    <tr>
                        <td>${user.name}</td>
                        <td>${user.email}</td>
                        <td>${roleBadge}</td>
                        <td><span class="badge ${badgeClass}">${isActive}</span></td>
                        <td>${createdDate}</td>
                        <td>${lastLogin}</td>
                    </tr>
                `;
            });
            
            html += `
                    </tbody>
                </table>
            `;
            
            container.innerHTML = html;
        }

        // Update status overview
        function updateStatusOverview() {
            const statusGrid = document.getElementById('statusGrid');
            
            // Get current status
            const localUsers = JSON.parse(localStorage.getItem('systemUsers') || '[]');
            const isFirebaseReady = window.firebaseService && window.firebaseService.isFirebaseReady();
            const isUserManagerReady = typeof window.userManager !== 'undefined';
            
            const statusCards = [
                {
                    title: 'المستخدمون المحليون',
                    value: localUsers.length,
                    status: localUsers.length > 0 ? 'success' : 'warning',
                    icon: 'fas fa-users'
                },
                {
                    title: 'حالة Firebase',
                    value: isFirebaseReady ? 'متصل' : 'غير متصل',
                    status: isFirebaseReady ? 'success' : 'error',
                    icon: 'fas fa-cloud'
                },
                {
                    title: 'نظام إدارة المستخدمين',
                    value: isUserManagerReady ? 'جاهز' : 'غير جاهز',
                    status: isUserManagerReady ? 'success' : 'error',
                    icon: 'fas fa-cog'
                },
                {
                    title: 'المستخدمون النشطون',
                    value: localUsers.filter(u => u.isActive).length,
                    status: localUsers.filter(u => u.isActive).length > 0 ? 'success' : 'warning',
                    icon: 'fas fa-user-check'
                }
            ];
            
            let html = '';
            statusCards.forEach(card => {
                html += `
                    <div class="status-card ${card.status}">
                        <h4><i class="${card.icon}"></i> ${card.title}</h4>
                        <p><strong>${card.value}</strong></p>
                    </div>
                `;
            });
            
            statusGrid.innerHTML = html;
        }

        // Run full diagnostic
        async function runFullDiagnostic() {
            addToLog('🔍 بدء الفحص الشامل...', 'info');
            
            try {
                // Clear previous results
                clearLog();
                addToLog('🚀 بدء الفحص الشامل للمستخدمين...', 'info');
                
                // Check all components
                await checkFirebaseStatus();
                const localUsers = await loadLocalUsers();
                const firebaseUsers = await loadFirebaseUsers();
                
                // Compare users
                compareUsers(localUsers, firebaseUsers);
                
                // Update status
                updateStatusOverview();
                
                addToLog('✅ تم الانتهاء من الفحص الشامل', 'success');
                
            } catch (error) {
                addToLog(`❌ خطأ في الفحص الشامل: ${error.message}`, 'error');
            }
        }

        // Compare users between local and Firebase
        function compareUsers(localUsers, firebaseUsers) {
            addToLog('🔄 مقارنة المستخدمين بين المحلي و Firebase...', 'info');
            
            addToLog(`📊 المحلي: ${localUsers.length} مستخدم`, 'info');
            addToLog(`📊 Firebase: ${firebaseUsers.length} مستخدم`, 'info');
            
            if (localUsers.length === firebaseUsers.length) {
                addToLog('✅ عدد المستخدمين متطابق', 'success');
            } else {
                addToLog('⚠️ عدد المستخدمين مختلف - يحتاج مزامنة', 'warning');
            }
            
            // Check for missing users
            const localEmails = localUsers.map(u => u.email);
            const firebaseEmails = firebaseUsers.map(u => u.email);
            
            const missingInFirebase = localUsers.filter(u => !firebaseEmails.includes(u.email));
            const missingInLocal = firebaseUsers.filter(u => !localEmails.includes(u.email));
            
            if (missingInFirebase.length > 0) {
                addToLog(`⚠️ ${missingInFirebase.length} مستخدم موجود محلياً لكن غير موجود في Firebase`, 'warning');
                missingInFirebase.forEach(u => {
                    addToLog(`  - ${u.name} (${u.email})`, 'warning');
                });
            }
            
            if (missingInLocal.length > 0) {
                addToLog(`⚠️ ${missingInLocal.length} مستخدم موجود في Firebase لكن غير موجود محلياً`, 'warning');
                missingInLocal.forEach(u => {
                    addToLog(`  - ${u.name} (${u.email})`, 'warning');
                });
            }
            
            if (missingInFirebase.length === 0 && missingInLocal.length === 0) {
                addToLog('✅ جميع المستخدمين متطابقون', 'success');
            }
        }

        // Sync users from Firebase
        async function syncUsersFromFirebase() {
            addToLog('📥 تحميل المستخدمين من Firebase...', 'info');
            
            try {
                if (window.userManager && typeof window.userManager.loadUsersFromFirebase === 'function') {
                    const result = await window.userManager.loadUsersFromFirebase();
                    if (result) {
                        addToLog('✅ تم تحميل المستخدمين من Firebase بنجاح', 'success');
                        await loadLocalUsers(); // Refresh display
                        updateStatusOverview();
                    } else {
                        addToLog('⚠️ لا توجد مستخدمين في Firebase', 'warning');
                    }
                } else {
                    addToLog('❌ وظيفة تحميل المستخدمين غير متاحة', 'error');
                }
            } catch (error) {
                addToLog(`❌ خطأ في تحميل المستخدمين: ${error.message}`, 'error');
            }
        }

        // Sync users to Firebase
        async function syncUsersToFirebase() {
            addToLog('📤 رفع المستخدمين إلى Firebase...', 'info');
            
            try {
                if (window.userManager && typeof window.userManager.syncUsersToFirebase === 'function') {
                    const result = await window.userManager.syncUsersToFirebase();
                    if (result) {
                        addToLog('✅ تم رفع المستخدمين إلى Firebase بنجاح', 'success');
                        await loadFirebaseUsers(); // Refresh display
                        updateStatusOverview();
                    } else {
                        addToLog('❌ فشل في رفع المستخدمين إلى Firebase', 'error');
                    }
                } else {
                    addToLog('❌ وظيفة رفع المستخدمين غير متاحة', 'error');
                }
            } catch (error) {
                addToLog(`❌ خطأ في رفع المستخدمين: ${error.message}`, 'error');
            }
        }

        // Create test user
        function createTestUser() {
            addToLog('👤 إنشاء مستخدم تجريبي...', 'info');
            
            try {
                if (window.userManager && typeof window.userManager.addUser === 'function') {
                    const testUser = {
                        name: 'مستخدم تجريبي',
                        email: `test${Date.now()}@example.com`,
                        password: 'test123',
                        role: 'employee'
                    };
                    
                    const result = window.userManager.addUser(testUser);
                    if (result.success) {
                        addToLog(`✅ تم إنشاء المستخدم التجريبي: ${testUser.email}`, 'success');
                        loadLocalUsers(); // Refresh display
                        updateStatusOverview();
                    } else {
                        addToLog(`❌ فشل في إنشاء المستخدم التجريبي: ${result.error}`, 'error');
                    }
                } else {
                    addToLog('❌ وظيفة إضافة المستخدمين غير متاحة', 'error');
                }
            } catch (error) {
                addToLog(`❌ خطأ في إنشاء المستخدم التجريبي: ${error.message}`, 'error');
            }
        }

        // Add to log
        function addToLog(message, type = 'info') {
            const log = document.getElementById('diagnosticLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.innerHTML = `[${timestamp}] ${message}`;
            
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        // Clear log
        function clearLog() {
            document.getElementById('diagnosticLog').innerHTML = '';
            addToLog('تم مسح السجل', 'info');
        }
    </script>
</body>
</html>
