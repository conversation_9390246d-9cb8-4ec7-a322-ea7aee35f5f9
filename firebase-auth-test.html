<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔥 اختبار Firebase Auth - النسور الماسية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            border-radius: 10px;
        }

        .test-section {
            margin-bottom: 20px;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .btn.primary { background: #007bff; color: white; }
        .btn.success { background: #28a745; color: white; }
        .btn.warning { background: #ffc107; color: #212529; }
        .btn.danger { background: #dc3545; color: white; }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
        }

        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }

        .log {
            background: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .config-display {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            border: 1px solid #dee2e6;
            margin: 10px 0;
        }

        .test-email {
            width: 100%;
            padding: 10px;
            border: 2px solid #e1e5e9;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔥 اختبار Firebase Auth</h1>
            <p>تشخيص وإصلاح مشاكل Firebase Authentication</p>
        </div>

        <!-- Firebase Status -->
        <div class="test-section">
            <h3>📊 حالة Firebase</h3>
            <div id="firebaseStatus"></div>
            <button class="btn primary" onclick="checkFirebaseStatus()">
                🔍 فحص حالة Firebase
            </button>
            <button class="btn warning" onclick="reinitializeFirebase()">
                🔄 إعادة تهيئة Firebase
            </button>
        </div>

        <!-- Configuration Display -->
        <div class="test-section">
            <h3>⚙️ إعدادات Firebase</h3>
            <div id="configDisplay" class="config-display">جاري التحميل...</div>
            <button class="btn success" onclick="showFirebaseConfig()">
                📋 عرض الإعدادات
            </button>
        </div>

        <!-- Auth Tests -->
        <div class="test-section">
            <h3>🔐 اختبار المصادقة</h3>
            <input type="email" id="testEmail" class="test-email" placeholder="أدخل بريد إلكتروني للاختبار" value="<EMAIL>">
            <br>
            <button class="btn primary" onclick="testPasswordReset()">
                📧 اختبار إرسال بريد استرداد كلمة المرور
            </button>
            <button class="btn success" onclick="testAuthMethods()">
                🧪 اختبار جميع وظائف المصادقة
            </button>
        </div>

        <!-- Network Tests -->
        <div class="test-section">
            <h3>🌐 اختبار الشبكة</h3>
            <button class="btn warning" onclick="testNetworkConnection()">
                📡 اختبار الاتصال بالإنترنت
            </button>
            <button class="btn primary" onclick="testFirebaseConnection()">
                🔥 اختبار الاتصال بـ Firebase
            </button>
        </div>

        <!-- Test Log -->
        <div class="test-section">
            <h3>📝 سجل الاختبار</h3>
            <button class="btn danger" onclick="clearLog()">🗑️ مسح السجل</button>
            <div class="log" id="testLog">
                <div>[INFO] مرحباً بك في أداة اختبار Firebase Auth</div>
            </div>
        </div>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBl6gHBz8N5QJ5K5K5K5K5K5K5K5K5K5K5",
            authDomain: "golden-eagles-inventory.firebaseapp.com",
            projectId: "golden-eagles-inventory",
            storageBucket: "golden-eagles-inventory.appspot.com",
            messagingSenderId: "123456789012",
            appId: "1:123456789012:web:abcdef123456789012345678"
        };

        // Initialize on load
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 بدء اختبار Firebase Auth...', 'info');
            setTimeout(() => {
                initializeFirebaseTest();
            }, 1000);
        });

        // Initialize Firebase test
        function initializeFirebaseTest() {
            try {
                if (!firebase.apps.length) {
                    firebase.initializeApp(firebaseConfig);
                    log('✅ تم تهيئة Firebase', 'success');
                } else {
                    log('ℹ️ Firebase مُهيأ مسبقاً', 'info');
                }

                if (firebase.auth) {
                    firebase.auth().languageCode = 'ar';
                    log('✅ تم تعيين اللغة العربية', 'success');
                }

                checkFirebaseStatus();
                showFirebaseConfig();
                
            } catch (error) {
                log(`❌ خطأ في تهيئة Firebase: ${error.message}`, 'error');
            }
        }

        // Check Firebase status
        function checkFirebaseStatus() {
            log('🔍 فحص حالة Firebase...', 'info');
            
            const statusDiv = document.getElementById('firebaseStatus');
            let statusHtml = '';

            // Check Firebase app
            if (typeof firebase !== 'undefined') {
                statusHtml += '<div class="status success">✅ Firebase SDK محمل</div>';
                log('✅ Firebase SDK متاح', 'success');
            } else {
                statusHtml += '<div class="status error">❌ Firebase SDK غير محمل</div>';
                log('❌ Firebase SDK غير متاح', 'error');
                statusDiv.innerHTML = statusHtml;
                return;
            }

            // Check Firebase app initialization
            if (firebase.apps.length > 0) {
                statusHtml += '<div class="status success">✅ Firebase App مُهيأ</div>';
                log('✅ Firebase App مُهيأ', 'success');
            } else {
                statusHtml += '<div class="status error">❌ Firebase App غير مُهيأ</div>';
                log('❌ Firebase App غير مُهيأ', 'error');
            }

            // Check Firebase Auth
            if (firebase.auth) {
                statusHtml += '<div class="status success">✅ Firebase Auth متاح</div>';
                log('✅ Firebase Auth متاح', 'success');

                // Check Auth instance
                try {
                    const auth = firebase.auth();
                    if (auth) {
                        statusHtml += '<div class="status success">✅ Firebase Auth Instance جاهز</div>';
                        log('✅ Firebase Auth Instance جاهز', 'success');
                    }
                } catch (error) {
                    statusHtml += '<div class="status error">❌ خطأ في Firebase Auth Instance</div>';
                    log(`❌ خطأ في Firebase Auth Instance: ${error.message}`, 'error');
                }
            } else {
                statusHtml += '<div class="status error">❌ Firebase Auth غير متاح</div>';
                log('❌ Firebase Auth غير متاح', 'error');
            }

            statusDiv.innerHTML = statusHtml;
        }

        // Show Firebase configuration
        function showFirebaseConfig() {
            const configDiv = document.getElementById('configDisplay');
            
            if (firebase.apps.length > 0) {
                const app = firebase.apps[0];
                const config = app.options;
                
                configDiv.innerHTML = `
                    <strong>Firebase Configuration:</strong><br>
                    Project ID: ${config.projectId || 'غير محدد'}<br>
                    Auth Domain: ${config.authDomain || 'غير محدد'}<br>
                    API Key: ${config.apiKey ? config.apiKey.substring(0, 10) + '...' : 'غير محدد'}<br>
                    App ID: ${config.appId ? config.appId.substring(0, 20) + '...' : 'غير محدد'}
                `;
                
                log('📋 تم عرض إعدادات Firebase', 'info');
            } else {
                configDiv.innerHTML = '<span style="color: red;">Firebase غير مُهيأ</span>';
                log('❌ لا يمكن عرض إعدادات Firebase - غير مُهيأ', 'error');
            }
        }

        // Test password reset
        async function testPasswordReset() {
            const email = document.getElementById('testEmail').value;
            
            if (!email) {
                log('⚠️ يرجى إدخال بريد إلكتروني للاختبار', 'warning');
                return;
            }

            log(`📧 اختبار إرسال بريد استرداد كلمة المرور إلى: ${email}`, 'info');

            try {
                if (!firebase.auth) {
                    throw new Error('Firebase Auth غير متاح');
                }

                const auth = firebase.auth();
                
                const actionCodeSettings = {
                    url: window.location.origin + '/reset-password.html',
                    handleCodeInApp: true
                };

                await auth.sendPasswordResetEmail(email, actionCodeSettings);
                
                log('✅ تم إرسال بريد استرداد كلمة المرور بنجاح', 'success');
                
            } catch (error) {
                log(`❌ خطأ في إرسال بريد استرداد كلمة المرور: ${error.message}`, 'error');
                log(`رمز الخطأ: ${error.code}`, 'error');
            }
        }

        // Test all auth methods
        function testAuthMethods() {
            log('🧪 اختبار جميع وظائف المصادقة...', 'info');

            // Test Firebase availability
            if (typeof firebase === 'undefined') {
                log('❌ Firebase غير متاح', 'error');
                return;
            }

            // Test Firebase Auth
            if (!firebase.auth) {
                log('❌ Firebase Auth غير متاح', 'error');
                return;
            }

            try {
                const auth = firebase.auth();
                log('✅ Firebase Auth Instance متاح', 'success');

                // Test language setting
                auth.languageCode = 'ar';
                log('✅ تم تعيين اللغة العربية', 'success');

                // Test auth state listener
                auth.onAuthStateChanged((user) => {
                    if (user) {
                        log(`👤 مستخدم مسجل دخول: ${user.email}`, 'info');
                    } else {
                        log('👤 لا يوجد مستخدم مسجل دخول', 'info');
                    }
                });

                log('✅ جميع وظائف المصادقة تعمل بشكل طبيعي', 'success');

            } catch (error) {
                log(`❌ خطأ في اختبار وظائف المصادقة: ${error.message}`, 'error');
            }
        }

        // Test network connection
        async function testNetworkConnection() {
            log('📡 اختبار الاتصال بالإنترنت...', 'info');

            try {
                const response = await fetch('https://www.google.com/favicon.ico', {
                    method: 'HEAD',
                    mode: 'no-cors'
                });
                
                log('✅ الاتصال بالإنترنت يعمل', 'success');
                
            } catch (error) {
                log('❌ لا يوجد اتصال بالإنترنت', 'error');
            }
        }

        // Test Firebase connection
        async function testFirebaseConnection() {
            log('🔥 اختبار الاتصال بـ Firebase...', 'info');

            try {
                if (!firebase.auth) {
                    throw new Error('Firebase Auth غير متاح');
                }

                const auth = firebase.auth();
                
                // Try to get current user (this tests the connection)
                const user = auth.currentUser;
                log('✅ الاتصال بـ Firebase يعمل', 'success');
                
                if (user) {
                    log(`👤 مستخدم حالي: ${user.email}`, 'info');
                } else {
                    log('👤 لا يوجد مستخدم مسجل دخول', 'info');
                }
                
            } catch (error) {
                log(`❌ خطأ في الاتصال بـ Firebase: ${error.message}`, 'error');
            }
        }

        // Reinitialize Firebase
        function reinitializeFirebase() {
            log('🔄 إعادة تهيئة Firebase...', 'info');

            try {
                // Delete existing apps
                if (firebase.apps.length > 0) {
                    firebase.apps.forEach(app => {
                        app.delete();
                    });
                    log('🗑️ تم حذف التطبيقات الموجودة', 'info');
                }

                // Reinitialize
                firebase.initializeApp(firebaseConfig);
                log('✅ تم إعادة تهيئة Firebase', 'success');

                // Set language
                if (firebase.auth) {
                    firebase.auth().languageCode = 'ar';
                    log('✅ تم تعيين اللغة العربية', 'success');
                }

                // Recheck status
                setTimeout(() => {
                    checkFirebaseStatus();
                }, 1000);

            } catch (error) {
                log(`❌ خطأ في إعادة تهيئة Firebase: ${error.message}`, 'error');
            }
        }

        // Log function
        function log(message, type = 'info') {
            const logContainer = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                info: '#00ff00',
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107'
            };
            
            const logEntry = document.createElement('div');
            logEntry.style.color = colors[type] || colors.info;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;

            // Also log to console
            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // Clear log
        function clearLog() {
            document.getElementById('testLog').innerHTML = '';
            log('تم مسح السجل', 'info');
        }
    </script>
</body>
</html>
