<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد المستخدم الافتراضي - النسور الماسية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .setup-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 100%;
            text-align: center;
        }
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        p {
            color: #666;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .credentials {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        .credentials h3 {
            margin-top: 0;
            color: #28a745;
        }
        .credential-item {
            margin: 10px 0;
            font-family: monospace;
            background: white;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="logo">
            <i class="fas fa-gem"></i>
        </div>
        
        <h1>إعداد المستخدم الافتراضي</h1>
        <p>هذه الصفحة تضمن إنشاء المستخدم الافتراضي في أي متصفح</p>
        
        <div id="status"></div>
        
        <div class="credentials">
            <h3>🔑 بيانات تسجيل الدخول الافتراضية</h3>
            <div class="credential-item">
                <strong>البريد الإلكتروني:</strong> <EMAIL>
            </div>
            <div class="credential-item">
                <strong>كلمة المرور:</strong> 2030
            </div>
        </div>
        
        <button onclick="setupDefaultUser()">إنشاء المستخدم الافتراضي</button>
        <button onclick="checkCurrentStatus()">فحص الحالة الحالية</button>
        <button onclick="clearAllData()">مسح جميع البيانات</button>
        
        <div style="margin-top: 30px;">
            <button onclick="goToLogin()" style="background: #28a745;">
                الانتقال إلى صفحة تسجيل الدخول
            </button>
        </div>
    </div>

    <script>
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.className = `status ${type}`;
            statusDiv.innerHTML = message;
        }

        function setupDefaultUser() {
            try {
                console.log('🔧 إعداد المستخدم الافتراضي...');
                
                // Clear any existing data
                localStorage.removeItem('systemUsers');
                localStorage.removeItem('currentUser');
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('loginTime');
                localStorage.removeItem('isBrowseMode');
                
                // Create comprehensive default admin
                const defaultAdmin = {
                    id: 'admin_' + Date.now(),
                    name: 'المدير الرئيسي',
                    email: '<EMAIL>',
                    password: '2030',
                    role: 'admin',
                    permissions: [
                        'view_dashboard',
                        'view_products',
                        'add_products',
                        'edit_products',
                        'delete_products',
                        'view_customers',
                        'add_customers',
                        'edit_customers',
                        'delete_customers',
                        'view_settings',
                        'manage_users'
                    ],
                    isActive: true,
                    createdAt: new Date().toISOString(),
                    lastLogin: null
                };
                
                // Save to localStorage
                localStorage.setItem('systemUsers', JSON.stringify([defaultAdmin]));
                
                // Verify save
                const savedData = localStorage.getItem('systemUsers');
                if (savedData) {
                    const parsedData = JSON.parse(savedData);
                    console.log('✅ تم حفظ المستخدم:', parsedData[0]);
                    
                    showStatus(`
                        ✅ تم إنشاء المستخدم الافتراضي بنجاح!<br><br>
                        <strong>البريد:</strong> ${defaultAdmin.email}<br>
                        <strong>كلمة المرور:</strong> ${defaultAdmin.password}<br><br>
                        يمكنك الآن تسجيل الدخول في أي متصفح بهذه البيانات.
                    `, 'success');
                } else {
                    throw new Error('فشل في حفظ البيانات');
                }
                
            } catch (error) {
                console.error('❌ خطأ في إعداد المستخدم:', error);
                showStatus(`❌ فشل في إعداد المستخدم: ${error.message}`, 'error');
            }
        }

        function checkCurrentStatus() {
            try {
                const systemUsersData = localStorage.getItem('systemUsers');
                const currentUserData = localStorage.getItem('currentUser');
                const isLoggedIn = localStorage.getItem('isLoggedIn');
                
                let statusMessage = '<strong>📊 حالة النظام الحالية:</strong><br><br>';
                
                if (systemUsersData) {
                    const users = JSON.parse(systemUsersData);
                    statusMessage += `👥 عدد المستخدمين: ${users.length}<br>`;
                    
                    users.forEach((user, index) => {
                        statusMessage += `${index + 1}. ${user.name} - ${user.email} - نشط: ${user.isActive}<br>`;
                    });
                } else {
                    statusMessage += '⚠️ لا توجد بيانات مستخدمين<br>';
                }
                
                statusMessage += `<br>🔐 حالة تسجيل الدخول: ${isLoggedIn === 'true' ? 'مسجل' : 'غير مسجل'}<br>`;
                
                if (currentUserData && currentUserData !== 'زائر - وضع التصفح') {
                    try {
                        const currentUser = JSON.parse(currentUserData);
                        statusMessage += `👤 المستخدم الحالي: ${currentUser.name}<br>`;
                    } catch (e) {
                        statusMessage += `👤 المستخدم الحالي: ${currentUserData}<br>`;
                    }
                }
                
                showStatus(statusMessage, 'info');
                
            } catch (error) {
                showStatus(`❌ خطأ في فحص الحالة: ${error.message}`, 'error');
            }
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟\nسيتم حذف جميع المستخدمين وبيانات تسجيل الدخول.')) {
                localStorage.clear();
                showStatus('🗑️ تم مسح جميع البيانات بنجاح', 'success');
            }
        }

        function goToLogin() {
            window.location.href = 'login-system.html';
        }

        // Auto-check status on load
        window.addEventListener('load', function() {
            setTimeout(() => {
                checkCurrentStatus();
            }, 500);
        });
    </script>
</body>
</html>
