/**
 * 🎯 منسق المزامنة المركزي
 * يمنع تداخل عمليات المزامنة ويحل مشكلة الإشعارات المتكررة
 */

class SyncCoordinator {
    constructor() {
        this.isInitialized = false;
        this.activeSyncs = new Set();
        this.syncQueue = [];
        this.isProcessingQueue = false;
        
        // Sync settings
        this.syncEnabled = true;
        this.maxConcurrentSyncs = 1; // Only one sync at a time
        this.syncCooldown = 10000; // 10 seconds between syncs
        this.lastSyncTime = 0;
        
        // Sync types tracking
        this.syncTypes = {
            MANUAL: 'manual',
            AUTO: 'auto', 
            REALTIME: 'realtime',
            FOCUS: 'focus',
            VISIBILITY: 'visibility',
            PERIODIC: 'periodic',
            LOGIN: 'login'
        };
        
        this.lastSyncByType = new Map();
        this.syncIntervals = new Map();
        this.syncTimeouts = new Map();
        
        // Notification control
        this.notificationsEnabled = true;
        this.lastNotificationTime = 0;
        this.notificationCooldown = 15000; // 15 seconds between notifications
        
        console.log('🎯 تم تهيئة منسق المزامنة المركزي');
        this.init();
    }
    
    init() {
        // Override existing sync functions
        this.overrideSyncFunctions();
        
        // Clear existing intervals
        this.clearExistingIntervals();
        
        // Setup controlled sync
        this.setupControlledSync();
        
        // Setup event listeners
        this.setupEventListeners();
        
        this.isInitialized = true;
        console.log('✅ تم تهيئة منسق المزامنة بنجاح');
    }
    
    // Override existing sync functions to prevent conflicts
    overrideSyncFunctions() {
        // Store original functions
        if (window.quickSyncProducts) {
            this.originalQuickSync = window.quickSyncProducts;
            window.quickSyncProducts = (...args) => this.coordinatedSync(this.syncTypes.MANUAL, ...args);
        }
        
        if (window.autoSyncOnAppStart) {
            this.originalAutoSync = window.autoSyncOnAppStart;
            window.autoSyncOnAppStart = (...args) => this.coordinatedSync(this.syncTypes.AUTO, ...args);
        }
        
        console.log('🔧 تم تطبيق التحكم المركزي على دوال المزامنة');
    }
    
    // Clear existing intervals that cause conflicts
    clearExistingIntervals() {
        // Clear any existing sync intervals
        for (let i = 1; i < 99999; i++) {
            clearInterval(i);
        }
        
        // Clear any existing sync timeouts
        for (let i = 1; i < 99999; i++) {
            clearTimeout(i);
        }
        
        console.log('🧹 تم مسح جميع المؤقتات المتداخلة');
    }
    
    // Setup controlled sync system - PAGE LOAD ONLY
    setupControlledSync() {
        console.log('📄 المزامنة السحابية ستحدث فقط عند تحديث الصفحة');

        // No periodic sync - only on page load
        // All automatic sync intervals are disabled

        console.log('✅ تم تعطيل جميع المزامنات التلقائية - المزامنة فقط عند تحديث الصفحة');
    }
    
    // Setup event listeners - DISABLED (sync only on page load)
    setupEventListeners() {
        console.log('🚫 تم تعطيل جميع مستمعي أحداث المزامنة');
        console.log('📄 المزامنة ستحدث فقط عند تحديث الصفحة');

        // All event listeners are disabled
        // No focus sync, no visibility change sync
        // Only manual sync and page load sync are allowed
    }
    
    // Coordinated sync function - PAGE LOAD AND MANUAL ONLY
    async coordinatedSync(syncType, ...args) {
        const now = Date.now();
        const syncKey = `sync_${syncType}`;

        // Check if sync is enabled
        if (!this.syncEnabled) {
            console.log(`⏸️ المزامنة معطلة - تجاهل ${syncType}`);
            return false;
        }

        // Only allow manual sync and page load sync
        const allowedTypes = [this.syncTypes.MANUAL, this.syncTypes.LOGIN];
        if (!allowedTypes.includes(syncType)) {
            console.log(`🚫 نوع المزامنة ${syncType} غير مسموح - المزامنة فقط عند تحديث الصفحة أو يدوياً`);
            return false;
        }
        
        // Check global cooldown
        if (now - this.lastSyncTime < this.syncCooldown) {
            console.log(`⏸️ فترة انتظار المزامنة - تجاهل ${syncType} (${Math.round((this.syncCooldown - (now - this.lastSyncTime))/1000)}s متبقية)`);
            return false;
        }
        
        // Check type-specific cooldown
        const lastSyncOfType = this.lastSyncByType.get(syncType) || 0;
        const typeCooldown = this.getTypeCooldown(syncType);
        if (now - lastSyncOfType < typeCooldown) {
            console.log(`⏸️ فترة انتظار ${syncType} - تجاهل (${Math.round((typeCooldown - (now - lastSyncOfType))/1000)}s متبقية)`);
            return false;
        }
        
        // Check if already syncing
        if (this.activeSyncs.size >= this.maxConcurrentSyncs) {
            console.log(`⏸️ مزامنة قيد التنفيذ - إضافة ${syncType} للطابور`);
            this.addToQueue(syncType, args);
            return false;
        }
        
        // Check UI stability
        if (window.uiStabilityManager) {
            const status = window.uiStabilityManager.getStatus();
            if (status.pendingUpdates > 2) {
                console.log(`⏸️ الواجهة غير مستقرة - تأجيل ${syncType}`);
                this.addToQueue(syncType, args);
                return false;
            }
        }
        
        // Execute sync
        return await this.executeSync(syncType, args);
    }
    
    // Get cooldown for sync type
    getTypeCooldown(syncType) {
        const cooldowns = {
            [this.syncTypes.MANUAL]: 5000,      // 5 seconds
            [this.syncTypes.AUTO]: 30000,       // 30 seconds
            [this.syncTypes.REALTIME]: 15000,   // 15 seconds
            [this.syncTypes.FOCUS]: 60000,      // 1 minute
            [this.syncTypes.VISIBILITY]: 60000, // 1 minute
            [this.syncTypes.PERIODIC]: 120000,  // 2 minutes
            [this.syncTypes.LOGIN]: 10000       // 10 seconds
        };
        
        return cooldowns[syncType] || 30000;
    }
    
    // Execute sync
    async executeSync(syncType, args) {
        const syncId = `${syncType}_${Date.now()}`;
        this.activeSyncs.add(syncId);
        
        try {
            console.log(`🚀 بدء مزامنة منسقة: ${syncType}`);
            
            // Update timestamps
            const now = Date.now();
            this.lastSyncTime = now;
            this.lastSyncByType.set(syncType, now);
            
            // Clear pending UI updates
            if (window.uiStabilityManager) {
                window.uiStabilityManager.clearPendingUpdates();
            }
            
            // Execute the actual sync
            let result = false;
            
            if (window.SIMPLE_SYNC && window.SIMPLE_SYNC.syncProductsNow) {
                result = await window.SIMPLE_SYNC.syncProductsNow();
            } else if (this.originalQuickSync) {
                result = await this.originalQuickSync(...args);
            } else if (this.originalAutoSync) {
                result = await this.originalAutoSync(...args);
            }
            
            // Add to notifications hub only (no popup)
            if (result) {
                console.log(`✅ تمت المزامنة بنجاح (${syncType})`);

                if (typeof addNotificationToHistory === 'function') {
                    addNotificationToHistory(`تمت المزامنة بنجاح (${syncType})`, 'success');
                }
            }
            
            console.log(`✅ انتهت المزامنة المنسقة: ${syncType} - ${result ? 'نجحت' : 'فشلت'}`);
            
            // Ensure UI stability after sync
            if (window.uiStabilityManager) {
                setTimeout(() => {
                    window.uiStabilityManager.ensureUIStability();
                }, 1000);
            }
            
            return result;
            
        } catch (error) {
            console.error(`❌ خطأ في المزامنة المنسقة ${syncType}:`, error);
            return false;
        } finally {
            // Remove from active syncs
            this.activeSyncs.delete(syncId);
            
            // Process queue
            setTimeout(() => this.processQueue(), 1000);
        }
    }
    
    // Add sync to queue
    addToQueue(syncType, args) {
        // Remove existing sync of same type from queue
        this.syncQueue = this.syncQueue.filter(item => item.type !== syncType);
        
        // Add new sync to queue
        this.syncQueue.push({
            type: syncType,
            args: args,
            timestamp: Date.now()
        });
        
        // Limit queue size
        if (this.syncQueue.length > 3) {
            this.syncQueue = this.syncQueue.slice(-3);
        }
        
        console.log(`📋 تم إضافة ${syncType} للطابور (${this.syncQueue.length} عناصر)`);
    }
    
    // Process sync queue
    async processQueue() {
        if (this.isProcessingQueue || this.syncQueue.length === 0) {
            return;
        }
        
        this.isProcessingQueue = true;
        
        try {
            const queueItem = this.syncQueue.shift();
            if (queueItem) {
                console.log(`📋 معالجة طابور المزامنة: ${queueItem.type}`);
                await this.coordinatedSync(queueItem.type, ...queueItem.args);
            }
        } catch (error) {
            console.error('❌ خطأ في معالجة طابور المزامنة:', error);
        } finally {
            this.isProcessingQueue = false;
            
            // Continue processing if there are more items
            if (this.syncQueue.length > 0) {
                setTimeout(() => this.processQueue(), 2000);
            }
        }
    }
    
    // Check if should show notification
    shouldShowNotification() {
        if (!this.notificationsEnabled) return false;
        
        const now = Date.now();
        if (now - this.lastNotificationTime < this.notificationCooldown) {
            return false;
        }
        
        this.lastNotificationTime = now;
        return true;
    }
    
    // Show controlled notification
    showControlledNotification(message, type) {
        try {
            if (window.notificationManager && window.notificationManager.showNotification) {
                window.notificationManager.showNotification(message, type);
            } else if (typeof utils !== 'undefined' && utils.showNotification) {
                utils.showNotification(message, type);
            } else {
                console.log(`📢 ${type.toUpperCase()}: ${message}`);
            }
        } catch (error) {
            console.error('❌ خطأ في عرض الإشعار المنسق:', error);
        }
    }
    
    // Enable/disable sync
    setSyncEnabled(enabled) {
        this.syncEnabled = enabled;
        console.log(`🔄 المزامنة ${enabled ? 'مفعلة' : 'معطلة'}`);
    }
    
    // Enable/disable notifications
    setNotificationsEnabled(enabled) {
        this.notificationsEnabled = enabled;
        console.log(`📢 إشعارات المزامنة ${enabled ? 'مفعلة' : 'معطلة'}`);
    }
    
    // Force sync now (bypass cooldowns)
    async forceSyncNow() {
        console.log('🚨 مزامنة قسرية - تجاهل فترات الانتظار');
        
        // Temporarily disable cooldowns
        const originalCooldown = this.syncCooldown;
        this.syncCooldown = 0;
        
        try {
            const result = await this.executeSync(this.syncTypes.MANUAL, []);
            return result;
        } finally {
            // Restore cooldowns
            this.syncCooldown = originalCooldown;
        }
    }
    
    // Get status
    getStatus() {
        return {
            syncEnabled: this.syncEnabled,
            activeSyncs: this.activeSyncs.size,
            queueLength: this.syncQueue.length,
            lastSyncTime: this.lastSyncTime,
            notificationsEnabled: this.notificationsEnabled,
            intervals: this.syncIntervals.size,
            lastSyncByType: Object.fromEntries(this.lastSyncByType)
        };
    }
    
    // Cleanup
    cleanup() {
        // Clear all intervals
        this.syncIntervals.forEach(interval => clearInterval(interval));
        this.syncIntervals.clear();
        
        // Clear all timeouts
        this.syncTimeouts.forEach(timeout => clearTimeout(timeout));
        this.syncTimeouts.clear();
        
        // Clear queue
        this.syncQueue = [];
        this.activeSyncs.clear();
        
        console.log('🧹 تم تنظيف منسق المزامنة');
    }
}

// Initialize Sync Coordinator
window.syncCoordinator = new SyncCoordinator();

// Export for use in other files
window.SyncCoordinator = SyncCoordinator;

// Add global functions for easy access
window.coordinatedSync = (type, ...args) => {
    return window.syncCoordinator.coordinatedSync(type, ...args);
};

window.forceSyncNow = () => {
    return window.syncCoordinator.forceSyncNow();
};

window.setSyncEnabled = (enabled) => {
    window.syncCoordinator.setSyncEnabled(enabled);
};

window.getSyncStatus = () => {
    return window.syncCoordinator.getStatus();
};

console.log('🎯 منسق المزامنة المركزي جاهز للاستخدام');
