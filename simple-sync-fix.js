// 🔄 حل بسيط وفعال لمزامنة المنتجات
// Simple and Effective Products Sync Solution

console.log('🔄 تحميل نظام المزامنة البسيط...');

// Ensure window.products is always an array
function ensureProductsArray() {
    try {
        if (!window.products || !Array.isArray(window.products)) {
            console.log('🔧 إصلاح window.products - إنشاء مصفوفة جديدة...');
            window.products = [];

            // Try to load from localStorage
            const localProducts = localStorage.getItem('inventory_products');
            if (localProducts) {
                const parsed = JSON.parse(localProducts);
                if (Array.isArray(parsed)) {
                    window.products = parsed;
                    console.log(`✅ تم تحميل ${parsed.length} منتج من localStorage إلى window.products`);
                }
            }
        }

        console.log(`📊 window.products جاهز مع ${window.products.length} منتج`);
        return true;

    } catch (error) {
        console.error('❌ خطأ في إصلاح window.products:', error);
        window.products = [];
        return false;
    }
}

// Global sync functions that actually work
window.SIMPLE_SYNC = {
    isRunning: false,
    lastSync: 0,
    
    // Force sync products NOW
    async syncProductsNow() {
        if (this.isRunning) {
            console.log('⏸️ المزامنة قيد التشغيل بالفعل...');
            return false;
        }

        this.isRunning = true;
        console.log('🚀 بدء المزامنة الفورية للمنتجات...');

        try {
            // FIRST: Ensure window.products is an array
            ensureProductsArray();
            // Step 1: Get local products
            let localProducts = [];
            try {
                const productsData = localStorage.getItem('inventory_products');
                if (productsData) {
                    localProducts = JSON.parse(productsData);
                }
                console.log(`📱 المنتجات المحلية: ${localProducts.length}`);
            } catch (error) {
                console.error('❌ خطأ في قراءة المنتجات المحلية:', error);
            }

            // Step 2: Check Firebase
            if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
                console.log('❌ Firebase غير متاح');
                this.isRunning = false;
                return false;
            }

            // Step 3: Upload local products to Firebase
            if (localProducts.length > 0) {
                console.log('📤 رفع المنتجات إلى Firebase...');
                const uploadResult = await window.firebaseService.saveProducts(localProducts);
                if (uploadResult) {
                    console.log('✅ تم رفع المنتجات بنجاح');
                    this.showNotification(`تم رفع ${localProducts.length} منتج إلى السحابة`, 'success');
                } else {
                    console.log('❌ فشل في رفع المنتجات');
                }
            }

            // Step 4: Download from Firebase
            console.log('📥 تحميل المنتجات من Firebase...');
            const firebaseProducts = await window.firebaseService.loadProducts();
            
            if (firebaseProducts && firebaseProducts.length > 0) {
                console.log(`📥 تم تحميل ${firebaseProducts.length} منتج من Firebase`);
                
                // Update localStorage
                localStorage.setItem('inventory_products', JSON.stringify(firebaseProducts));
                
                // Update global variable - SAFE METHOD
                try {
                    // Ensure window.products is an array
                    if (!window.products || !Array.isArray(window.products)) {
                        console.log('🔧 إنشاء مصفوفة منتجات جديدة...');
                        window.products = [];
                    }

                    // Clear and update
                    window.products.length = 0;
                    window.products.push(...firebaseProducts);
                    console.log('✅ تم تحديث المتغير العام للمنتجات');

                } catch (error) {
                    console.error('❌ خطأ في تحديث window.products:', error);
                    // Force create new array
                    window.products = [...firebaseProducts];
                    console.log('🔧 تم إنشاء مصفوفة منتجات جديدة قسرياً');
                }
                
                // Force update UI
                this.forceUpdateUI();
                
                this.showNotification(`تم تحميل ${firebaseProducts.length} منتج من السحابة`, 'success');
                
                this.lastSync = Date.now();
                console.log('✅ تمت المزامنة بنجاح');
                return true;
            } else {
                console.log('ℹ️ لا توجد منتجات في Firebase');
                return false;
            }

        } catch (error) {
            console.error('❌ خطأ في المزامنة:', error);
            this.showNotification('خطأ في المزامنة: ' + error.message, 'error');
            return false;
        } finally {
            this.isRunning = false;
        }
    },

    // Force update UI
    forceUpdateUI() {
        console.log('🔄 تحديث الواجهة قسرياً...');

        try {
            // Ensure products array is ready
            ensureProductsArray();

            const productCount = window.products ? window.products.length : 0;
            console.log(`📊 عدد المنتجات المتاحة: ${productCount}`);

            // Method 1: Try loadProductsTable function
            if (typeof loadProductsTable === 'function') {
                console.log('🔄 استخدام loadProductsTable...');
                loadProductsTable();
                console.log('✅ تم استدعاء loadProductsTable');
            } else {
                console.log('⚠️ وظيفة loadProductsTable غير متاحة');
            }

            // Method 2: Try manual table update
            this.manualUpdateProductsTable();

            // Method 3: Update dashboard stats
            if (typeof updateDashboardStats === 'function') {
                updateDashboardStats();
                console.log('✅ تم تحديث إحصائيات لوحة التحكم');
            } else {
                console.log('⚠️ وظيفة updateDashboardStats غير متاحة');
            }

            // Method 4: Force reload products table with delay
            setTimeout(() => {
                console.log('🔄 إعادة تحميل مؤجلة للجدول...');
                this.manualUpdateProductsTable();

                if (typeof loadProductsTable === 'function') {
                    loadProductsTable();
                }
            }, 1000);

            // Method 5: Update any product counts on the page
            const productCountElements = document.querySelectorAll('[data-product-count]');
            if (productCountElements.length > 0) {
                productCountElements.forEach(el => {
                    el.textContent = productCount;
                });
                console.log(`✅ تم تحديث عدادات المنتجات: ${productCount}`);
            }

            // Method 6: Trigger custom events
            this.triggerUIUpdateEvents();

        } catch (error) {
            console.error('❌ خطأ في تحديث الواجهة:', error);
        }
    },

    // Manual update products table
    manualUpdateProductsTable() {
        console.log('🔧 تحديث جدول المنتجات يدوياً...');

        try {
            const productsTable = document.querySelector('#productsTable tbody');
            if (!productsTable) {
                console.log('⚠️ جدول المنتجات غير موجود');
                return;
            }

            console.log('✅ تم العثور على جدول المنتجات');

            if (!window.products || window.products.length === 0) {
                console.log('📭 لا توجد منتجات لعرضها');
                productsTable.innerHTML = '<tr><td colspan="6" style="text-align: center; color: #666;">لا توجد منتجات</td></tr>';
                return;
            }

            console.log(`📦 عرض ${window.products.length} منتج في الجدول`);

            let tableHTML = '';
            window.products.forEach((product, index) => {
                tableHTML += `
                    <tr>
                        <td>${index + 1}</td>
                        <td>${product.manufacturer || 'غير محدد'}</td>
                        <td>${product.category || 'غير محدد'}</td>
                        <td>${product.availableQuantity || 0}</td>
                        <td>${product.price || 0} ريال</td>
                        <td>
                            <button class="btn-edit" onclick="editProduct(${product.id || index})">
                                <i class="fas fa-edit"></i> تعديل
                            </button>
                            <button class="btn-delete" onclick="deleteProduct(${product.id || index})">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        </td>
                    </tr>
                `;
            });

            productsTable.innerHTML = tableHTML;
            console.log('✅ تم تحديث جدول المنتجات يدوياً');

        } catch (error) {
            console.error('❌ خطأ في التحديث اليدوي للجدول:', error);
        }
    },

    // Trigger UI update events
    triggerUIUpdateEvents() {
        console.log('📡 إرسال أحداث تحديث الواجهة...');

        try {
            // Trigger custom events
            const updateEvent = new CustomEvent('productsUpdated', {
                detail: { products: window.products }
            });
            document.dispatchEvent(updateEvent);

            const syncEvent = new CustomEvent('productsSynced', {
                detail: { count: window.products ? window.products.length : 0 }
            });
            document.dispatchEvent(syncEvent);

            console.log('✅ تم إرسال أحداث تحديث الواجهة');

        } catch (error) {
            console.error('❌ خطأ في إرسال الأحداث:', error);
        }
    },

    // Show notification
    showNotification(message, type = 'info') {
        console.log(`📢 ${type.toUpperCase()}: ${message}`);

        // Filter sync/cloud notifications - only add to hub, no popup
        const isSyncNotification = message.includes('مزامنة') ||
                                  message.includes('السحابة') ||
                                  message.includes('Firebase') ||
                                  message.includes('تم تحميل') ||
                                  message.includes('تم رفع') ||
                                  message.includes('تم تحديث') ||
                                  message.includes('تمت المزامنة') ||
                                  message.includes('نظام المزامنة');

        if (isSyncNotification) {
            console.log(`📱 إشعار مزامنة أضيف للمركز فقط: ${message}`);

            // Add to interactive notifications hub only
            if (typeof addNotificationToHistory === 'function') {
                addNotificationToHistory(message, type);
            }

            return; // Don't show popup for sync notifications
        }

        // Try multiple notification methods
        try {
            // Method 1: utils.showNotification
            if (typeof utils !== 'undefined' && utils.showNotification) {
                utils.showNotification(message, type);
                return;
            }

            // Method 2: window.notificationManager
            if (window.notificationManager && window.notificationManager.showNotification) {
                window.notificationManager.showNotification(message, type);
                return;
            }

            // Method 3: Simple alert for critical messages
            if (type === 'error') {
                alert('خطأ: ' + message);
                return;
            }

            // Method 4: Console log as fallback
            console.log(`📢 ${message}`);

        } catch (error) {
            console.error('❌ خطأ في عرض الإشعار:', error);
        }
    },

    // Auto sync - DISABLED (handled by sync coordinator)
    startAutoSync() {
        console.log('⚠️ المزامنة التلقائية معطلة - يتم التحكم بواسطة منسق المزامنة المركزي');

        // Check if sync coordinator is available
        if (window.syncCoordinator) {
            console.log('✅ منسق المزامنة المركزي نشط - لا حاجة للمزامنة التلقائية');
            return;
        }

        // Note: Auto sync is now handled by the central sync coordinator
        // to prevent multiple conflicting sync operations
    },

    // Check sync status
    getStatus() {
        // Ensure products array exists
        ensureProductsArray();

        return {
            isRunning: this.isRunning,
            lastSync: this.lastSync,
            timeSinceLastSync: Date.now() - this.lastSync,
            localProductsCount: Array.isArray(window.products) ? window.products.length : 0,
            firebaseReady: window.firebaseService ? window.firebaseService.isFirebaseReady() : false,
            productsArrayValid: Array.isArray(window.products)
        };
    }
};

// Override the existing sync functions with our simple ones
window.syncProductsToFirebase = async function() {
    console.log('📤 استدعاء syncProductsToFirebase - تحويل للنظام البسيط...');
    return await window.SIMPLE_SYNC.syncProductsNow();
};

window.loadProductsFromFirebase = async function() {
    console.log('📥 استدعاء loadProductsFromFirebase - تحويل للنظام البسيط...');
    return await window.SIMPLE_SYNC.syncProductsNow();
};

window.syncProductsBidirectional = async function() {
    console.log('🔄 استدعاء syncProductsBidirectional - تحويل للنظام البسيط...');
    return await window.SIMPLE_SYNC.syncProductsNow();
};

// Add a simple button to force sync - DISABLED
window.addSyncButton = function() {
    console.log('🚫 زر المزامنة الفورية معطل لمنع الإشعارات المتكررة');

    // Remove existing button if it exists
    const existingButton = document.getElementById('forceSyncBtn');
    if (existingButton) {
        existingButton.remove();
        console.log('🗑️ تم حذف زر المزامنة الفورية الموجود');
    }

    // Do not create new sync button
    return;
    
    button.onclick = async function() {
        button.disabled = true;
        button.innerHTML = '⏳ جاري المزامنة...';
        
        const result = await window.SIMPLE_SYNC.syncProductsNow();
        
        if (result) {
            button.innerHTML = '✅ تمت المزامنة';
            button.style.background = '#28a745';
        } else {
            button.innerHTML = '❌ فشلت المزامنة';
            button.style.background = '#dc3545';
        }
        
        setTimeout(() => {
            button.disabled = false;
            button.innerHTML = '🔄 مزامنة فورية';
            button.style.background = '#28a745';
        }, 3000);
    };
    
    document.body.appendChild(button);
    console.log('✅ تم إضافة زر المزامنة الفورية');
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تهيئة نظام المزامنة البسيط...');

    // FIRST: Ensure products array is ready
    ensureProductsArray();

    // Wait for other systems to load
    setTimeout(() => {
        // Ensure products array again
        ensureProductsArray();

        // Add sync button
        window.addSyncButton();

        // Start auto sync
        window.SIMPLE_SYNC.startAutoSync();

        // Do initial sync
        setTimeout(() => {
            window.SIMPLE_SYNC.syncProductsNow();
        }, 5000);

        console.log('✅ نظام المزامنة البسيط جاهز');
    }, 3000);
});

// Add global shortcut
window.forceSyncNow = function() {
    return window.SIMPLE_SYNC.syncProductsNow();
};

// Add status check function
window.checkSyncStatus = function() {
    const status = window.SIMPLE_SYNC.getStatus();
    console.table(status);
    return status;
};

// Add global fix function for window.products
window.fixProductsArray = function() {
    console.log('🔧 إصلاح window.products...');
    return ensureProductsArray();
};

// Add global debug function for UI issues
window.debugProductsUI = function() {
    console.log('🔍 تشخيص مشكلة عرض المنتجات...');

    // Check products array
    console.log('📊 window.products:', window.products);
    console.log('📊 عدد المنتجات:', window.products ? window.products.length : 0);
    console.log('📊 نوع البيانات:', typeof window.products);
    console.log('📊 هل هو مصفوفة:', Array.isArray(window.products));

    // Check localStorage
    const localProducts = localStorage.getItem('inventory_products');
    if (localProducts) {
        try {
            const parsed = JSON.parse(localProducts);
            console.log('💾 localStorage products:', parsed);
            console.log('💾 عدد المنتجات في localStorage:', parsed.length);
        } catch (error) {
            console.error('❌ خطأ في قراءة localStorage:', error);
        }
    } else {
        console.log('💾 localStorage فارغ');
    }

    // Check products table
    const productsTable = document.querySelector('#productsTable tbody');
    console.log('🗃️ جدول المنتجات:', productsTable ? 'موجود' : 'غير موجود');
    if (productsTable) {
        console.log('🗃️ محتوى الجدول:', productsTable.innerHTML.length > 0 ? 'يحتوي على بيانات' : 'فارغ');
    }

    // Check functions
    console.log('🔧 loadProductsTable:', typeof loadProductsTable);
    console.log('🔧 updateDashboardStats:', typeof updateDashboardStats);

    // Force UI update
    console.log('🔄 محاولة تحديث الواجهة قسرياً...');
    if (window.SIMPLE_SYNC && window.SIMPLE_SYNC.forceUpdateUI) {
        window.SIMPLE_SYNC.forceUpdateUI();
    }

    return {
        productsCount: window.products ? window.products.length : 0,
        localStorageCount: localProducts ? JSON.parse(localProducts).length : 0,
        tableExists: !!productsTable,
        functionsAvailable: {
            loadProductsTable: typeof loadProductsTable === 'function',
            updateDashboardStats: typeof updateDashboardStats === 'function'
        }
    };
};

// Add error handler for push errors
window.addEventListener('error', function(event) {
    if (event.message && event.message.includes('window.products.push is not a function')) {
        console.error('🚨 تم اكتشاف خطأ window.products.push - تطبيق الإصلاح التلقائي...');
        ensureProductsArray();

        // Try to continue with sync
        setTimeout(() => {
            if (window.SIMPLE_SYNC && !window.SIMPLE_SYNC.isRunning) {
                window.SIMPLE_SYNC.syncProductsNow();
            }
        }, 1000);
    }
});

console.log('✅ تم تحميل نظام المزامنة البسيط بنجاح');
console.log('💡 استخدم window.forceSyncNow() للمزامنة الفورية');
console.log('💡 استخدم window.checkSyncStatus() لفحص حالة المزامنة');
console.log('💡 استخدم window.fixProductsArray() لإصلاح مشكلة window.products');
