<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول - النسور الماسية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        #output {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
            width: 200px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 اختبار تسجيل الدخول - النسور الماسية</h1>
        
        <div class="test-section">
            <h3>📊 معلومات النظام</h3>
            <button onclick="checkSystemInfo()">فحص معلومات النظام</button>
            <button onclick="checkLocalStorage()">فحص التخزين المحلي</button>
            <button onclick="clearAllData()">مسح جميع البيانات</button>
        </div>

        <div class="test-section">
            <h3>👥 إدارة المستخدمين</h3>
            <button onclick="checkUserSystem()">فحص نظام المستخدمين</button>
            <button onclick="createDefaultAdmin()">إنشاء المدير الافتراضي</button>
            <button onclick="listAllUsers()">عرض جميع المستخدمين</button>
        </div>

        <div class="test-section">
            <h3>🔐 اختبار تسجيل الدخول</h3>
            <div>
                <input type="email" id="testEmail" placeholder="البريد الإلكتروني" value="<EMAIL>">
                <input type="password" id="testPassword" placeholder="كلمة المرور" value="2030">
                <button onclick="testLogin()">اختبار تسجيل الدخول</button>
            </div>
            <button onclick="testDefaultLogin()">اختبار تسجيل الدخول الافتراضي</button>
        </div>

        <div class="test-section">
            <h3>🌐 اختبار إعادة التوجيه</h3>
            <button onclick="checkRedirects()">فحص إعادة التوجيه</button>
            <button onclick="testLocalRedirect()">اختبار التوجيه المحلي</button>
        </div>

        <div class="test-section">
            <h3>📝 نتائج الاختبار</h3>
            <div id="output"></div>
            <button onclick="clearOutput()">مسح النتائج</button>
        </div>
    </div>

    <!-- Load User Management System -->
    <script src="user-management.js"></script>

    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('output');
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
            output.textContent += `[${timestamp}] ${prefix} ${message}\n`;
            output.scrollTop = output.scrollHeight;
            console.log(message);
        }

        function clearOutput() {
            document.getElementById('output').textContent = '';
        }

        function checkSystemInfo() {
            log('🔍 فحص معلومات النظام...');
            log(`المتصفح: ${navigator.userAgent}`);
            log(`الرابط الحالي: ${window.location.href}`);
            log(`localStorage متاح: ${typeof Storage !== 'undefined'}`);
            log(`userManager متاح: ${typeof userManager !== 'undefined'}`);
            
            if (typeof userManager !== 'undefined') {
                log('✅ نظام إدارة المستخدمين محمل', 'success');
            } else {
                log('❌ نظام إدارة المستخدمين غير محمل', 'error');
            }
        }

        function checkLocalStorage() {
            log('💾 فحص التخزين المحلي...');
            
            const keys = ['systemUsers', 'currentUser', 'isLoggedIn', 'loginTime'];
            keys.forEach(key => {
                const value = localStorage.getItem(key);
                if (value) {
                    log(`${key}: ${value.substring(0, 100)}${value.length > 100 ? '...' : ''}`);
                } else {
                    log(`${key}: غير موجود`);
                }
            });
        }

        function clearAllData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات؟')) {
                localStorage.clear();
                log('🗑️ تم مسح جميع البيانات', 'success');
            }
        }

        function checkUserSystem() {
            log('👥 فحص نظام المستخدمين...');
            
            if (typeof userManager === 'undefined') {
                log('❌ نظام إدارة المستخدمين غير متاح', 'error');
                return;
            }

            try {
                const users = userManager.getAllUsers();
                log(`عدد المستخدمين: ${users.length}`);
                
                users.forEach((user, index) => {
                    log(`${index + 1}. ${user.name} - ${user.email} - نشط: ${user.isActive}`);
                });
            } catch (error) {
                log(`خطأ في فحص المستخدمين: ${error.message}`, 'error');
            }
        }

        function createDefaultAdmin() {
            log('👤 إنشاء المدير الافتراضي...');
            
            if (typeof userManager === 'undefined') {
                log('❌ نظام إدارة المستخدمين غير متاح', 'error');
                return;
            }

            try {
                // Clear existing users first
                localStorage.removeItem('systemUsers');
                
                // Reinitialize user manager
                userManager.initializeDefaultUsers();
                
                log('✅ تم إنشاء المدير الافتراضي بنجاح', 'success');
                
                // Verify creation
                setTimeout(() => {
                    checkUserSystem();
                }, 500);
            } catch (error) {
                log(`خطأ في إنشاء المدير: ${error.message}`, 'error');
            }
        }

        function listAllUsers() {
            log('📋 عرض جميع المستخدمين...');
            checkUserSystem();
        }

        function testLogin() {
            const email = document.getElementById('testEmail').value;
            const password = document.getElementById('testPassword').value;
            
            log(`🔐 اختبار تسجيل الدخول: ${email}`);
            
            if (typeof userManager === 'undefined') {
                log('❌ نظام إدارة المستخدمين غير متاح', 'error');
                return;
            }

            try {
                const result = userManager.authenticateUser(email, password);
                
                if (result.success) {
                    log(`✅ تم تسجيل الدخول بنجاح: ${result.user.name}`, 'success');
                } else {
                    log(`❌ فشل في تسجيل الدخول: ${result.error}`, 'error');
                }
            } catch (error) {
                log(`خطأ في تسجيل الدخول: ${error.message}`, 'error');
            }
        }

        function testDefaultLogin() {
            document.getElementById('testEmail').value = '<EMAIL>';
            document.getElementById('testPassword').value = '2030';
            testLogin();
        }

        function checkRedirects() {
            log('🌐 فحص إعادة التوجيه...');
            log(`الرابط الحالي: ${window.location.href}`);
            log(`النطاق: ${window.location.hostname}`);
            log(`البروتوكول: ${window.location.protocol}`);
            
            // Check for any redirect scripts
            const scripts = document.querySelectorAll('script');
            let foundRedirect = false;
            
            scripts.forEach((script, index) => {
                if (script.src && (script.src.includes('netlify') || script.src.includes('diamond-eagles'))) {
                    log(`⚠️ وجد script مشبوه: ${script.src}`, 'error');
                    foundRedirect = true;
                }
            });
            
            if (!foundRedirect) {
                log('✅ لم يتم العثور على scripts إعادة توجيه مشبوهة', 'success');
            }
        }

        function testLocalRedirect() {
            log('🔄 اختبار التوجيه المحلي...');
            
            // Simulate login success
            localStorage.setItem('isLoggedIn', 'true');
            localStorage.setItem('currentUser', JSON.stringify({
                name: 'مستخدم تجريبي',
                email: '<EMAIL>'
            }));
            localStorage.setItem('loginTime', new Date().toISOString());
            
            log('✅ تم حفظ بيانات تسجيل الدخول', 'success');
            log('🔗 يمكنك الآن الانتقال إلى index.html');
        }

        // Initialize on load
        window.addEventListener('load', function() {
            log('🚀 تم تحميل صفحة الاختبار');
            
            setTimeout(() => {
                checkSystemInfo();
            }, 1000);
        });
    </script>
</body>
</html>
