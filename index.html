<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شركة النسور الماسية للتجارة - نظام إدارة مخزون بطاريات الدواجن</title>

    <!-- Force desktop mode on mobile -->
    <script>
        // Force desktop viewport on mobile devices
        if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
            console.log('📱 تم اكتشاف جهاز محمول - تفعيل وضع سطح المكتب');

            // Update viewport for desktop mode
            const viewport = document.querySelector('meta[name="viewport"]');
            if (viewport) {
                viewport.setAttribute('content', 'width=1200, initial-scale=0.8, user-scalable=yes');
            }

            // Add desktop mode class to body
            document.addEventListener('DOMContentLoaded', function() {
                document.body.classList.add('desktop-mode-mobile');
                console.log('🖥️ تم تفعيل وضع سطح المكتب للهاتف');
            });
        }
    </script>

    <!-- Prevent Caching for Mobile Updates -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta name="cache-control" content="no-cache">
    <meta name="expires" content="0">
    <meta name="pragma" content="no-cache">

    <!-- PWA Meta Tags -->
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="النسور الماسية">
    <meta name="mobile-web-app-capable" content="yes">

    <!-- Force Refresh Meta -->
    <meta name="version" content="2024.12.12.001">
    <meta name="last-modified" content="2024-12-12T12:00:00Z">

    <!-- Custom Favicon -->
    <link rel="icon" type="image/svg+xml" href="data:image/svg+xml;base64,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">

    <!-- Fonts & Icons -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- html2pdf Library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>

    <!-- Google APIs -->
    <script src="https://apis.google.com/js/api.js"></script>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore-compat.js"></script>



    <!-- Styles with Cache Busting -->
    <link rel="stylesheet" href="style.css?v=2024.12.12.001">
    <link rel="stylesheet" href="notification-controls.css?v=2024.12.12.001">

    <!-- Desktop mode for mobile CSS -->
    <style>
        /* Desktop mode for mobile devices */
        .desktop-mode-mobile {
            min-width: 1200px !important;
            overflow-x: auto !important;
        }

        .desktop-mode-mobile * {
            -webkit-text-size-adjust: none !important;
            text-size-adjust: none !important;
        }

        .desktop-mode-mobile .container {
            min-width: 1200px !important;
            width: 1200px !important;
        }

        .desktop-mode-mobile .sidebar {
            width: 250px !important;
            position: fixed !important;
        }

        .desktop-mode-mobile .main-content {
            margin-right: 250px !important;
            width: calc(100% - 250px) !important;
        }

        /* Force desktop layout on mobile */
        @media (max-width: 768px) {
            .desktop-mode-mobile {
                transform-origin: top right;
                transform: scale(0.8);
                width: 125% !important;
            }

            .desktop-mode-mobile .header {
                position: fixed !important;
                top: 0 !important;
                right: 0 !important;
                width: 100% !important;
                z-index: 1000 !important;
            }

            .desktop-mode-mobile .sidebar {
                position: fixed !important;
                top: 60px !important;
                right: 0 !important;
                height: calc(100vh - 60px) !important;
                overflow-y: auto !important;
            }

            .desktop-mode-mobile .main-content {
                margin-top: 60px !important;
                margin-right: 250px !important;
                padding: 20px !important;
            }
        }
    </style>

</head>
<body>
    <!-- Login Check Script -->
    <script>
        // Force show cloud storage on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔧 إصلاح فوري لرؤية التخزين السحابي...');

            setTimeout(() => {
                // Force add settings-active class
                document.body.classList.add('settings-active');
                document.documentElement.classList.add('settings-cleaned');

                // Force show all settings cards
                const settingsCards = document.querySelectorAll('.settings-card');
                const settingsContainer = document.querySelector('.settings-container');

                if (settingsContainer) {
                    settingsContainer.style.cssText = `
                        display: grid !important;
                        visibility: visible !important;
                        opacity: 1 !important;
                        height: auto !important;
                        overflow: visible !important;
                    `;
                }

                settingsCards.forEach((card, index) => {
                    card.style.cssText = `
                        display: block !important;
                        visibility: visible !important;
                        opacity: 1 !important;
                        height: auto !important;
                        overflow: visible !important;
                        position: relative !important;
                        left: auto !important;
                        top: auto !important;
                        z-index: auto !important;
                        transform: none !important;
                        margin: 20px 0 !important;
                    `;
                    console.log(`✅ إصلاح بطاقة إعدادات #${index + 1}`);
                });

                console.log('✅ تم إصلاح رؤية التخزين السحابي');

                // Initialize local storage only
                console.log('💾 تهيئة التخزين المحلي...');
            }, 500);
        });

        // Check if user is logged in
        function checkLoginStatus() {
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            const loginTime = localStorage.getItem('loginTime');

            if (isLoggedIn !== 'true') {
                // Not logged in, redirect to login page
                window.location.href = 'login-system.html';
                return false;
            }

            if (loginTime) {
                const loginDate = new Date(loginTime);
                const now = new Date();
                const hoursDiff = (now - loginDate) / (1000 * 60 * 60);

                if (hoursDiff >= 24) {
                    // Login expired (24 hours), clear session and redirect
                    localStorage.removeItem('isLoggedIn');
                    localStorage.removeItem('currentUser');
                    localStorage.removeItem('loginTime');
                    window.location.href = 'login-system.html';
                    return false;
                }
            }

            return true;
        }

        // Check if in browse mode
        function isBrowseMode() {
            return localStorage.getItem('isBrowseMode') === 'true';
        }

        // Apply browse mode restrictions
        function applyBrowseModeRestrictions() {
            if (isBrowseMode()) {
                console.log('🔍 تطبيق قيود وضع التصفح...');

                // Hide all sections except products
                const sections = ['dashboard', 'customers', 'settings'];
                sections.forEach(sectionId => {
                    const section = document.getElementById(sectionId);
                    if (section) {
                        section.style.display = 'none';
                    }
                });

                // Hide navigation items except products
                const navItems = document.querySelectorAll('.nav-link');
                navItems.forEach(item => {
                    if (!item.classList.contains('nav-products')) {
                        item.style.display = 'none';
                    }
                });

                // Show only products section
                const productsSection = document.getElementById('products');
                if (productsSection) {
                    productsSection.classList.add('active');
                }

                // Update header to show browse mode
                const logoText = document.querySelector('.logo-text h1');
                if (logoText) {
                    logoText.innerHTML = 'شركة النسور الماسية للتجارة <span style="font-size: 0.7em; color: #28a745;">(وضع التصفح)</span>';
                }

                // Update exit button
                const exitBtn = document.getElementById('exitAppBtn');
                if (exitBtn) {
                    exitBtn.innerHTML = '<i class="fas fa-times"></i><span>إنهاء التصفح</span>';
                    exitBtn.onclick = exitBrowseMode;
                }

                // Hide add product button and other action buttons
                setTimeout(() => {
                    const addButtons = document.querySelectorAll('.btn-primary, .btn-danger, .btn-warning');
                    addButtons.forEach(btn => {
                        if (btn.textContent.includes('إضافة') || btn.textContent.includes('حذف') || btn.textContent.includes('تصدير')) {
                            btn.style.display = 'none';
                        }
                    });

                    // Hide products actions section
                    const productsActions = document.querySelector('.products-actions');
                    if (productsActions) {
                        productsActions.style.display = 'none';
                    }

                    // Add browse mode notice
                    const sectionHeader = document.querySelector('#products .section-header');
                    if (sectionHeader && !document.getElementById('browseNotice')) {
                        const notice = document.createElement('div');
                        notice.id = 'browseNotice';
                        notice.innerHTML = `
                            <div style="background: #e8f5e8; color: #2d5a2d; padding: 15px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #28a745;">
                                <i class="fas fa-eye" style="margin-left: 10px;"></i>
                                <strong>وضع التصفح:</strong> يمكنك عرض المنتجات فقط. للحصول على صلاحيات كاملة، يرجى تسجيل الدخول كمدير.
                            </div>
                        `;
                        sectionHeader.appendChild(notice);
                    }
                }, 1000);
            }
        }

        // Exit browse mode
        function exitBrowseMode() {
            localStorage.removeItem('isBrowseMode');
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('currentUser');
            localStorage.removeItem('loginTime');
            window.location.href = 'login-system.html';
        }

        // Check login status immediately
        if (!checkLoginStatus()) {
            // Stop loading the page if not logged in
            document.documentElement.style.display = 'none';
        } else {
            // Initialize app normally (no browse mode)
            document.addEventListener('DOMContentLoaded', function() {
                // Check for automatic sync triggers
                checkAutoSyncTriggers();

                // Load local data only
                console.log('📦 تحميل البيانات المحلية...');

                // Initialize app with local data
                if (typeof initializeApp === 'function') {
                    initializeApp();
                }

                // Single sync on page load only
                setTimeout(() => {
                    console.log('📄 مزامنة واحدة عند تحميل الصفحة...');
                    if (window.syncCoordinator) {
                        window.syncCoordinator.coordinatedSync('login');
                    } else if (typeof autoSyncOnAppStart === 'function') {
                        autoSyncOnAppStart();
                    }

                    // Show welcome message about new sync system
                    setTimeout(() => {
                        if (typeof utils !== 'undefined' && utils.showNotification) {
                            utils.showNotification('📄 المزامنة السحابية تحدث فقط عند تحديث الصفحة', 'info');
                        }
                    }, 2000);
                }, 3000); // Wait 3 seconds for app to fully load

                // Setup event listeners for products updates
                setupProductsEventListeners();

                // Initialize interactive notifications hub and load company settings
                setTimeout(() => {
                    updateNotificationHub();
                    loadCompanySettings();
                }, 3000);
            });
        }

        // Setup event listeners for products updates
        function setupProductsEventListeners() {
            console.log('📡 إعداد معالجات أحداث المنتجات...');

            // Listen for products updated event
            document.addEventListener('productsUpdated', function(event) {
                console.log('📦 تم استلام حدث تحديث المنتجات:', event.detail);

                // Force reload products table
                setTimeout(() => {
                    if (typeof loadProductsTable === 'function') {
                        console.log('🔄 إعادة تحميل جدول المنتجات بعد التحديث...');
                        loadProductsTable();
                    }

                    if (typeof updateDashboardStats === 'function') {
                        console.log('📊 تحديث إحصائيات لوحة التحكم بعد التحديث...');
                        updateDashboardStats();
                    }
                }, 100);
            });

            // Listen for products synced event
            document.addEventListener('productsSynced', function(event) {
                console.log('☁️ تم استلام حدث مزامنة المنتجات:', event.detail);

                // Show sync notification
                const count = event.detail.count;
                if (count > 0) {
                    console.log(`✅ تم مزامنة ${count} منتج من السحابة`);

                    // Force multiple UI updates
                    setTimeout(() => forceCompleteUIUpdate(), 200);
                    setTimeout(() => forceCompleteUIUpdate(), 1000);
                    setTimeout(() => forceCompleteUIUpdate(), 2000);
                }
            });

            console.log('✅ تم إعداد معالجات أحداث المنتجات');
        }

        // Force complete UI update
        function forceCompleteUIUpdate() {
            console.log('🔄 تحديث شامل للواجهة...');

            try {
                // Update products table
                if (typeof loadProductsTable === 'function') {
                    loadProductsTable();
                    console.log('✅ تم تحديث جدول المنتجات');
                }

                // Update dashboard stats
                if (typeof updateDashboardStats === 'function') {
                    updateDashboardStats();
                    console.log('✅ تم تحديث إحصائيات لوحة التحكم');
                }

                // Manual table update if needed
                const productsTable = document.querySelector('#productsTable tbody');
                if (productsTable && window.products && window.products.length > 0) {
                    console.log('🔧 تحديث الجدول يدوياً...');

                    let tableHTML = '';
                    window.products.forEach((product, index) => {
                        tableHTML += `
                            <tr>
                                <td>${index + 1}</td>
                                <td>${product.manufacturer || 'غير محدد'}</td>
                                <td>${product.category || 'غير محدد'}</td>
                                <td>${product.availableQuantity || 0}</td>
                                <td>${product.price || 0} ريال</td>
                                <td>
                                    <button class="btn-edit" onclick="editProduct(${product.id || index})">
                                        <i class="fas fa-edit"></i> تعديل
                                    </button>
                                    <button class="btn-delete" onclick="deleteProduct(${product.id || index})">
                                        <i class="fas fa-trash"></i> حذف
                                    </button>
                                </td>
                            </tr>
                        `;
                    });

                    productsTable.innerHTML = tableHTML;
                    console.log(`✅ تم تحديث الجدول يدوياً مع ${window.products.length} منتج`);
                }

                // Update product counts
                const productCountElements = document.querySelectorAll('[data-product-count]');
                if (productCountElements.length > 0) {
                    const count = window.products ? window.products.length : 0;
                    productCountElements.forEach(el => {
                        el.textContent = count;
                    });
                    console.log(`✅ تم تحديث عدادات المنتجات: ${count}`);
                }

            } catch (error) {
                console.error('❌ خطأ في التحديث الشامل للواجهة:', error);
            }
        }

        // Function to auto-sync data when app starts
        async function autoSyncOnAppStart() {
            console.log('🔄 بدء المزامنة التلقائية عند فتح التطبيق...');

            try {
                // Check if Firebase is available and ready
                if (typeof window.firebaseService === 'undefined') {
                    console.log('⚠️ Firebase service not available, skipping auto-sync');
                    return;
                }

                if (!window.firebaseService.isFirebaseReady()) {
                    console.log('⚠️ Firebase not ready, skipping auto-sync');
                    return;
                }

                console.log('✅ Firebase ready, checking for newer data...');

                // Check if there's newer data in Firebase
                const firebaseProducts = await window.firebaseService.loadProducts();
                const firebaseCustomers = await window.firebaseService.loadCustomers();
                const firebaseUsers = await window.firebaseService.loadUsers();
                const firebaseSettings = await window.firebaseService.loadSettings();

                const localProducts = JSON.parse(localStorage.getItem('inventory_products') || '[]');
                const localCustomers = JSON.parse(localStorage.getItem('inventory_customers') || '[]');
                const localUsers = JSON.parse(localStorage.getItem('systemUsers') || '[]');
                const localSettings = JSON.parse(localStorage.getItem('systemSettings') || '{}');

                let hasUpdates = false;

                // Check if Firebase has more recent data
                if (firebaseProducts && firebaseProducts.length !== localProducts.length) {
                    console.log(`📦 Products update: Local ${localProducts.length}, Firebase ${firebaseProducts.length}`);
                    hasUpdates = true;
                }

                if (firebaseCustomers && firebaseCustomers.length !== localCustomers.length) {
                    console.log(`👥 Customers update: Local ${localCustomers.length}, Firebase ${firebaseCustomers.length}`);
                    hasUpdates = true;
                }

                if (firebaseUsers && firebaseUsers.length !== localUsers.length) {
                    console.log(`🔐 Users update: Local ${localUsers.length}, Firebase ${firebaseUsers.length}`);
                    hasUpdates = true;
                }

                if (hasUpdates) {
                    console.log('📥 تحديث البيانات من Firebase...');
                    await window.firebaseService.syncAllFromFirebase();
                    console.log('✅ تم تحديث البيانات من Firebase');

                    // Force UI refresh after sync
                    console.log('🔄 تحديث الواجهة بالبيانات الجديدة...');
                    setTimeout(() => {
                        if (typeof window.forceUIRefresh === 'function') {
                            window.forceUIRefresh();
                        } else {
                            // Fallback manual refresh
                            if (typeof updateDashboardStats === 'function') updateDashboardStats();
                            if (typeof loadProductsTable === 'function') loadProductsTable();
                            if (typeof loadCustomersTable === 'function') loadCustomersTable();
                            console.log('✅ تم تحديث الواجهة (fallback)');
                        }

                        // Also check for logo updates
                        if (typeof loadLogoFromFirebase === 'function') {
                            loadLogoFromFirebase();
                        }

                        // Load users from Firebase
                        if (typeof window.userManager !== 'undefined' && typeof window.userManager.loadUsersFromFirebase === 'function') {
                            window.userManager.loadUsersFromFirebase();
                        }
                    }, 1000);
                } else {
                    console.log('✅ البيانات المحلية محدثة');

                    // Upload local data if Firebase is empty
                    if ((!firebaseProducts || firebaseProducts.length === 0) && localProducts.length > 0) {
                        console.log('📤 رفع البيانات المحلية إلى Firebase...');
                        await window.firebaseService.syncAllToFirebase();
                        console.log('✅ تم رفع البيانات المحلية إلى Firebase');
                    }
                }

            } catch (error) {
                console.error('❌ خطأ في المزامنة التلقائية:', error);
                // Don't show error to user, just log it
            }
        }

        // Auto-sync events - DISABLED (handled by sync coordinator)
        console.log('⚠️ أحداث المزامنة التلقائية معطلة - يتم التحكم بواسطة منسق المزامنة المركزي');

        // Note: All sync events are now coordinated centrally to prevent conflicts
    </script>


    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <div class="logo-icon">
                        <svg class="custom-logo-icon" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                            <!-- Diamond Background -->
                            <defs>
                                <linearGradient id="diamondGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
                                    <stop offset="50%" style="stop-color:#764ba2;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#f093fb;stop-opacity:1" />
                                </linearGradient>
                                <linearGradient id="eagleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#ffd700;stop-opacity:1" />
                                    <stop offset="100%" style="stop-color:#ffb347;stop-opacity:1" />
                                </linearGradient>
                            </defs>

                            <!-- Diamond Shape -->
                            <polygon points="50,5 85,35 50,65 15,35" fill="url(#diamondGradient)" stroke="#fff" stroke-width="2"/>

                            <!-- Eagle Silhouette -->
                            <path d="M50 20 C45 18, 40 20, 38 25 C36 30, 40 35, 45 37 L50 40 L55 37 C60 35, 64 30, 62 25 C60 20, 55 18, 50 20 Z" fill="url(#eagleGradient)"/>
                            <circle cx="45" cy="25" r="2" fill="#333"/>
                            <path d="M42 28 L48 30" stroke="#333" stroke-width="1"/>

                            <!-- Poultry Battery Representation -->
                            <rect x="25" y="70" width="50" height="25" rx="3" fill="#4a90e2" stroke="#fff" stroke-width="1"/>
                            <rect x="30" y="75" width="8" height="6" fill="#fff" opacity="0.8"/>
                            <rect x="42" y="75" width="8" height="6" fill="#fff" opacity="0.8"/>
                            <rect x="54" y="75" width="8" height="6" fill="#fff" opacity="0.8"/>
                            <rect x="66" y="75" width="8" height="6" fill="#fff" opacity="0.8"/>

                            <!-- Small decorative elements -->
                            <circle cx="20" cy="15" r="2" fill="#ffd700" opacity="0.6"/>
                            <circle cx="80" cy="15" r="2" fill="#ffd700" opacity="0.6"/>
                            <circle cx="15" cy="50" r="1.5" fill="#ffd700" opacity="0.4"/>
                            <circle cx="85" cy="50" r="1.5" fill="#ffd700" opacity="0.4"/>
                        </svg>
                    </div>
                    <div class="logo-text">
                        <h1>شركة النسور الماسية للتجارة</h1>
                        <p>نظام إدارة مخزون بطاريات الدواجن</p>
                    </div>
                </div>

                <!-- Header Actions -->
                <div class="header-actions">
                    <!-- Interactive Notifications Hub -->
                    <div class="notifications-hub" id="notificationsHub" onclick="toggleNotificationsPanel()">
                        <div class="notification-icon-wrapper">
                            <i class="fas fa-bell"></i>
                            <div class="notification-waves">
                                <div class="wave wave-1"></div>
                                <div class="wave wave-2"></div>
                                <div class="wave wave-3"></div>
                            </div>
                        </div>
                        <span class="notification-count" id="notificationCount">0</span>
                        <div class="notification-status-dot" id="notificationStatusDot"></div>
                    </div>

                    <!-- Exit Button -->
                    <button id="exitAppBtn" class="exit-app-btn" onclick="if(typeof logoutUser === 'function') logoutUser(); else alert('جاري تحميل النظام...');" title="تسجيل خروج من التطبيق">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>تسجيل خروج</span>
                    </button>
                </div>

                <!-- Interactive Notifications Panel -->
                <div class="notifications-panel" id="notificationsPanel">
                    <div class="notifications-panel-header">
                        <h3><i class="fas fa-layer-group"></i> مركز الإشعارات</h3>
                        <div class="panel-controls">
                            <button class="panel-btn" onclick="clearAllNotifications()" title="مسح جميع الإشعارات">
                                <i class="fas fa-trash"></i>
                            </button>
                            <button class="panel-btn" onclick="toggleNotificationsPanel()" title="إغلاق">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                    <div class="notifications-panel-content" id="notificationsPanelContent">
                        <div class="no-notifications">
                            <i class="fas fa-bell-slash"></i>
                            <p>لا توجد إشعارات</p>
                        </div>
                    </div>
                    <div class="notifications-panel-footer">
                        <div class="notification-stats">
                            <span id="notificationStats">0 إشعارات نشطة</span>
                        </div>
                    </div>
                </div>

                <nav class="main-nav">
                    <ul class="nav-menu">
                        <li><a href="#" onclick="showSection('dashboard')" class="nav-link nav-dashboard active">
                            <i class="fas fa-tachometer-alt"></i>
                            <span>لوحة التحكم</span>
                        </a></li>
                        <li><a href="#" onclick="showSection('products')" class="nav-link nav-products">
                            <i class="fas fa-boxes"></i>
                            <span>إدارة المنتجات</span>
                        </a></li>
                        <li><a href="#" onclick="showSection('customers')" class="nav-link nav-customers">
                            <i class="fas fa-users"></i>
                            <span>طلبات العملاء</span>
                        </a></li>
                        <li><a href="#" onclick="showSection('settings')" class="nav-link nav-settings">
                            <i class="fas fa-cogs"></i>
                            <span>الإعدادات</span>
                        </a></li>
                    </ul>
                </nav>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Dashboard Section -->
            <section id="dashboard" class="section active">
                <div class="dashboard-header">
                    <h2><i class="fas fa-tachometer-alt"></i> لوحة التحكم الرئيسية</h2>
                </div>
                
                <div class="stats-grid">
                    <!-- Products Management Card - Modern Design -->
                    <div class="products-management-card" onclick="navigateToProducts()" title="انقر للانتقال إلى إدارة المنتجات">
                        <div class="card-header">
                            <div class="header-icon">
                                <i class="fas fa-warehouse"></i>
                            </div>
                            <div class="header-content">
                                <h3>إدارة المنتجات</h3>
                                <p>نظام شامل لإدارة المخزون</p>
                            </div>
                        </div>

                        <div class="card-stats">
                            <div class="stat-item">
                                <div class="stat-number" id="totalProducts">٠</div>
                                <div class="stat-label">إجمالي المنتجات</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="availableProducts">٠</div>
                                <div class="stat-label">متاح</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-number" id="reservedProducts">٠</div>
                                <div class="stat-label">محجوز</div>
                            </div>
                        </div>

                        <div class="card-actions">
                            <div class="action-item">
                                <i class="fas fa-plus-circle"></i>
                                <span>إضافة منتج جديد</span>
                            </div>
                            <div class="action-item">
                                <i class="fas fa-search"></i>
                                <span>البحث والفلترة</span>
                            </div>
                            <div class="action-item">
                                <i class="fas fa-download"></i>
                                <span>تصدير التقارير</span>
                            </div>
                        </div>

                        <div class="card-footer">
                            <div class="capacity-info">
                                <i class="fas fa-chart-bar"></i>
                                <span>السعة الإجمالية: <strong id="totalCapacity">٠</strong> طائر</span>
                            </div>
                            <div class="navigate-arrow">
                                <i class="fas fa-arrow-left"></i>
                            </div>
                        </div>

                        <div class="card-decoration">
                            <i class="fas fa-warehouse"></i>
                        </div>
                    </div>

                    <!-- Total Customers Card - Purple Theme -->
                    <div class="stat-card stat-card-purple clickable" onclick="navigateToCustomers()" title="انقر للانتقال إلى طلبات العملاء">
                        <div class="stat-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalCustomers">٠</h3>
                            <p>إجمالي العملاء</p>
                        </div>
                        <div class="click-indicator">
                            <i class="fas fa-arrow-left"></i>
                        </div>
                        <div class="stat-decoration">
                            <i class="fas fa-users decoration-icon"></i>
                        </div>
                    </div>



                    <!-- Breeding Requests Card - Teal Theme -->
                    <div class="stat-card stat-card-teal clickable" onclick="navigateToCustomers()" title="انقر للانتقال إلى طلبات العملاء">
                        <div class="stat-icon">
                            <i class="fas fa-seedling"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="breedingRequests">٠</h3>
                            <p>تربية</p>
                        </div>
                        <div class="click-indicator">
                            <i class="fas fa-arrow-left"></i>
                        </div>
                        <div class="stat-decoration">
                            <i class="fas fa-seedling decoration-icon"></i>
                        </div>
                    </div>

                    <!-- Production Requests Card - Pink Theme -->
                    <div class="stat-card stat-card-pink clickable" onclick="navigateToCustomers()" title="انقر للانتقال إلى طلبات العملاء">
                        <div class="stat-icon">
                            <i class="fas fa-egg"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="productionRequests">٠</h3>
                            <p>إنتاج</p>
                        </div>
                        <div class="click-indicator">
                            <i class="fas fa-arrow-left"></i>
                        </div>
                        <div class="stat-decoration">
                            <i class="fas fa-egg decoration-icon"></i>
                        </div>
                    </div>
                </div>

                <div class="welcome-section">
                    <div class="welcome-card">
                        <div class="welcome-header">
                            <div class="welcome-icon">
                                <svg class="welcome-dashboard-icon" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                                    <defs>
                                        <linearGradient id="welcomeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                            <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.9" />
                                            <stop offset="100%" style="stop-color:#f0f9ff;stop-opacity:0.7" />
                                        </linearGradient>
                                    </defs>
                                    <!-- Dashboard representation -->
                                    <rect x="10" y="20" width="80" height="60" rx="8" fill="url(#welcomeGradient)" stroke="rgba(255,255,255,0.5)" stroke-width="2"/>
                                    <!-- Chart bars -->
                                    <rect x="20" y="50" width="8" height="20" fill="rgba(255,255,255,0.8)"/>
                                    <rect x="35" y="40" width="8" height="30" fill="rgba(255,255,255,0.8)"/>
                                    <rect x="50" y="45" width="8" height="25" fill="rgba(255,255,255,0.8)"/>
                                    <rect x="65" y="35" width="8" height="35" fill="rgba(255,255,255,0.8)"/>
                                    <!-- Decorative elements -->
                                    <circle cx="25" cy="30" r="3" fill="rgba(255,255,255,0.6)"/>
                                    <circle cx="75" cy="30" r="3" fill="rgba(255,255,255,0.6)"/>
                                </svg>
                            </div>
                            <div class="welcome-content">
                                <h3>مرحباً بك في نظام إدارة المخزون</h3>
                                <p>نظام متكامل لإدارة منتجات بطاريات الدواجن وطلبات العملاء بكل سهولة واحترافية</p>
                            </div>
                        </div>

                        <div class="quick-actions">
                            <button class="btn btn-gradient-blue" onclick="showSection('products')">
                                <i class="fas fa-plus"></i>
                                <span>إضافة منتج جديد</span>
                                <div class="btn-shine"></div>
                            </button>
                            <button class="btn btn-gradient-purple" onclick="showSection('customers')">
                                <i class="fas fa-user-plus"></i>
                                <span>إضافة عميل جديد</span>
                                <div class="btn-shine"></div>
                            </button>
                            <button class="btn btn-gradient-green" onclick="showSection('settings')">
                                <i class="fas fa-cog"></i>
                                <span>إعدادات النظام</span>
                                <div class="btn-shine"></div>
                            </button>
                        </div>

                        <div class="system-features">
                            <div class="feature-item">
                                <i class="fas fa-chart-line"></i>
                                <span>تقارير تفصيلية</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-mobile-alt"></i>
                                <span>واجهة متجاوبة</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-shield-alt"></i>
                                <span>حفظ آمن للبيانات</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-download"></i>
                                <span>تصدير متعدد الصيغ</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Products Section -->
            <section id="products" class="section">
                <div class="section-header">
                    <h2><i class="fas fa-boxes"></i> إدارة المنتجات</h2>
                    <button class="btn btn-primary" onclick="showAddProductModal()">
                        <i class="fas fa-plus"></i>
                        إضافة منتج جديد
                    </button>
                </div>

                <div class="section-content">
                    <div class="filters-section">
                        <div class="search-and-filters">
                            <div class="search-box">
                                <i class="fas fa-search"></i>
                                <input type="text" id="productSearch" placeholder="البحث في المنتجات..." onkeyup="searchProducts()">
                            </div>

                            <div class="filters-grid">
                                <select id="productStatusFilter" onchange="searchProducts()">
                                    <option value="">جميع الحالات</option>
                                    <option value="متاح">متاح</option>
                                    <option value="محجوز">محجوز</option>
                                </select>

                                <select id="productCategoryFilter" onchange="searchProducts()">
                                    <option value="">جميع الفئات</option>
                                    <option value="انتاج">انتاج</option>
                                    <option value="تربية">تربية</option>
                                </select>

                                <button class="btn btn-secondary" onclick="clearProductFilters()">
                                    <i class="fas fa-times"></i>
                                    مسح الفلاتر
                                </button>

                                <button class="btn btn-success" onclick="showFilteredExportOptions()">
                                    <i class="fas fa-download"></i>
                                    تصدير النتائج
                                </button>
                            </div>
                        </div>
                    </div>

                <!-- Products Actions -->
                <div class="products-actions">
                    <div class="export-buttons">
                        <button class="btn btn-success" onclick="exportAllProducts('word')">
                            <i class="fas fa-file-word"></i>
                            تصدير Word
                        </button>
                        <button class="btn btn-danger" onclick="exportAllProducts('pdf')">
                            <i class="fas fa-file-pdf"></i>
                            تصدير PDF
                        </button>
                        <button class="btn btn-info" onclick="showProductsManagement()">
                            <i class="fas fa-cogs"></i>
                            إدارة المنتجات
                        </button>
                        <button class="btn btn-warning" onclick="showFloatingCalculator()" title="الآلة الحاسبة البسيطة">
                            <i class="fas fa-calculator"></i>
                            آلة حاسبة
                        </button>
                        <button class="btn btn-danger btn-delete-all" onclick="confirmDeleteAllProducts()" title="حذف جميع المنتجات">
                            <i class="fas fa-trash-alt"></i>
                            حذف الكل
                        </button>
                    </div>
                </div>

                <!-- Products Table -->
                <div class="table-container">
                    <table class="data-table" id="productsTable">
                        <thead>
                            <tr>
                                <th>الشركة المصنعة</th>
                                <th>بلد المنشأ</th>
                                <th>الفئة</th>
                                <th>الحالة</th>
                                <th>تاريخ الفك</th>
                                <th>الكمية المتاحة</th>
                                <th>عدد الأدوار</th>
                                <th>عدد الخطوط</th>
                                <th>عدد العشوش/خط</th>
                                <th>مقاس العش (سم)</th>
                                <th>إجمالي عدد العشوش</th>
                                <th>السعة الإجمالية</th>
                                <th>السعر</th>
                            </tr>
                        </thead>
                        <tbody id="productsTableBody">
                            <!-- Products will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Customers Section -->
            <section id="customers" class="section">
                <div class="section-header">
                    <h2><i class="fas fa-users"></i> إدارة طلبات العملاء</h2>
                    <div class="header-actions">
                        <button class="btn btn-primary" onclick="showAddCustomerModal()">
                            <i class="fas fa-user-plus"></i>
                            إضافة عميل جديد
                        </button>
                        <button class="btn btn-danger btn-delete-all" onclick="confirmDeleteAllCustomers()" title="حذف جميع العملاء">
                            <i class="fas fa-trash-alt"></i>
                            حذف الكل
                        </button>
                    </div>
                </div>

                <!-- Customers Filters -->
                <div class="filters-section">
                    <div class="filters-header">
                        <div class="search-and-filters">
                            <div class="search-box">
                                <i class="fas fa-search"></i>
                                <input type="text" id="customerSearch" placeholder="البحث برقم العميل أو الاسم أو رقم الجوال...">
                            </div>

                            <div class="filters-grid">
                                <select id="customerTypeFilter">
                                    <option value="">جميع أنواع العملاء</option>
                                    <option value="عميل عادي">عميل عادي</option>
                                    <option value="عميل محتمل">عميل محتمل</option>
                                </select>

                                <select id="requestTypeFilter">
                                    <option value="">جميع أنواع الطلبات</option>
                                    <option value="تربية">تربية</option>
                                    <option value="انتاج">انتاج</option>
                                </select>

                                <select id="nationalityFilter">
                                    <option value="">جميع الجنسيات</option>
                                    <!-- Will be populated dynamically -->
                                </select>

                                <button class="btn btn-secondary" onclick="clearCustomerFilters()">
                                    <i class="fas fa-times"></i>
                                    مسح الفلاتر
                                </button>
                            </div>
                        </div>

                        <div class="export-actions">
                            <button class="btn btn-success" onclick="exportFilteredCustomers('word')">
                                <i class="fas fa-file-word"></i>
                                تصدير Word
                            </button>
                            <button class="btn btn-info" onclick="exportFilteredCustomers('excel')">
                                <i class="fas fa-file-excel"></i>
                                تصدير Excel
                            </button>
                            <button class="btn btn-danger" onclick="exportFilteredCustomers('pdf')">
                                <i class="fas fa-file-pdf"></i>
                                تصدير PDF
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Customers Table -->
                <div class="table-container">
                    <table class="data-table" id="customersTable">
                        <thead>
                            <tr>
                                <th>الاسم</th>
                                <th>رقم الجوال</th>
                                <th>الجنسية</th>
                                <th>نوع العميل</th>
                                <th>نوع الطلب</th>
                                <th>تفاصيل الطلب</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="customersTableBody">
                            <!-- Customers will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Settings Section -->
            <section id="settings" class="section">
                <div class="section-header">
                    <h2><i class="fas fa-cogs"></i> إعدادات النظام</h2>
                </div>

                <div class="settings-container">
                    <!-- Company Information -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <h3><i class="fas fa-building"></i> بيانات الشركة</h3>
                        </div>
                        <div class="settings-card-body">
                            <form id="companySettingsForm">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="companyName">اسم الشركة</label>
                                        <input type="text" id="companyName" name="companyName" value="شركة النسور الماسية للتجارة">
                                    </div>
                                    <div class="form-group">
                                        <label for="commercialRegister">السجل التجاري</label>
                                        <input type="text" id="commercialRegister" name="commercialRegister" placeholder="رقم السجل التجاري">
                                    </div>
                                    <div class="form-group">
                                        <label for="taxNumber">الرقم الضريبي</label>
                                        <input type="text" id="taxNumber" name="taxNumber" placeholder="الرقم الضريبي (اختياري)">
                                    </div>
                                    <div class="form-group">
                                        <label for="companyCountry">الدولة</label>
                                        <select id="companyCountry" name="companyCountry">
                                            <option value="">اختر الدولة</option>
                                            <option value="السعودية">السعودية</option>
                                            <option value="الإمارات">الإمارات العربية المتحدة</option>
                                            <option value="الكويت">الكويت</option>
                                            <option value="قطر">قطر</option>
                                            <option value="البحرين">البحرين</option>
                                            <option value="عمان">عمان</option>
                                            <option value="مصر">مصر</option>
                                            <option value="الأردن">الأردن</option>
                                            <option value="لبنان">لبنان</option>
                                            <option value="سوريا">سوريا</option>
                                            <option value="العراق">العراق</option>
                                            <option value="فلسطين">فلسطين</option>
                                            <option value="المغرب">المغرب</option>
                                            <option value="الجزائر">الجزائر</option>
                                            <option value="تونس">تونس</option>
                                            <option value="ليبيا">ليبيا</option>
                                            <option value="السودان">السودان</option>
                                            <option value="اليمن">اليمن</option>
                                        </select>
                                    </div>
                                    <div class="form-group full-width">
                                        <label for="companyAddress">العنوان</label>
                                        <textarea id="companyAddress" name="companyAddress" rows="3" placeholder="العنوان التفصيلي للشركة"></textarea>
                                    </div>
                                    <div class="form-group">
                                        <label for="companyPhone">رقم الجوال الرئيسي</label>
                                        <input type="tel" id="companyPhone" name="companyPhone" value="">
                                    </div>
                                    <div class="form-group">
                                        <label for="whatsappNumber">رقم واتساب</label>
                                        <input type="tel" id="whatsappNumber" name="whatsappNumber" value="">
                                    </div>
                                </div>

                                <!-- Additional Contact Numbers Section -->
                                <div class="contact-numbers-section">
                                    <h4><i class="fas fa-phone-alt"></i> أرقام التواصل الإضافية</h4>
                                    <div id="additionalContacts">
                                        <!-- Additional contacts will be added here -->
                                    </div>
                                    <button type="button" class="btn btn-secondary btn-sm" onclick="addContactNumber()">
                                        <i class="fas fa-plus"></i> إضافة رقم تواصل
                                    </button>
                                </div>

                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="supportEmail">بريد الدعم</label>
                                        <input type="email" id="supportEmail" name="supportEmail" placeholder="<EMAIL>">
                                    </div>
                                    <div class="form-group">
                                        <label for="companyWebsite">الموقع الإلكتروني</label>
                                        <input type="url" id="companyWebsite" name="companyWebsite" placeholder="https://www.company.com">
                                    </div>
                                </div>
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> حفظ بيانات الشركة
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- User Management Settings -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <h3><i class="fas fa-users-cog"></i> إدارة المستخدمين والصلاحيات</h3>
                        </div>
                        <div class="settings-card-body">
                            <!-- Current Users List -->
                            <div class="users-section">
                                <div class="section-header-small" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                                    <h4 style="margin: 0;"><i class="fas fa-list"></i> المستخدمون الحاليون</h4>
                                    <button type="button" class="btn btn-success btn-sm" onclick="showAddUserModal()">
                                        <i class="fas fa-user-plus"></i> إضافة مستخدم جديد
                                    </button>
                                </div>

                                <div class="users-table-container" style="margin-bottom: 20px;">
                                    <table class="table" style="width: 100%; border-collapse: collapse;">
                                        <thead>
                                            <tr style="background-color: #f8f9fa;">
                                                <th style="padding: 10px; border: 1px solid #ddd;">الاسم</th>
                                                <th style="padding: 10px; border: 1px solid #ddd;">البريد الإلكتروني</th>
                                                <th style="padding: 10px; border: 1px solid #ddd;">الدور</th>
                                                <th style="padding: 10px; border: 1px solid #ddd;">الصلاحيات</th>
                                                <th style="padding: 10px; border: 1px solid #ddd;">الحالة</th>
                                                <th style="padding: 10px; border: 1px solid #ddd;">الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody id="usersTableBody">
                                            <!-- Users will be loaded here -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- Admin Settings -->
                            <div class="admin-settings-section" style="border-top: 1px solid #eee; padding-top: 20px;">
                                <h4><i class="fas fa-user-shield"></i> إعدادات المدير الرئيسي</h4>
                                <form id="adminSettingsForm">
                                    <div class="form-grid">
                                        <div class="form-group">
                                            <label for="adminEmail">البريد الإلكتروني للمدير</label>
                                            <input type="email" id="adminEmail" name="adminEmail" value="<EMAIL>" placeholder="<EMAIL>">
                                        </div>
                                        <div class="form-group">
                                            <label for="adminPassword">كلمة مرور المدير</label>
                                            <input type="password" id="adminPassword" name="adminPassword" value="2030" placeholder="كلمة المرور">
                                        </div>
                                        <div class="form-group">
                                            <label for="sessionTimeout">انتهاء الجلسة (بالدقائق)</label>
                                            <input type="number" id="sessionTimeout" name="sessionTimeout" min="5" max="1440" value="60" placeholder="60">
                                        </div>
                                    </div>

                                    <div class="user-info" style="margin: 15px 0; padding: 10px; background-color: #f8f9fa; border-radius: 5px; border-left: 4px solid #007bff;">
                                        <p style="margin: 0; font-size: 14px;">
                                            <i class="fas fa-info-circle" style="color: #007bff;"></i>
                                            <strong>المستخدم الحالي:</strong> <span id="currentUserDisplay"><EMAIL></span>
                                        </p>
                                        <p style="margin: 5px 0 0 0; font-size: 12px; color: #666;">
                                            <i class="fas fa-clock"></i> آخر تسجيل دخول: <span id="lastLoginDisplay">غير محدد</span>
                                        </p>
                                    </div>

                                    <div class="form-actions">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i> حفظ إعدادات المدير
                                        </button>
                                        <button type="button" class="btn btn-warning" onclick="logoutUser()">
                                            <i class="fas fa-sign-out-alt"></i> تسجيل خروج
                                        </button>
                                    </div>
                                </form>
                            </div>

                            <!-- System Maintenance -->
                            <div class="system-maintenance-section" style="border-top: 1px solid #eee; padding-top: 20px; margin-top: 20px;">
                                <h4><i class="fas fa-tools"></i> صيانة النظام</h4>
                                <div class="maintenance-actions" style="display: flex; gap: 10px; flex-wrap: wrap;">
                                    <button type="button" class="btn btn-info btn-sm" onclick="fixCorruptedData()">
                                        <i class="fas fa-wrench"></i> إصلاح البيانات التالفة
                                    </button>
                                    <button type="button" class="btn btn-warning btn-sm" onclick="resetUserManagement()">
                                        <i class="fas fa-undo"></i> إعادة تعيين المستخدمين
                                    </button>
                                    <button type="button" class="btn btn-success btn-sm" onclick="loadUsersTable()">
                                        <i class="fas fa-refresh"></i> تحديث الجدول
                                    </button>
                                    <button type="button" class="btn btn-primary btn-sm" onclick="testAddUser()">
                                        <i class="fas fa-user-plus"></i> اختبار إضافة مستخدم
                                    </button>
                                    <button type="button" class="btn btn-secondary btn-sm" onclick="debugUsersAndPasswords()">
                                        <i class="fas fa-bug"></i> تشخيص كلمات المرور
                                    </button>
                                    <button type="button" class="btn btn-danger btn-sm" onclick="resetAdminPassword()">
                                        <i class="fas fa-key"></i> إعادة تعيين كلمة مرور المدير
                                    </button>
                                    <button type="button" class="btn btn-dark btn-sm" onclick="testLogin()">
                                        <i class="fas fa-sign-in-alt"></i> اختبار تسجيل الدخول
                                    </button>

                                    <button type="button" class="btn btn-warning btn-sm" onclick="fixAllUserPasswords()">
                                        <i class="fas fa-tools"></i> إصلاح كلمات المرور
                                    </button>
                                    <button type="button" class="btn btn-info btn-sm" onclick="checkLocalStorage()">
                                        <i class="fas fa-database"></i> فحص التخزين المحلي
                                    </button>
                                    <button type="button" class="btn btn-success btn-sm" onclick="createSimpleTestUser()">
                                        <i class="fas fa-user-plus"></i> إنشاء مستخدم بسيط
                                    </button>
                                </div>
                                <small style="color: #666; display: block; margin-top: 10px;">
                                    <i class="fas fa-info-circle"></i>
                                    استخدم هذه الأدوات في حالة وجود مشاكل في بيانات المستخدمين أو تسجيل الدخول
                                </small>
                            </div>
                        </div>
                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Financial Settings -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <h3><i class="fas fa-calculator"></i> الإعدادات المالية</h3>
                        </div>
                        <div class="settings-card-body">
                            <form id="financialSettingsForm">
                                <div class="form-grid">
                                    <div class="form-group">
                                        <label for="taxRate">نسبة الضريبة (%)</label>
                                        <input type="number" id="taxRate" name="taxRate" min="0" max="100" step="0.01" value="15" placeholder="15.00">
                                    </div>
                                    <div class="form-group">
                                        <label for="appLanguage">لغة التطبيق</label>
                                        <select id="appLanguage" name="appLanguage" onchange="changeAppLanguage(this.value)">
                                            <option value="ar">العربية</option>
                                            <option value="en">English</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-save"></i> حفظ الإعدادات المالية
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Documents Management -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <h3><i class="fas fa-file-upload"></i> إدارة الوثائق</h3>
                        </div>
                        <div class="settings-card-body">
                            <div class="documents-section">
                                <div class="upload-area" onclick="document.getElementById('documentUpload').click()">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <p>انقر لرفع الوثائق</p>
                                    <small>PDF, DOC, DOCX, JPG, PNG (حد أقصى 10MB)</small>
                                </div>
                                <input type="file" id="documentUpload" multiple accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" style="display: none;" onchange="handleDocumentUpload(event)">

                                <div class="documents-list" id="documentsList">
                                    <!-- Documents will be displayed here -->
                                </div>
                            </div>
                        </div>
                    </div>






                    <!-- Firebase Cloud Storage -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <h3><i class="fas fa-cloud"></i> التخزين السحابي - Firebase</h3>
                            <p style="margin: 5px 0 0 0; font-size: 12px; color: #666;">مزامنة تلقائية فورية بين جميع الأجهزة</p>
                        </div>
                        <div class="settings-card-body">
                            <div class="cloud-info">
                                <div class="info-item">
                                    <i class="fas fa-wifi"></i>
                                    <span>حالة Firebase:</span>
                                    <div id="firebaseStatus" class="connection-status-inline">
                                        <span class="status-text offline">جاري التحقق...</span>
                                    </div>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-database"></i>
                                    <span>قاعدة البيانات: Firestore</span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-clock"></i>
                                    <span>آخر مزامنة: <span id="lastFirebaseSync">لم يتم بعد</span></span>
                                </div>
                            </div>

                            <div class="cloud-actions">
                                <button type="button" class="btn btn-success" onclick="syncToFirebase()">
                                    <i class="fas fa-cloud-upload-alt"></i> رفع البيانات للسحابة
                                </button>
                                <button type="button" class="btn btn-info" onclick="syncFromFirebase()">
                                    <i class="fas fa-cloud-download-alt"></i> تحميل البيانات من السحابة
                                </button>
                                <button type="button" class="btn btn-warning" onclick="checkFirebaseStatus()">
                                    <i class="fas fa-heartbeat"></i> فحص حالة Firebase
                                </button>
                                <button type="button" class="btn btn-secondary" onclick="testFirebaseSync()">
                                    <i class="fas fa-vial"></i> اختبار المزامنة
                                </button>
                            </div>

                            <div class="cloud-info-note" style="margin-top: 15px; padding: 10px; background-color: #e8f5e8; border-radius: 5px; border-left: 4px solid #28a745;">
                                <p style="margin: 0; font-size: 14px;">
                                    <i class="fas fa-rocket" style="color: #28a745;"></i>
                                    <strong>Firebase Firestore:</strong> مزامنة فورية وآمنة
                                </p>
                                <p style="margin: 5px 0 0 0; font-size: 12px; color: #666;">
                                    <i class="fas fa-sync-alt"></i> مزامنة تلقائية: البيانات متاحة فوراً على جميع الأجهزة
                                </p>
                                <p style="margin: 5px 0 0 0; font-size: 12px; color: #666;">
                                    <i class="fas fa-shield-alt"></i> آمان عالي: تشفير وحماية متقدمة
                                </p>
                                <p style="margin: 5px 0 0 0; font-size: 12px; color: #666;">
                                    <i class="fas fa-globe"></i> وصول عالمي: من أي مكان وأي جهاز
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Logo Settings -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <h3><i class="fas fa-image"></i> شعار الشركة</h3>
                        </div>
                        <div class="settings-card-body">
                            <div class="logo-section">
                                <div class="current-logo" id="currentLogo">
                                    <div class="logo-placeholder">
                                        <i class="fas fa-building"></i>
                                        <p>لا يوجد شعار محدد</p>
                                    </div>
                                </div>
                                <div class="logo-upload">
                                    <button type="button" class="btn btn-primary" onclick="document.getElementById('logoUpload').click()">
                                        <i class="fas fa-upload"></i> رفع شعار جديد
                                    </button>
                                    <input type="file" id="logoUpload" accept="image/*" style="display: none;" onchange="handleLogoUpload(event)">
                                    <button type="button" class="btn btn-danger" onclick="removeLogo()" id="removeLogoBtn" style="display: none;">
                                        <i class="fas fa-trash"></i> حذف الشعار
                                    </button>
                                </div>
                                <small class="upload-note">يُفضل استخدام صور بصيغة PNG أو JPG بحجم 200x200 بكسل</small>
                            </div>
                        </div>
                    </div>

                    <!-- Date Settings -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <h3><i class="fas fa-calendar-alt"></i> إعدادات التاريخ</h3>
                        </div>
                        <div class="settings-card-body">
                            <form id="dateSettingsForm">
                                <div class="form-group">
                                    <label for="dateFormat">نوع التقويم المستخدم</label>
                                    <select id="dateFormat" name="dateFormat">
                                        <option value="gregorian" selected>التقويم الميلادي</option>
                                        <option value="hijri">التقويم الهجري</option>
                                    </select>
                                </div>
                                <div class="date-preview">
                                    <p><strong>معاينة التاريخ الحالي:</strong></p>
                                    <div id="datePreview" class="date-display">
                                        <!-- Date preview will be shown here -->
                                    </div>
                                </div>
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> حفظ إعدادات التاريخ
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Theme Settings -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <h3><i class="fas fa-palette"></i> مظهر الألوان</h3>
                        </div>
                        <div class="settings-card-body">
                            <form id="themeSettingsForm">
                                <div class="form-group">
                                    <label for="colorTheme">اختر مظهر الألوان</label>
                                    <select id="colorTheme" name="colorTheme">
                                        <option value="default">المظهر الافتراضي (أزرق-بنفسجي)</option>
                                        <option value="ocean">مظهر المحيط (أزرق-أخضر)</option>
                                        <option value="sunset">مظهر الغروب (برتقالي-وردي)</option>
                                        <option value="forest">مظهر الغابة (أخضر-أزرق داكن)</option>
                                        <option value="royal">مظهر ملكي (بنفسجي-ذهبي)</option>
                                        <option value="dark">المظهر الداكن (رمادي-أسود)</option>
                                        <option value="professional">المظهر المهني (أزرق-رمادي)</option>
                                    </select>
                                </div>
                                <div class="theme-preview" id="themePreview">
                                    <div class="preview-card">
                                        <div class="preview-header">معاينة المظهر</div>
                                        <div class="preview-stats">
                                            <div class="preview-stat">
                                                <div class="preview-icon"></div>
                                                <div class="preview-content">
                                                    <h4>123</h4>
                                                    <p>إجمالي المنتجات</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> تطبيق المظهر
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="resetTheme()">
                                        <i class="fas fa-undo"></i> إعادة تعيين
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Developer Tools -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <h3><i class="fas fa-code"></i> أدوات المطورين</h3>
                        </div>
                        <div class="settings-card-body">
                            <div class="developer-actions">
                                <button type="button" class="btn btn-info" onclick="testProductSpecifications()">
                                    <i class="fas fa-search"></i> فحص مواصفات المنتجات
                                </button>
                                <button type="button" class="btn btn-success" onclick="addSampleSpecificationsToProducts()">
                                    <i class="fas fa-plus"></i> إضافة مواصفات تجريبية
                                </button>
                                <button type="button" class="btn btn-warning" onclick="testLogoInExport()">
                                    <i class="fas fa-image"></i> اختبار الشعار في التصدير
                                </button>
                                <button type="button" class="btn btn-primary" onclick="testProductFilters()">
                                    <i class="fas fa-filter"></i> اختبار فلاتر المنتجات
                                </button>
                                <button type="button" class="btn btn-warning" onclick="testTotalCapacityCalculation()">
                                    <i class="fas fa-calculator"></i> اختبار حساب السعة الإجمالية
                                </button>

                                <small class="developer-note">
                                    <i class="fas fa-info-circle"></i>
                                    هذه الأدوات مخصصة لاختبار وتشخيص المشاكل. تحقق من وحدة التحكم (F12) لرؤية النتائج.
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- System Actions -->
                    <div class="settings-card">
                        <div class="settings-card-header">
                            <h3><i class="fas fa-tools"></i> إجراءات النظام</h3>
                        </div>
                        <div class="settings-card-body">
                            <div class="system-actions">
                                <button type="button" class="btn btn-info" onclick="exportAllData()">
                                    <i class="fas fa-download"></i> تصدير جميع البيانات
                                </button>
                                <button type="button" class="btn btn-warning" onclick="clearCache()">
                                    <i class="fas fa-broom"></i> مسح ذاكرة التخزين المؤقت
                                </button>

                                <button type="button" class="btn btn-info" onclick="showSplashScreenNow()">
                                    <i class="fas fa-play"></i> عرض الشاشة الافتتاحية
                                </button>
                                <button type="button" class="btn btn-danger" onclick="resetAllSettings()">
                                    <i class="fas fa-undo"></i> إعادة تعيين جميع الإعدادات
                                </button>
                            </div>
                        </div>
                    </div>


                </div>
            </section>
        </div>
    </main>

    <!-- Fixed Footer -->
    <footer class="fixed-footer">
        <div class="copyright-text">
            جميع الحقوق محفوظة لمطور التطبيق <strong>كريم واصل</strong> /
            <span class="phone-numbers">01022225982 - 0594347807</span>
            لصالح <strong>شركة النسور الماسية للتجارة</strong> 2025
        </div>
    </footer>

    <!-- Modals will be added here -->
    <div id="modalContainer"></div>



    <!-- Floating Calculator -->
    <div id="floatingCalculator" class="floating-calculator" style="display: none;">
        <div class="calculator-header">
            <h4><i class="fas fa-calculator"></i> الآلة الحاسبة</h4>
            <button class="close-calculator" onclick="hideFloatingCalculator()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="calculator-body">
            <div class="calculator-display">
                <input type="text" id="calculatorDisplay" readonly placeholder="0">
            </div>
            <div class="calculator-buttons">
                <div class="calculator-row">
                    <button class="calc-btn calc-clear" onclick="clearCalculator()">C</button>
                    <button class="calc-btn calc-clear" onclick="clearEntry()">CE</button>
                    <button class="calc-btn calc-operator" onclick="deleteLast()">⌫</button>
                    <button class="calc-btn calc-operator" onclick="appendToDisplay('/')">/</button>
                </div>
                <div class="calculator-row">
                    <button class="calc-btn calc-number" onclick="appendToDisplay('7')">7</button>
                    <button class="calc-btn calc-number" onclick="appendToDisplay('8')">8</button>
                    <button class="calc-btn calc-number" onclick="appendToDisplay('9')">9</button>
                    <button class="calc-btn calc-operator" onclick="appendToDisplay('*')">×</button>
                </div>
                <div class="calculator-row">
                    <button class="calc-btn calc-number" onclick="appendToDisplay('4')">4</button>
                    <button class="calc-btn calc-number" onclick="appendToDisplay('5')">5</button>
                    <button class="calc-btn calc-number" onclick="appendToDisplay('6')">6</button>
                    <button class="calc-btn calc-operator" onclick="appendToDisplay('-')">-</button>
                </div>
                <div class="calculator-row">
                    <button class="calc-btn calc-number" onclick="appendToDisplay('1')">1</button>
                    <button class="calc-btn calc-number" onclick="appendToDisplay('2')">2</button>
                    <button class="calc-btn calc-number" onclick="appendToDisplay('3')">3</button>
                    <button class="calc-btn calc-operator" onclick="appendToDisplay('+')">+</button>
                </div>
                <div class="calculator-row">
                    <button class="calc-btn calc-number calc-zero" onclick="appendToDisplay('0')">0</button>
                    <button class="calc-btn calc-number" onclick="appendToDisplay('.')">.</button>
                    <button class="calc-btn calc-equals" onclick="calculateResult()">=</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts with Cache Busting -->

    <!-- Firebase Configuration -->
    <script src="firebase-config.js?v=2024.12.12.001"></script>

    <script src="script.js?v=2024.12.12.001"></script>



    <!-- API Integration System -->
    <script src="api-integration.js?v=2024.12.12.001"></script>



    <!-- User Management System -->
    <script src="user-management.js?v=2024.12.12.001"></script>

    <!-- Permissions Control System -->
    <script src="permissions-control.js?v=2024.12.12.001"></script>

    <!-- Advanced Permissions System -->
    <script src="permissions-system.js?v=2024.12.12.001"></script>

    <!-- Sync Coordinator - Must load first -->
    <script src="sync-coordinator.js?v=2024.12.12.001"></script>

    <!-- Realtime Sync System -->
    <script src="realtime-sync-system.js?v=2024.12.12.001"></script>

    <!-- Enhanced Notification Manager -->
    <script src="notification-manager.js?v=2024.12.12.001"></script>

    <!-- UI Stability Manager -->
    <script src="ui-stability-manager.js?v=2024.12.12.001"></script>

    <!-- Products Sync System -->
    <script src="products-sync.js?v=2024.12.12.001"></script>

    <!-- Simple Sync Fix - WORKS GUARANTEED -->
    <script src="simple-sync-fix.js?v=2024.12.12.002"></script>

    <script>
        // Legacy function - removed (using interactive notifications hub instead)

        // Interactive Notifications Hub
        let notificationsHistory = [];
        let notificationsPanelOpen = false;

        function toggleNotificationsPanel() {
            const panel = document.getElementById('notificationsPanel');
            const hub = document.getElementById('notificationsHub');

            notificationsPanelOpen = !notificationsPanelOpen;

            if (notificationsPanelOpen) {
                panel.classList.add('active');
                hub.style.transform = 'translateY(-2px) scale(1.05)';
                updateNotificationsPanel();
            } else {
                panel.classList.remove('active');
                hub.style.transform = '';
            }
        }

        function updateNotificationsPanel() {
            const content = document.getElementById('notificationsPanelContent');
            const stats = document.getElementById('notificationStats');

            if (notificationsHistory.length === 0) {
                content.innerHTML = `
                    <div class="no-notifications">
                        <i class="fas fa-bell-slash"></i>
                        <p>لا توجد إشعارات</p>
                    </div>
                `;
                stats.textContent = '0 إشعارات نشطة';
            } else {
                const recentNotifications = notificationsHistory.slice(-10).reverse();
                content.innerHTML = recentNotifications.map(notification => `
                    <div class="panel-notification ${notification.type}">
                        <div class="notification-header">
                            <div class="notification-icon ${notification.type}">
                                <i class="fas ${getNotificationIcon(notification.type)}"></i>
                            </div>
                            <div class="notification-time">${formatTime(notification.timestamp)}</div>
                        </div>
                        <p class="notification-message">${notification.message}</p>
                    </div>
                `).join('');

                stats.textContent = `${notificationsHistory.length} إشعارات`;
            }
        }

        function addNotificationToHistory(message, type) {
            const notification = {
                id: Date.now(),
                message: message,
                type: type,
                timestamp: Date.now()
            };

            notificationsHistory.push(notification);

            // Keep only last 50 notifications
            if (notificationsHistory.length > 50) {
                notificationsHistory = notificationsHistory.slice(-50);
            }

            updateNotificationHub();

            if (notificationsPanelOpen) {
                updateNotificationsPanel();
            }
        }

        function updateNotificationHub() {
            const count = document.getElementById('notificationCount');
            const dot = document.getElementById('notificationStatusDot');
            const hub = document.getElementById('notificationsHub');

            const recentCount = notificationsHistory.filter(n =>
                Date.now() - n.timestamp < 300000 // Last 5 minutes
            ).length;

            if (recentCount > 0) {
                count.textContent = recentCount > 99 ? '99+' : recentCount;
                count.classList.add('active');
                count.classList.add('pulse');
                setTimeout(() => count.classList.remove('pulse'), 800);

                // Add wave animation for new notifications
                hub.classList.add('has-notifications');

                const hasErrors = notificationsHistory.some(n =>
                    n.type === 'error' && Date.now() - n.timestamp < 300000
                );

                if (hasErrors) {
                    dot.className = 'notification-status-dot has-errors';
                } else {
                    dot.className = 'notification-status-dot has-notifications';
                }
            } else {
                count.classList.remove('active');
                dot.className = 'notification-status-dot';
                hub.classList.remove('has-notifications');
            }
        }

        function getNotificationIcon(type) {
            const icons = {
                success: 'fa-check',
                error: 'fa-times',
                warning: 'fa-exclamation',
                info: 'fa-info',
                sync: 'fa-sync-alt'
            };
            return icons[type] || 'fa-bell';
        }

        function formatTime(timestamp) {
            const now = Date.now();
            const diff = now - timestamp;

            if (diff < 60000) return 'الآن';
            if (diff < 3600000) return `${Math.floor(diff / 60000)} د`;
            if (diff < 86400000) return `${Math.floor(diff / 3600000)} س`;
            return new Date(timestamp).toLocaleDateString('ar-SA');
        }

        function clearAllNotifications() {
            notificationsHistory = [];
            updateNotificationHub();
            updateNotificationsPanel();

            if (window.notificationManager && window.notificationManager.clearAll) {
                window.notificationManager.clearAll();
            }
        }

        // Close panel when clicking outside
        document.addEventListener('click', function(event) {
            const panel = document.getElementById('notificationsPanel');
            const hub = document.getElementById('notificationsHub');

            if (notificationsPanelOpen && !panel.contains(event.target) && !hub.contains(event.target)) {
                toggleNotificationsPanel();
            }
        });

        // Company Settings Form Handler
        document.addEventListener('DOMContentLoaded', function() {
            const companyForm = document.getElementById('companySettingsForm');
            if (companyForm) {
                companyForm.addEventListener('submit', function(e) {
                    e.preventDefault();
                    saveCompanySettings();
                });
            }
        });

        function saveCompanySettings() {
            const form = document.getElementById('companySettingsForm');
            const formData = new FormData(form);

            const companyData = {
                name: formData.get('companyName'),
                description: formData.get('companyDescription'),
                address: formData.get('companyAddress'),
                phone: formData.get('companyPhone'),
                whatsapp: formData.get('whatsappNumber'),
                supportEmail: formData.get('supportEmail'),
                website: formData.get('companyWebsite'),
                lastUpdated: new Date().toISOString()
            };

            try {
                // Save to localStorage
                localStorage.setItem('companySettings', JSON.stringify(companyData));

                // Show success notification
                if (typeof addNotificationToHistory === 'function') {
                    addNotificationToHistory('تم حفظ بيانات الشركة بنجاح', 'success');
                }

                console.log('✅ تم حفظ بيانات الشركة:', companyData);

                // Update UI if needed
                updateCompanyDisplay();

            } catch (error) {
                console.error('❌ خطأ في حفظ بيانات الشركة:', error);

                if (typeof addNotificationToHistory === 'function') {
                    addNotificationToHistory('فشل في حفظ بيانات الشركة', 'error');
                }
            }
        }

        function loadCompanySettings() {
            try {
                const savedData = localStorage.getItem('companySettings');
                if (savedData) {
                    const companyData = JSON.parse(savedData);

                    // Fill form fields
                    document.getElementById('companyName').value = companyData.name || '';
                    document.getElementById('companyDescription').value = companyData.description || '';
                    document.getElementById('companyAddress').value = companyData.address || '';
                    document.getElementById('companyPhone').value = companyData.phone || '';
                    document.getElementById('whatsappNumber').value = companyData.whatsapp || '';
                    document.getElementById('supportEmail').value = companyData.supportEmail || '';
                    document.getElementById('companyWebsite').value = companyData.website || '';

                    console.log('✅ تم تحميل بيانات الشركة المحفوظة');
                }
            } catch (error) {
                console.error('❌ خطأ في تحميل بيانات الشركة:', error);
            }
        }

        function updateCompanyDisplay() {
            // Update company name in header if exists
            const companyNameElement = document.querySelector('.company-name');
            if (companyNameElement) {
                const savedData = localStorage.getItem('companySettings');
                if (savedData) {
                    const companyData = JSON.parse(savedData);
                    companyNameElement.textContent = companyData.name || 'شركة النسور الماسية للتجارة';
                }
            }
        }

        // Initialize products notifications button state
        function initProductsNotificationButton() {
            const button = document.getElementById('productsNotificationToggle');
            const icon = button.querySelector('i');

            if (window.notificationManager && window.notificationManager.productsNotificationsEnabled) {
                icon.style.color = '#28a745'; // Green
                button.title = 'إيقاف إشعارات المنتجات';
            } else {
                icon.style.color = '#dc3545'; // Red
                button.title = 'تشغيل إشعارات المنتجات';
            }
        }

        // Legacy functions removed - using interactive notifications hub instead

        // Check for sync triggers - PAGE LOAD ONLY
        function checkAutoSyncTriggers() {
            console.log('📄 فحص محفزات المزامنة - فقط عند تحميل الصفحة');

            const triggerImmediateSync = localStorage.getItem('triggerImmediateSync');
            const syncOnLogin = localStorage.getItem('syncOnLogin');

            // Only sync once on page load if triggered
            if (triggerImmediateSync === 'true' || syncOnLogin === 'true') {
                console.log('🚀 تنفيذ مزامنة واحدة عند تحميل الصفحة');

                // Clear the triggers
                localStorage.removeItem('triggerImmediateSync');
                localStorage.removeItem('syncOnLogin');

                // Single sync on page load
                setTimeout(() => {
                    console.log('📄 مزامنة تحميل الصفحة...');
                    if (window.syncCoordinator) {
                        window.syncCoordinator.coordinatedSync('login');
                    } else {
                        quickSyncProducts();
                    }
                }, 2000);
            }

            // Disable continuous auto sync
            localStorage.removeItem('enableAutoSync');
            console.log('🚫 تم تعطيل المزامنة المستمرة - المزامنة فقط عند تحديث الصفحة');
        }

        // Continuous auto sync - COMPLETELY DISABLED
        function startContinuousAutoSync() {
            console.log('🚫 المزامنة المستمرة معطلة تماماً');
            console.log('📄 المزامنة ستحدث فقط عند تحديث الصفحة');

            // No continuous sync at all
            // Only page load sync is allowed

            // Event listeners - DISABLED (handled by sync coordinator)
            console.log('⚠️ مستمعي أحداث المزامنة معطلين - يتم التحكم بواسطة منسق المزامنة');

            // Note: Focus and visibility change events are now handled by sync coordinator
            // to prevent multiple conflicting sync operations
        }

        // Quick sync products - STABLE AND GUARANTEED TO WORK
        async function quickSyncProducts() {
            console.log('🚀 بدء المزامنة السريعة المستقرة...');

            // Check if UI updates are in progress
            if (window.uiStabilityManager) {
                const status = window.uiStabilityManager.getStatus();
                if (status.pendingUpdates > 0) {
                    console.log('⏸️ تأجيل المزامنة - تحديثات الواجهة قيد التنفيذ');
                    return false;
                }
            }

            // Use the simple sync system
            if (window.SIMPLE_SYNC && window.SIMPLE_SYNC.syncProductsNow) {
                console.log('✅ استخدام النظام البسيط للمزامنة...');

                // Clear any pending UI updates before sync
                if (window.uiStabilityManager) {
                    window.uiStabilityManager.clearPendingUpdates();
                }

                const result = await window.SIMPLE_SYNC.syncProductsNow();

                if (result) {
                    console.log('✅ نجحت المزامنة بالنظام البسيط');

                    // Ensure UI stability after sync
                    if (window.uiStabilityManager) {
                        setTimeout(() => {
                            window.uiStabilityManager.ensureUIStability();
                        }, 1000);
                    }
                } else {
                    console.log('❌ فشلت المزامنة بالنظام البسيط');
                }

                return result;
            }

            // Fallback to force sync function
            if (window.forceSyncNow && typeof window.forceSyncNow === 'function') {
                console.log('🔄 استخدام وظيفة المزامنة القسرية...');
                return await window.forceSyncNow();
            }

            // Last resort - manual sync
            console.log('⚠️ استخدام المزامنة اليدوية كحل أخير...');
            if (typeof utils !== 'undefined' && utils.showNotification) {
                utils.showNotification('جاري المزامنة اليدوية...', 'warning');
            }

            try {
                // Get local products
                let localProducts = [];
                const productsData = localStorage.getItem('inventory_products');
                if (productsData) {
                    localProducts = JSON.parse(productsData);
                }

                console.log(`📱 المنتجات المحلية: ${localProducts.length}`);

                // Check Firebase
                if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
                    if (typeof utils !== 'undefined' && utils.showNotification) {
                        utils.showNotification('Firebase غير متاح للمزامنة', 'error');
                    }
                    return false;
                }

                // Upload to Firebase
                if (localProducts.length > 0) {
                    const uploadResult = await window.firebaseService.saveProducts(localProducts);
                    if (uploadResult) {
                        console.log('✅ تم رفع المنتجات');
                    }
                }

                // Download from Firebase
                const firebaseProducts = await window.firebaseService.loadProducts();
                if (firebaseProducts && firebaseProducts.length > 0) {
                    localStorage.setItem('inventory_products', JSON.stringify(firebaseProducts));

                    // Safe update of window.products
                    try {
                        if (!window.products || !Array.isArray(window.products)) {
                            window.products = [];
                        }
                        window.products.length = 0;
                        window.products.push(...firebaseProducts);
                        console.log('✅ تم تحديث window.products بأمان');
                    } catch (error) {
                        console.error('❌ خطأ في تحديث window.products:', error);
                        window.products = [...firebaseProducts];
                        console.log('🔧 تم إنشاء window.products جديد');
                    }

                    // Force UI update
                    if (typeof loadProductsTable === 'function') {
                        loadProductsTable();
                    }

                    if (typeof utils !== 'undefined' && utils.showNotification) {
                        utils.showNotification(`تم تحميل ${firebaseProducts.length} منتج`, 'success');
                    }

                    console.log('✅ تمت المزامنة اليدوية بنجاح');
                    return true;
                }

                return false;

            } catch (error) {
                console.error('❌ خطأ في المزامنة اليدوية:', error);
                if (typeof utils !== 'undefined' && utils.showNotification) {
                    utils.showNotification('خطأ في المزامنة: ' + error.message, 'error');
                }
                return false;
            }
        }
    </script>

</body>
</html>
