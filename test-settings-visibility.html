<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار رؤية الإعدادات - النسور الماسية</title>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            font-size: 28px;
        }

        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .test-section h2 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 20px;
        }

        .btn {
            padding: 12px 20px;
            margin: 5px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-family: 'Cairo', sans-serif;
            font-weight: 600;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #117a8b);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #e0a800);
            color: #212529;
        }

        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }

        .log-area {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .clear-log {
            background: #e53e3e;
            color: white;
            padding: 8px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-bottom: 10px;
        }

        .section-tabs {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .tab {
            padding: 10px 20px;
            background: #6c757d;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .tab.active {
            background: #007bff;
        }

        .tab:hover {
            background: #495057;
        }

        .tab.active:hover {
            background: #0056b3;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .result-card {
            background: white;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
        }

        .result-card.success {
            border-color: #28a745;
            background: #d4edda;
        }

        .result-card.error {
            border-color: #dc3545;
            background: #f8d7da;
        }

        .result-card.warning {
            border-color: #ffc107;
            background: #fff3cd;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-eye"></i> اختبار رؤية الإعدادات</h1>

        <!-- Section Navigation -->
        <div class="test-section">
            <h2><i class="fas fa-tabs"></i> التنقل بين الأقسام</h2>
            <div class="section-tabs">
                <button class="tab active" onclick="simulateSection('dashboard')">لوحة التحكم</button>
                <button class="tab" onclick="simulateSection('products')">إدارة المنتجات</button>
                <button class="tab" onclick="simulateSection('customers')">إدارة العملاء</button>
                <button class="tab" onclick="simulateSection('requests')">طلبات العملاء</button>
                <button class="tab" onclick="simulateSection('settings')">الإعدادات</button>
            </div>
            <div id="currentSection" style="font-weight: bold; color: #007bff;">القسم الحالي: لوحة التحكم</div>
        </div>

        <!-- Test Controls -->
        <div class="test-section">
            <h2><i class="fas fa-tools"></i> أدوات الاختبار</h2>
            
            <button class="btn btn-primary" onclick="scanAllSettings()">
                <i class="fas fa-search"></i> فحص جميع الإعدادات
            </button>
            
            <button class="btn btn-info" onclick="testSectionVisibility()">
                <i class="fas fa-eye"></i> اختبار رؤية الأقسام
            </button>
            
            <button class="btn btn-success" onclick="forceCleanup()">
                <i class="fas fa-broom"></i> تنظيف إجباري
            </button>
            
            <button class="btn btn-warning" onclick="resetAllStyles()">
                <i class="fas fa-undo"></i> إعادة تعيين الأنماط
            </button>
            
            <button class="btn btn-danger" onclick="showAllSettings()">
                <i class="fas fa-eye-slash"></i> إظهار جميع الإعدادات (للاختبار)
            </button>
        </div>

        <!-- Results -->
        <div class="test-section">
            <h2><i class="fas fa-clipboard-list"></i> نتائج الاختبار</h2>
            <button class="clear-log" onclick="clearLog()">مسح السجل</button>
            <div id="testLog" class="log-area"></div>
        </div>

        <!-- Live Results -->
        <div class="test-section">
            <h2><i class="fas fa-chart-bar"></i> النتائج المباشرة</h2>
            <div id="liveResults" class="results-grid"></div>
        </div>
    </div>

    <!-- Load Required Scripts -->
    <script src="style.css" type="text/css"></script>
    <script src="script.js"></script>

    <script>
        let testLog = document.getElementById('testLog');
        let liveResults = document.getElementById('liveResults');
        let currentSectionName = 'dashboard';

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
            testLog.innerHTML += `<div>[${timestamp}] ${icon} ${message}</div>`;
            testLog.scrollTop = testLog.scrollHeight;
        }

        function clearLog() {
            testLog.innerHTML = '';
        }

        function updateCurrentSection(sectionName) {
            currentSectionName = sectionName;
            document.getElementById('currentSection').textContent = `القسم الحالي: ${getSectionDisplayName(sectionName)}`;
            
            // Update tab states
            document.querySelectorAll('.tab').forEach(tab => tab.classList.remove('active'));
            event.target.classList.add('active');
        }

        function getSectionDisplayName(sectionName) {
            const names = {
                'dashboard': 'لوحة التحكم',
                'products': 'إدارة المنتجات',
                'customers': 'إدارة العملاء',
                'requests': 'طلبات العملاء',
                'settings': 'الإعدادات'
            };
            return names[sectionName] || sectionName;
        }

        function simulateSection(sectionName) {
            log(`🔄 محاكاة التنقل إلى قسم: ${getSectionDisplayName(sectionName)}`);
            updateCurrentSection(sectionName);
            
            // Simulate the hideSettingsInOtherSections function
            if (typeof hideSettingsInOtherSections === 'function') {
                hideSettingsInOtherSections(sectionName);
            } else {
                log('❌ وظيفة hideSettingsInOtherSections غير موجودة', 'error');
            }
            
            // Update live results
            setTimeout(() => {
                updateLiveResults();
            }, 500);
        }

        function scanAllSettings() {
            log('🔍 فحص جميع عناصر الإعدادات...');
            
            const settingsCards = document.querySelectorAll('.settings-card');
            const settingsContainers = document.querySelectorAll('.settings-container');
            
            log(`📊 العثور على ${settingsCards.length} بطاقة إعدادات و ${settingsContainers.length} حاوية إعدادات`);
            
            settingsCards.forEach((card, index) => {
                const parentSection = card.closest('.section');
                const isVisible = card.offsetHeight > 0 && card.offsetWidth > 0;
                const computedStyle = getComputedStyle(card);
                const displayStyle = computedStyle.display;
                const visibilityStyle = computedStyle.visibility;
                
                log(`📋 بطاقة #${index + 1}: القسم=${parentSection ? parentSection.id : 'غير محدد'}, مرئية=${isVisible}, display=${displayStyle}, visibility=${visibilityStyle}`);
            });
        }

        function testSectionVisibility() {
            log('👁️ اختبار رؤية الأقسام...');
            
            const sections = ['dashboard', 'products', 'customers', 'requests', 'settings'];
            
            sections.forEach(sectionName => {
                const section = document.getElementById(sectionName);
                if (section) {
                    const settingsInSection = section.querySelectorAll('.settings-card, .settings-container');
                    const visibleSettings = Array.from(settingsInSection).filter(el => 
                        el.offsetHeight > 0 && el.offsetWidth > 0 && 
                        getComputedStyle(el).display !== 'none'
                    );
                    
                    if (sectionName === 'settings') {
                        if (visibleSettings.length > 0) {
                            log(`✅ قسم ${getSectionDisplayName(sectionName)}: ${visibleSettings.length} إعدادات مرئية (صحيح)`, 'success');
                        } else {
                            log(`❌ قسم ${getSectionDisplayName(sectionName)}: لا توجد إعدادات مرئية (خطأ)`, 'error');
                        }
                    } else {
                        if (visibleSettings.length === 0) {
                            log(`✅ قسم ${getSectionDisplayName(sectionName)}: لا توجد إعدادات مرئية (صحيح)`, 'success');
                        } else {
                            log(`❌ قسم ${getSectionDisplayName(sectionName)}: ${visibleSettings.length} إعدادات مرئية (خطأ)`, 'error');
                        }
                    }
                } else {
                    log(`⚠️ قسم ${getSectionDisplayName(sectionName)} غير موجود`, 'warning');
                }
            });
        }

        function forceCleanup() {
            log('🧹 تنفيذ تنظيف إجباري...');
            
            if (typeof cleanupSettingsDisplay === 'function') {
                cleanupSettingsDisplay();
                log('✅ تم تنفيذ تنظيف الإعدادات', 'success');
            } else {
                log('❌ وظيفة cleanupSettingsDisplay غير موجودة', 'error');
            }
            
            setTimeout(() => {
                updateLiveResults();
            }, 500);
        }

        function resetAllStyles() {
            log('🔄 إعادة تعيين جميع الأنماط...');
            
            const allSettings = document.querySelectorAll('.settings-card, .settings-container');
            allSettings.forEach(element => {
                element.style.cssText = '';
                element.classList.remove('force-hidden');
            });
            
            document.body.classList.remove('settings-active');
            document.documentElement.classList.remove('settings-cleaned');
            
            log('✅ تم إعادة تعيين جميع الأنماط', 'success');
            
            setTimeout(() => {
                updateLiveResults();
            }, 500);
        }

        function showAllSettings() {
            log('👁️ إظهار جميع الإعدادات للاختبار...');
            
            const allSettings = document.querySelectorAll('.settings-card, .settings-container');
            allSettings.forEach(element => {
                element.style.display = 'block';
                element.style.visibility = 'visible';
                element.style.opacity = '1';
                element.classList.remove('force-hidden');
            });
            
            log('✅ تم إظهار جميع الإعدادات', 'success');
            
            setTimeout(() => {
                updateLiveResults();
            }, 500);
        }

        function updateLiveResults() {
            const sections = ['dashboard', 'products', 'customers', 'requests', 'settings'];
            liveResults.innerHTML = '';
            
            sections.forEach(sectionName => {
                const section = document.getElementById(sectionName);
                if (section) {
                    const settingsInSection = section.querySelectorAll('.settings-card, .settings-container');
                    const visibleSettings = Array.from(settingsInSection).filter(el => 
                        el.offsetHeight > 0 && el.offsetWidth > 0 && 
                        getComputedStyle(el).display !== 'none'
                    );
                    
                    const card = document.createElement('div');
                    card.className = 'result-card';
                    
                    if (sectionName === 'settings') {
                        card.className += visibleSettings.length > 0 ? ' success' : ' error';
                    } else {
                        card.className += visibleSettings.length === 0 ? ' success' : ' error';
                    }
                    
                    card.innerHTML = `
                        <h3>${getSectionDisplayName(sectionName)}</h3>
                        <p>إجمالي الإعدادات: ${settingsInSection.length}</p>
                        <p>الإعدادات المرئية: ${visibleSettings.length}</p>
                        <p>الحالة: ${
                            (sectionName === 'settings' && visibleSettings.length > 0) || 
                            (sectionName !== 'settings' && visibleSettings.length === 0) 
                            ? '✅ صحيح' : '❌ خطأ'
                        }</p>
                    `;
                    
                    liveResults.appendChild(card);
                }
            });
        }

        // Initialize test
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 بدء اختبار رؤية الإعدادات...');
            
            setTimeout(() => {
                scanAllSettings();
                testSectionVisibility();
                updateLiveResults();
            }, 1000);
        });
    </script>
</body>
</html>
