<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>✅ تم تغيير كلمة المرور - النسور الماسية</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .success-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            max-width: 500px;
            width: 90%;
            text-align: center;
        }

        .success-icon {
            font-size: 64px;
            color: #28a745;
            margin-bottom: 20px;
            animation: bounce 1s ease-in-out;
        }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0);
            }
            40% {
                transform: translateY(-10px);
            }
            60% {
                transform: translateY(-5px);
            }
        }

        .success-header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }

        .success-header p {
            color: #666;
            font-size: 16px;
            line-height: 1.5;
            margin-bottom: 30px;
        }

        .btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
            text-decoration: none;
            display: inline-block;
            box-sizing: border-box;
        }

        .btn.primary {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .btn.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }

        .btn.secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e1e5e9;
        }

        .btn.secondary:hover {
            background: #e9ecef;
        }

        .countdown {
            margin: 20px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            color: #666;
        }

        .countdown strong {
            color: #28a745;
            font-size: 18px;
        }

        .tips {
            text-align: right;
            margin-top: 30px;
            padding: 20px;
            background: #e8f5e8;
            border-radius: 10px;
            border-left: 4px solid #28a745;
        }

        .tips h3 {
            color: #155724;
            margin-bottom: 15px;
        }

        .tips ul {
            color: #155724;
            line-height: 1.6;
        }

        .tips li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="success-container">
        <div class="success-icon">
            <i class="fas fa-check-circle"></i>
        </div>

        <div class="success-header">
            <h1>تم تغيير كلمة المرور بنجاح!</h1>
            <p>تم تحديث كلمة المرور الخاصة بك بنجاح. يمكنك الآن تسجيل الدخول باستخدام كلمة المرور الجديدة.</p>
        </div>

        <div class="countdown" id="countdown">
            سيتم توجيهك تلقائياً إلى صفحة تسجيل الدخول خلال <strong id="timer">5</strong> ثوان
        </div>

        <a href="login-system.html" class="btn primary">
            <i class="fas fa-sign-in-alt"></i> تسجيل الدخول الآن
        </a>

        <a href="index.html" class="btn secondary">
            <i class="fas fa-home"></i> العودة للصفحة الرئيسية
        </a>

        <div class="tips">
            <h3><i class="fas fa-lightbulb"></i> نصائح أمنية:</h3>
            <ul>
                <li>احتفظ بكلمة المرور في مكان آمن</li>
                <li>لا تشارك كلمة المرور مع أي شخص</li>
                <li>استخدم كلمة مرور قوية تحتوي على أحرف وأرقام</li>
                <li>قم بتغيير كلمة المرور بانتظام</li>
                <li>لا تستخدم نفس كلمة المرور في مواقع أخرى</li>
            </ul>
        </div>
    </div>

    <script>
        // Countdown timer
        let timeLeft = 5;
        const timerElement = document.getElementById('timer');
        
        const countdown = setInterval(() => {
            timeLeft--;
            timerElement.textContent = timeLeft;
            
            if (timeLeft <= 0) {
                clearInterval(countdown);
                window.location.href = 'login-system.html';
            }
        }, 1000);

        // Allow user to cancel auto-redirect by clicking anywhere
        document.addEventListener('click', () => {
            clearInterval(countdown);
            document.getElementById('countdown').style.display = 'none';
        });

        // Log success
        console.log('✅ صفحة نجاح تغيير كلمة المرور');
    </script>
</body>
</html>
