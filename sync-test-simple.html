<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 اختبار المزامنة البسيط - النسور الماسية</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 10px;
        }
        .btn {
            padding: 15px 25px;
            margin: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn.primary { background: #007bff; color: white; }
        .btn.success { background: #28a745; color: white; }
        .btn.warning { background: #ffc107; color: #212529; }
        .btn.danger { background: #dc3545; color: white; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 4px 8px rgba(0,0,0,0.2); }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .log {
            background: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            border: 2px solid #e9ecef;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 اختبار المزامنة البسيط</h1>
            <p>حل نهائي وبسيط لمشكلة مزامنة المنتجات</p>
        </div>

        <!-- Status -->
        <div id="status" class="status info">
            📊 جاري تحميل حالة النظام...
        </div>

        <!-- Stats -->
        <div class="stats" id="stats">
            <!-- Will be populated by JavaScript -->
        </div>

        <!-- Actions -->
        <div style="text-align: center; margin: 30px 0;">
            <button class="btn success" onclick="forceSyncNow()">
                🚀 مزامنة فورية الآن
            </button>
            <button class="btn primary" onclick="checkStatus()">
                📊 فحص الحالة
            </button>
            <button class="btn warning" onclick="addTestProduct()">
                ➕ إضافة منتج تجريبي
            </button>
            <button class="btn danger" onclick="clearLog()">
                🗑️ مسح السجل
            </button>
        </div>

        <!-- Log -->
        <div class="log" id="log">
            <div>[INFO] مرحباً بك في اختبار المزامنة البسيط</div>
            <div>[INFO] جاري تحميل النظام...</div>
        </div>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>
    <script src="firebase-config.js"></script>
    <script src="simple-sync-fix.js"></script>

    <script>
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 تهيئة اختبار المزامنة البسيط...', 'info');
            
            setTimeout(() => {
                checkStatus();
                updateStats();
            }, 2000);
        });

        // Force sync now
        async function forceSyncNow() {
            log('🚀 بدء المزامنة الفورية...', 'info');
            updateStatus('جاري المزامنة...', 'warning');

            try {
                let result = false;

                // Try simple sync system first
                if (window.SIMPLE_SYNC && window.SIMPLE_SYNC.syncProductsNow) {
                    log('✅ استخدام النظام البسيط...', 'info');
                    result = await window.SIMPLE_SYNC.syncProductsNow();
                } else if (window.forceSyncNow && typeof window.forceSyncNow === 'function') {
                    log('🔄 استخدام وظيفة المزامنة القسرية...', 'info');
                    result = await window.forceSyncNow();
                } else {
                    log('⚠️ استخدام المزامنة اليدوية...', 'warning');
                    result = await manualSync();
                }

                if (result) {
                    log('✅ تمت المزامنة بنجاح!', 'success');
                    updateStatus('✅ تمت المزامنة بنجاح', 'success');
                } else {
                    log('❌ فشلت المزامنة', 'error');
                    updateStatus('❌ فشلت المزامنة', 'error');
                }

                updateStats();

            } catch (error) {
                log(`❌ خطأ في المزامنة: ${error.message}`, 'error');
                updateStatus('❌ خطأ في المزامنة', 'error');
            }
        }

        // Manual sync as fallback
        async function manualSync() {
            try {
                // Get local products
                let localProducts = [];
                const productsData = localStorage.getItem('inventory_products');
                if (productsData) {
                    localProducts = JSON.parse(productsData);
                }
                log(`📱 المنتجات المحلية: ${localProducts.length}`, 'info');

                // Check Firebase
                if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
                    log('❌ Firebase غير متاح', 'error');
                    return false;
                }

                // Upload to Firebase
                if (localProducts.length > 0) {
                    log('📤 رفع المنتجات إلى Firebase...', 'info');
                    const uploadResult = await window.firebaseService.saveProducts(localProducts);
                    if (uploadResult) {
                        log('✅ تم رفع المنتجات بنجاح', 'success');
                    } else {
                        log('❌ فشل في رفع المنتجات', 'error');
                    }
                }

                // Download from Firebase
                log('📥 تحميل المنتجات من Firebase...', 'info');
                const firebaseProducts = await window.firebaseService.loadProducts();
                
                if (firebaseProducts && firebaseProducts.length > 0) {
                    log(`📥 تم تحميل ${firebaseProducts.length} منتج من Firebase`, 'success');
                    
                    // Update localStorage
                    localStorage.setItem('inventory_products', JSON.stringify(firebaseProducts));
                    
                    // Update global variable
                    if (typeof window.products !== 'undefined') {
                        window.products.length = 0;
                        window.products.push(...firebaseProducts);
                    }
                    
                    log('✅ تم تحديث البيانات المحلية', 'success');
                    return true;
                } else {
                    log('ℹ️ لا توجد منتجات في Firebase', 'info');
                    return false;
                }

            } catch (error) {
                log(`❌ خطأ في المزامنة اليدوية: ${error.message}`, 'error');
                return false;
            }
        }

        // Check status
        function checkStatus() {
            log('📊 فحص حالة النظام...', 'info');

            const status = {
                firebaseReady: window.firebaseService ? window.firebaseService.isFirebaseReady() : false,
                simpleSyncAvailable: typeof window.SIMPLE_SYNC !== 'undefined',
                forceSyncAvailable: typeof window.forceSyncNow === 'function',
                localProductsCount: 0,
                globalProductsCount: window.products ? window.products.length : 0
            };

            // Get local products count
            try {
                const productsData = localStorage.getItem('inventory_products');
                if (productsData) {
                    status.localProductsCount = JSON.parse(productsData).length;
                }
            } catch (error) {
                log(`❌ خطأ في قراءة المنتجات المحلية: ${error.message}`, 'error');
            }

            // Log status
            log('📊 حالة النظام:', 'info');
            log(`   Firebase: ${status.firebaseReady ? '✅ متاح' : '❌ غير متاح'}`, 'info');
            log(`   النظام البسيط: ${status.simpleSyncAvailable ? '✅ متاح' : '❌ غير متاح'}`, 'info');
            log(`   المزامنة القسرية: ${status.forceSyncAvailable ? '✅ متاح' : '❌ غير متاح'}`, 'info');
            log(`   المنتجات المحلية: ${status.localProductsCount}`, 'info');
            log(`   المنتجات العامة: ${status.globalProductsCount}`, 'info');

            // Update status display
            if (status.firebaseReady && status.simpleSyncAvailable) {
                updateStatus('✅ النظام جاهز للمزامنة', 'success');
            } else if (status.firebaseReady) {
                updateStatus('⚠️ Firebase متاح لكن النظام البسيط غير محمل', 'warning');
            } else {
                updateStatus('❌ Firebase غير متاح', 'error');
            }

            updateStats();
            return status;
        }

        // Add test product
        function addTestProduct() {
            log('➕ إضافة منتج تجريبي...', 'info');

            try {
                // Get existing products
                let products = [];
                const productsData = localStorage.getItem('inventory_products');
                if (productsData) {
                    products = JSON.parse(productsData);
                }

                // Add test product
                const testProduct = {
                    id: Date.now(),
                    manufacturer: 'شركة تجريبية',
                    category: 'فئة تجريبية',
                    availableQuantity: Math.floor(Math.random() * 100) + 1,
                    price: Math.floor(Math.random() * 1000) + 100,
                    addedAt: new Date().toISOString()
                };

                products.push(testProduct);

                // Save to localStorage
                localStorage.setItem('inventory_products', JSON.stringify(products));

                // Update global variable
                if (typeof window.products !== 'undefined') {
                    window.products.push(testProduct);
                }

                log(`✅ تم إضافة منتج تجريبي: ${testProduct.manufacturer}`, 'success');
                updateStats();

            } catch (error) {
                log(`❌ خطأ في إضافة المنتج التجريبي: ${error.message}`, 'error');
            }
        }

        // Update status
        function updateStatus(message, type) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        // Update stats
        function updateStats() {
            const statsEl = document.getElementById('stats');
            
            let localCount = 0;
            try {
                const productsData = localStorage.getItem('inventory_products');
                if (productsData) {
                    localCount = JSON.parse(productsData).length;
                }
            } catch (error) {
                // Ignore error
            }

            const globalCount = window.products ? window.products.length : 0;
            const firebaseReady = window.firebaseService ? window.firebaseService.isFirebaseReady() : false;
            const simpleSyncReady = typeof window.SIMPLE_SYNC !== 'undefined';

            statsEl.innerHTML = `
                <div class="stat-card">
                    <div class="stat-value">${localCount}</div>
                    <div class="stat-label">المنتجات المحلية</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${globalCount}</div>
                    <div class="stat-label">المنتجات العامة</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${firebaseReady ? '✅' : '❌'}</div>
                    <div class="stat-label">Firebase</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${simpleSyncReady ? '✅' : '❌'}</div>
                    <div class="stat-label">النظام البسيط</div>
                </div>
            `;
        }

        // Log function
        function log(message, type = 'info') {
            const logEl = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                info: '#00ff00',
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107'
            };
            
            const logEntry = document.createElement('div');
            logEntry.style.color = colors[type] || colors.info;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            
            logEl.appendChild(logEntry);
            logEl.scrollTop = logEl.scrollHeight;

            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // Clear log
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            log('تم مسح السجل', 'info');
        }
    </script>
</body>
</html>
