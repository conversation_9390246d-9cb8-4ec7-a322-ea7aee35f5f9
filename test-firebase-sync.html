<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار مزامنة Firebase - النسور الماسية</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .status-card {
            background: rgba(255, 255, 255, 0.2);
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 5px solid #ffd700;
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            margin: 10px 5px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        .info { color: #2196F3; }
        #console {
            background: rgba(0, 0, 0, 0.7);
            color: #00ff00;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            height: 300px;
            overflow-y: auto;
            margin-top: 20px;
            white-space: pre-wrap;
        }
        .data-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h1>🔥 اختبار مزامنة Firebase</h1>
        <p>هذه الصفحة لاختبار مزامنة جميع بيانات التطبيق مع Firebase</p>

        <div class="status-card">
            <h3>📊 حالة Firebase</h3>
            <div id="firebase-status">جاري التحقق...</div>
        </div>

        <div class="status-card">
            <h3>🔄 عمليات المزامنة</h3>
            <button class="btn" onclick="syncAllToFirebase()">📤 رفع جميع البيانات</button>
            <button class="btn" onclick="syncAllFromFirebase()">📥 تحميل جميع البيانات</button>
            <button class="btn" onclick="checkSyncStatus()">🔍 فحص حالة المزامنة</button>
            <button class="btn" onclick="testFirebaseConnection()">🧪 اختبار الاتصال</button>
        </div>

        <div class="status-card">
            <h3>📈 إحصائيات البيانات</h3>
            <div id="data-stats">جاري التحميل...</div>
        </div>

        <div class="status-card">
            <h3>🔧 مزامنة فردية</h3>
            <button class="btn" onclick="syncProducts()">📦 مزامنة المنتجات</button>
            <button class="btn" onclick="syncCustomers()">👥 مزامنة العملاء</button>
            <button class="btn" onclick="syncUsers()">🔐 مزامنة المستخدمين</button>
            <button class="btn" onclick="syncSettings()">⚙️ مزامنة الإعدادات</button>
        </div>

        <div class="status-card">
            <h3>🔄 تحديث الواجهة</h3>
            <button class="btn" onclick="forceUIRefresh()">🔄 تحديث الواجهة فوراً</button>
            <button class="btn" onclick="openUIRefreshTool()">🛠️ فتح أداة تحديث الواجهة</button>
        </div>

        <div id="console"></div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore-compat.js"></script>
    
    <!-- Firebase Configuration -->
    <script src="firebase-config.js"></script>

    <script>
        let consoleElement = document.getElementById('console');
        
        // Override console.log to show in page
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.join(' ') + '\n';
            consoleElement.textContent += message;
            consoleElement.scrollTop = consoleElement.scrollHeight;
        };

        // Check Firebase status on load
        window.addEventListener('load', function() {
            setTimeout(checkFirebaseStatus, 1000);
            setTimeout(updateDataStats, 1500);
        });

        function checkFirebaseStatus() {
            const statusDiv = document.getElementById('firebase-status');
            if (window.firebaseService && window.firebaseService.isFirebaseReady()) {
                statusDiv.innerHTML = '<span class="success">✅ Firebase متصل وجاهز</span>';
                console.log('✅ Firebase connection successful');
            } else {
                statusDiv.innerHTML = '<span class="error">❌ Firebase غير متصل</span>';
                console.log('❌ Firebase connection failed');
            }
        }

        function updateDataStats() {
            const statsDiv = document.getElementById('data-stats');
            try {
                const products = JSON.parse(localStorage.getItem('inventory_products') || '[]');
                const customers = JSON.parse(localStorage.getItem('inventory_customers') || '[]');
                const users = JSON.parse(localStorage.getItem('systemUsers') || '[]');
                const settings = JSON.parse(localStorage.getItem('systemSettings') || '{}');
                const credentials = JSON.parse(localStorage.getItem('loginCredentials') || '{}');

                statsDiv.innerHTML = `
                    <div class="data-row">
                        <span>📦 المنتجات:</span>
                        <span class="info">${products.length} عنصر</span>
                    </div>
                    <div class="data-row">
                        <span>👥 العملاء:</span>
                        <span class="info">${customers.length} عميل</span>
                    </div>
                    <div class="data-row">
                        <span>🔐 المستخدمين:</span>
                        <span class="info">${users.length} مستخدم</span>
                    </div>
                    <div class="data-row">
                        <span>⚙️ الإعدادات:</span>
                        <span class="info">${Object.keys(settings).length} إعداد</span>
                    </div>
                    <div class="data-row">
                        <span>🔑 بيانات الدخول:</span>
                        <span class="info">${Object.keys(credentials).length} عنصر</span>
                    </div>
                `;
            } catch (error) {
                statsDiv.innerHTML = '<span class="error">❌ خطأ في قراءة البيانات</span>';
                console.error('Error reading data stats:', error);
            }
        }

        async function syncAllToFirebase() {
            console.log('🔄 بدء رفع جميع البيانات إلى Firebase...');
            try {
                const result = await window.syncToFirebase();
                if (result) {
                    console.log('✅ تم رفع جميع البيانات بنجاح');
                } else {
                    console.log('⚠️ فشل في رفع بعض البيانات');
                }
            } catch (error) {
                console.error('❌ خطأ في رفع البيانات:', error);
            }
        }

        async function syncAllFromFirebase() {
            console.log('🔄 بدء تحميل جميع البيانات من Firebase...');
            try {
                const result = await window.syncFromFirebase();
                if (result) {
                    console.log('✅ تم تحميل جميع البيانات بنجاح');
                    updateDataStats();
                } else {
                    console.log('⚠️ فشل في تحميل بعض البيانات');
                }
            } catch (error) {
                console.error('❌ خطأ في تحميل البيانات:', error);
            }
        }

        async function checkSyncStatus() {
            console.log('🔍 فحص حالة المزامنة...');
            try {
                const status = await window.checkFirebaseSyncStatus();
                if (status) {
                    console.log('📊 تقرير المزامنة مكتمل');
                } else {
                    console.log('❌ فشل في فحص حالة المزامنة');
                }
            } catch (error) {
                console.error('❌ خطأ في فحص المزامنة:', error);
            }
        }

        async function testFirebaseConnection() {
            console.log('🧪 اختبار اتصال Firebase...');
            try {
                if (window.firebaseService && window.firebaseService.isFirebaseReady()) {
                    console.log('✅ Firebase متصل ويعمل بشكل صحيح');
                    
                    // Test write operation
                    const testData = { test: true, timestamp: Date.now() };
                    await window.firebaseService.db.collection('test').doc('connection').set(testData);
                    console.log('✅ اختبار الكتابة نجح');
                    
                    // Test read operation
                    const doc = await window.firebaseService.db.collection('test').doc('connection').get();
                    if (doc.exists) {
                        console.log('✅ اختبار القراءة نجح');
                    }
                    
                    console.log('🎉 جميع اختبارات Firebase نجحت!');
                } else {
                    console.log('❌ Firebase غير متصل');
                }
            } catch (error) {
                console.error('❌ خطأ في اختبار Firebase:', error);
            }
        }

        async function syncProducts() {
            console.log('📦 مزامنة المنتجات...');
            try {
                const result = await window.syncProductsToFirebase();
                console.log(result ? '✅ تم رفع المنتجات' : '❌ فشل رفع المنتجات');
            } catch (error) {
                console.error('❌ خطأ في مزامنة المنتجات:', error);
            }
        }

        async function syncCustomers() {
            console.log('👥 مزامنة العملاء...');
            try {
                const result = await window.syncCustomersToFirebase();
                console.log(result ? '✅ تم رفع العملاء' : '❌ فشل رفع العملاء');
            } catch (error) {
                console.error('❌ خطأ في مزامنة العملاء:', error);
            }
        }

        async function syncUsers() {
            console.log('🔐 مزامنة المستخدمين...');
            try {
                const result = await window.syncUsersToFirebase();
                console.log(result ? '✅ تم رفع المستخدمين' : '❌ فشل رفع المستخدمين');
            } catch (error) {
                console.error('❌ خطأ في مزامنة المستخدمين:', error);
            }
        }

        async function syncSettings() {
            console.log('⚙️ مزامنة الإعدادات...');
            try {
                const result = await window.syncSettingsToFirebase();
                console.log(result ? '✅ تم رفع الإعدادات' : '❌ فشل رفع الإعدادات');
            } catch (error) {
                console.error('❌ خطأ في مزامنة الإعدادات:', error);
            }
        }

        function forceUIRefresh() {
            console.log('🔄 إجبار تحديث الواجهة...');
            try {
                if (typeof window.forceUIRefresh === 'function') {
                    window.forceUIRefresh();
                    console.log('✅ تم تحديث الواجهة');
                } else {
                    console.log('⚠️ وظيفة تحديث الواجهة غير متاحة');
                    alert('وظيفة تحديث الواجهة غير متاحة. افتح التطبيق الرئيسي أولاً.');
                }
            } catch (error) {
                console.error('❌ خطأ في تحديث الواجهة:', error);
            }
        }

        function openUIRefreshTool() {
            console.log('🛠️ فتح أداة تحديث الواجهة...');
            window.open('force-ui-refresh.html', 'UIRefresh', 'width=700,height=600,scrollbars=yes,resizable=yes');
        }
    </script>
</body>
</html>
