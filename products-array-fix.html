<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 إصلاح مشكلة window.products</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #dc3545, #c82333);
            border-radius: 10px;
        }
        .btn {
            padding: 15px 25px;
            margin: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn.primary { background: #007bff; color: white; }
        .btn.success { background: #28a745; color: white; }
        .btn.warning { background: #ffc107; color: #212529; }
        .btn.danger { background: #dc3545; color: white; }
        .btn:hover { transform: translateY(-2px); }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .log {
            background: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
        }
        .info-box {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 إصلاح مشكلة window.products</h1>
            <p>حل خطأ "window.products.push is not a function"</p>
        </div>

        <!-- Current Status -->
        <div class="info-box">
            <h3>📊 الحالة الحالية:</h3>
            <div id="currentStatus">جاري الفحص...</div>
        </div>

        <!-- Actions -->
        <div style="text-align: center; margin: 30px 0;">
            <button class="btn primary" onclick="checkProductsArray()">
                🔍 فحص window.products
            </button>
            <button class="btn success" onclick="fixProductsArray()">
                🔧 إصلاح window.products
            </button>
            <button class="btn warning" onclick="testPushFunction()">
                🧪 اختبار push
            </button>
            <button class="btn danger" onclick="clearLog()">
                🗑️ مسح السجل
            </button>
        </div>

        <!-- Log -->
        <div class="log" id="log">
            <div>[INFO] مرحباً بك في أداة إصلاح window.products</div>
        </div>
    </div>

    <script>
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 بدء تشخيص window.products...', 'info');
            setTimeout(() => {
                checkProductsArray();
            }, 1000);
        });

        // Check products array
        function checkProductsArray() {
            log('🔍 فحص حالة window.products...', 'info');
            
            const status = document.getElementById('currentStatus');
            let statusHtml = '';
            let hasIssues = false;

            // Check if window.products exists
            if (typeof window.products === 'undefined') {
                statusHtml += '<div class="status error">❌ window.products غير موجود</div>';
                log('❌ window.products غير موجود', 'error');
                hasIssues = true;
            } else {
                statusHtml += '<div class="status success">✅ window.products موجود</div>';
                log('✅ window.products موجود', 'success');
            }

            // Check if it's an array
            if (window.products && Array.isArray(window.products)) {
                statusHtml += '<div class="status success">✅ window.products هو مصفوفة</div>';
                statusHtml += `<div class="status info">📊 عدد المنتجات: ${window.products.length}</div>`;
                log(`✅ window.products هو مصفوفة مع ${window.products.length} عنصر`, 'success');
            } else if (window.products) {
                statusHtml += '<div class="status error">❌ window.products ليس مصفوفة</div>';
                statusHtml += `<div class="status warning">⚠️ النوع الحالي: ${typeof window.products}</div>`;
                log(`❌ window.products ليس مصفوفة، النوع: ${typeof window.products}`, 'error');
                hasIssues = true;
            }

            // Check push function
            if (window.products && typeof window.products.push === 'function') {
                statusHtml += '<div class="status success">✅ وظيفة push متاحة</div>';
                log('✅ وظيفة push متاحة', 'success');
            } else {
                statusHtml += '<div class="status error">❌ وظيفة push غير متاحة</div>';
                log('❌ وظيفة push غير متاحة', 'error');
                hasIssues = true;
            }

            // Check localStorage
            try {
                const localProducts = localStorage.getItem('inventory_products');
                if (localProducts) {
                    const parsed = JSON.parse(localProducts);
                    if (Array.isArray(parsed)) {
                        statusHtml += `<div class="status info">📱 localStorage: ${parsed.length} منتج</div>`;
                        log(`📱 localStorage يحتوي على ${parsed.length} منتج`, 'info');
                    } else {
                        statusHtml += '<div class="status warning">⚠️ localStorage ليس مصفوفة</div>';
                        log('⚠️ localStorage ليس مصفوفة', 'warning');
                    }
                } else {
                    statusHtml += '<div class="status warning">⚠️ localStorage فارغ</div>';
                    log('⚠️ localStorage فارغ', 'warning');
                }
            } catch (error) {
                statusHtml += '<div class="status error">❌ خطأ في قراءة localStorage</div>';
                log(`❌ خطأ في قراءة localStorage: ${error.message}`, 'error');
            }

            // Overall status
            if (!hasIssues) {
                statusHtml += '<div class="status success">🎉 window.products يعمل بشكل صحيح!</div>';
                log('🎉 window.products يعمل بشكل صحيح!', 'success');
            } else {
                statusHtml += '<div class="status error">🔧 window.products يحتاج إصلاح</div>';
                log('🔧 window.products يحتاج إصلاح', 'error');
            }

            status.innerHTML = statusHtml;
        }

        // Fix products array
        function fixProductsArray() {
            log('🔧 بدء إصلاح window.products...', 'info');

            try {
                // Step 1: Backup current data if it exists
                let backupData = null;
                if (window.products && typeof window.products === 'object') {
                    backupData = window.products;
                    log('💾 تم حفظ البيانات الحالية كنسخة احتياطية', 'info');
                }

                // Step 2: Try to load from localStorage
                let productsFromStorage = [];
                try {
                    const localProducts = localStorage.getItem('inventory_products');
                    if (localProducts) {
                        const parsed = JSON.parse(localProducts);
                        if (Array.isArray(parsed)) {
                            productsFromStorage = parsed;
                            log(`📱 تم تحميل ${parsed.length} منتج من localStorage`, 'success');
                        }
                    }
                } catch (error) {
                    log(`⚠️ خطأ في تحميل localStorage: ${error.message}`, 'warning');
                }

                // Step 3: Create new array
                window.products = [...productsFromStorage];
                log(`✅ تم إنشاء window.products جديد مع ${window.products.length} منتج`, 'success');

                // Step 4: Test the array
                if (Array.isArray(window.products) && typeof window.products.push === 'function') {
                    log('✅ window.products الآن مصفوفة صحيحة مع وظيفة push', 'success');
                    
                    // Step 5: Test push function
                    const testItem = { test: true, timestamp: Date.now() };
                    window.products.push(testItem);
                    log('✅ اختبار push نجح', 'success');
                    
                    // Remove test item
                    window.products.pop();
                    log('🧹 تم إزالة العنصر التجريبي', 'info');
                    
                    // Step 6: Update localStorage
                    localStorage.setItem('inventory_products', JSON.stringify(window.products));
                    log('💾 تم تحديث localStorage', 'success');
                    
                    log('🎉 تم إصلاح window.products بنجاح!', 'success');
                    
                    // Refresh status
                    setTimeout(() => {
                        checkProductsArray();
                    }, 1000);
                    
                } else {
                    throw new Error('فشل في إنشاء مصفوفة صحيحة');
                }

            } catch (error) {
                log(`❌ خطأ في إصلاح window.products: ${error.message}`, 'error');
                
                // Emergency fix
                log('🚨 تطبيق الإصلاح الطارئ...', 'warning');
                window.products = [];
                log('✅ تم إنشاء مصفوفة فارغة كحل طارئ', 'success');
            }
        }

        // Test push function
        function testPushFunction() {
            log('🧪 اختبار وظيفة push...', 'info');

            try {
                if (!window.products) {
                    log('❌ window.products غير موجود', 'error');
                    return;
                }

                if (!Array.isArray(window.products)) {
                    log('❌ window.products ليس مصفوفة', 'error');
                    return;
                }

                if (typeof window.products.push !== 'function') {
                    log('❌ وظيفة push غير متاحة', 'error');
                    return;
                }

                // Test push
                const originalLength = window.products.length;
                const testItem = {
                    id: 'test-' + Date.now(),
                    name: 'منتج تجريبي',
                    timestamp: new Date().toISOString()
                };

                window.products.push(testItem);
                log(`✅ تم إضافة منتج تجريبي، العدد: ${originalLength} → ${window.products.length}`, 'success');

                // Remove test item
                window.products.pop();
                log(`🧹 تم إزالة المنتج التجريبي، العدد: ${window.products.length}`, 'info');

                log('🎉 اختبار push نجح بالكامل!', 'success');

            } catch (error) {
                log(`❌ خطأ في اختبار push: ${error.message}`, 'error');
                log('🔧 يرجى تشغيل "إصلاح window.products" أولاً', 'warning');
            }
        }

        // Log function
        function log(message, type = 'info') {
            const logEl = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                info: '#00ff00',
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107'
            };
            
            const logEntry = document.createElement('div');
            logEntry.style.color = colors[type] || colors.info;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            
            logEl.appendChild(logEntry);
            logEl.scrollTop = logEl.scrollHeight;

            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // Clear log
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            log('تم مسح السجل', 'info');
        }

        // Auto-fix on severe errors
        window.addEventListener('error', function(event) {
            if (event.message && event.message.includes('window.products.push is not a function')) {
                log('🚨 تم اكتشاف خطأ window.products.push - تطبيق الإصلاح التلقائي...', 'error');
                setTimeout(() => {
                    fixProductsArray();
                }, 1000);
            }
        });

        // Global fix function
        window.fixProductsArrayNow = function() {
            return fixProductsArray();
        };

        // Global check function
        window.checkProductsArrayNow = function() {
            return checkProductsArray();
        };
    </script>
</body>
</html>
