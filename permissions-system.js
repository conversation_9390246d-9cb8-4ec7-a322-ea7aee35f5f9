// 🔐 نظام الصلاحيات المتقدم - Advanced Permissions System
// يدير الصلاحيات والوصول للبيانات السحابية

class PermissionsSystem {
    constructor() {
        this.currentUser = null;
        this.userPermissions = [];
        
        // Define all available permissions
        this.availablePermissions = {
            // Products permissions
            'products_read': 'عرض المنتجات',
            'products_write': 'إضافة وتعديل المنتجات',
            'products_delete': 'حذف المنتجات',
            'products_export': 'تصدير المنتجات',
            
            // Customers permissions
            'customers_read': 'عرض العملاء',
            'customers_write': 'إضافة وتعديل العملاء',
            'customers_delete': 'حذف العملاء',
            'customers_export': 'تصدير العملاء',
            
            // Users permissions
            'users_read': 'عرض المستخدمين',
            'users_write': 'إضافة وتعديل المستخدمين',
            'users_delete': 'حذف المستخدمين',
            'users_manage': 'إدارة صلاحيات المستخدمين',
            
            // Settings permissions
            'settings_read': 'عرض الإعدادات',
            'settings_write': 'تعديل الإعدادات',
            'settings_system': 'إعدادات النظام المتقدمة',
            
            // Reports permissions
            'reports_read': 'عرض التقارير',
            'reports_generate': 'إنشاء التقارير',
            'reports_export': 'تصدير التقارير',
            
            // Dashboard permissions
            'dashboard_read': 'عرض لوحة التحكم',
            'dashboard_stats': 'عرض الإحصائيات',
            
            // System permissions
            'system_backup': 'النسخ الاحتياطي',
            'system_restore': 'استعادة البيانات',
            'system_sync': 'مزامنة البيانات',
            'system_admin': 'إدارة النظام الكاملة'
        };

        // Define role-based permissions
        this.rolePermissions = {
            'admin': Object.keys(this.availablePermissions), // All permissions
            'manager': [
                'products_read', 'products_write', 'products_export',
                'customers_read', 'customers_write', 'customers_export',
                'users_read',
                'settings_read',
                'reports_read', 'reports_generate', 'reports_export',
                'dashboard_read', 'dashboard_stats',
                'system_sync'
            ],
            'employee': [
                'products_read', 'products_write',
                'customers_read', 'customers_write',
                'dashboard_read', 'dashboard_stats',
                'reports_read'
            ],
            'viewer': [
                'products_read',
                'customers_read',
                'dashboard_read',
                'reports_read'
            ]
        };

        this.init();
    }

    // Initialize permissions system
    init() {
        console.log('🔐 تهيئة نظام الصلاحيات...');
        this.loadCurrentUser();
        this.setupPermissionChecks();
        console.log('✅ تم تهيئة نظام الصلاحيات');
    }

    // Load current user and their permissions
    loadCurrentUser() {
        try {
            const currentUserData = localStorage.getItem('currentUser');
            if (currentUserData) {
                this.currentUser = JSON.parse(currentUserData);
                this.userPermissions = this.getUserPermissions(this.currentUser);
                
                console.log('👤 المستخدم الحالي:', this.currentUser.name);
                console.log('🎭 الدور:', this.currentUser.role);
                console.log('🔐 عدد الصلاحيات:', this.userPermissions.length);
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل بيانات المستخدم:', error);
        }
    }

    // Get user permissions based on role and custom permissions
    getUserPermissions(user) {
        if (!user) return [];
        
        // Start with role-based permissions
        let permissions = [...(this.rolePermissions[user.role] || [])];
        
        // Add custom permissions if any
        if (user.permissions && Array.isArray(user.permissions)) {
            permissions = [...new Set([...permissions, ...user.permissions])];
        }
        
        return permissions;
    }

    // Check if user has specific permission
    hasPermission(permission) {
        if (!this.currentUser) {
            console.log('⚠️ لا يوجد مستخدم مسجل دخول');
            return false;
        }
        
        // Admin has all permissions
        if (this.currentUser.role === 'admin') {
            return true;
        }
        
        // Check if user has the specific permission
        const hasPermission = this.userPermissions.includes(permission);
        
        if (!hasPermission) {
            console.log(`❌ المستخدم ${this.currentUser.name} لا يملك صلاحية: ${permission}`);
        }
        
        return hasPermission;
    }

    // Check multiple permissions (user must have ALL)
    hasAllPermissions(permissions) {
        return permissions.every(permission => this.hasPermission(permission));
    }

    // Check multiple permissions (user must have ANY)
    hasAnyPermission(permissions) {
        return permissions.some(permission => this.hasPermission(permission));
    }

    // Get user's readable permissions list
    getUserPermissionsList() {
        return this.userPermissions.map(permission => ({
            key: permission,
            name: this.availablePermissions[permission] || permission
        }));
    }

    // Setup permission checks for UI elements
    setupPermissionChecks() {
        // Hide/show elements based on permissions
        this.applyPermissionsToUI();
        
        // Setup periodic permission refresh
        setInterval(() => {
            this.refreshPermissions();
        }, 60000); // Check every minute
    }

    // Apply permissions to UI elements
    applyPermissionsToUI() {
        console.log('🎨 تطبيق الصلاحيات على الواجهة...');
        
        // Products section
        this.toggleElementsByPermission('products_write', [
            '#addProductBtn',
            '.edit-product-btn',
            '#productForm'
        ]);
        
        this.toggleElementsByPermission('products_delete', [
            '.delete-product-btn'
        ]);
        
        // Customers section
        this.toggleElementsByPermission('customers_write', [
            '#addCustomerBtn',
            '.edit-customer-btn',
            '#customerForm'
        ]);
        
        this.toggleElementsByPermission('customers_delete', [
            '.delete-customer-btn'
        ]);
        
        // Users section
        this.toggleElementsByPermission('users_read', [
            '#usersSection',
            '.users-tab'
        ]);
        
        this.toggleElementsByPermission('users_write', [
            '#addUserBtn',
            '.edit-user-btn'
        ]);
        
        this.toggleElementsByPermission('users_delete', [
            '.delete-user-btn'
        ]);
        
        // Settings section
        this.toggleElementsByPermission('settings_write', [
            '#settingsForm',
            '.settings-save-btn'
        ]);
        
        this.toggleElementsByPermission('settings_system', [
            '#systemSettingsSection',
            '.system-settings-tab'
        ]);
        
        // Reports section
        this.toggleElementsByPermission('reports_generate', [
            '#generateReportBtn',
            '.report-generate-btn'
        ]);
        
        this.toggleElementsByPermission('reports_export', [
            '.export-report-btn'
        ]);
        
        // System tools
        this.toggleElementsByPermission('system_admin', [
            '#adminToolsSection',
            '.admin-tools-tab'
        ]);
    }

    // Toggle elements visibility based on permission
    toggleElementsByPermission(permission, selectors) {
        const hasPermission = this.hasPermission(permission);
        
        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                if (hasPermission) {
                    element.style.display = '';
                    element.removeAttribute('disabled');
                } else {
                    element.style.display = 'none';
                    element.setAttribute('disabled', 'true');
                }
            });
        });
    }

    // Refresh permissions (in case user role changed)
    refreshPermissions() {
        this.loadCurrentUser();
        this.applyPermissionsToUI();
    }

    // Check permission before executing function
    checkPermissionAndExecute(permission, callback, errorMessage = null) {
        if (this.hasPermission(permission)) {
            return callback();
        } else {
            const message = errorMessage || `ليس لديك صلاحية: ${this.availablePermissions[permission] || permission}`;
            this.showPermissionError(message);
            return false;
        }
    }

    // Show permission error
    showPermissionError(message) {
        if (typeof utils !== 'undefined' && typeof utils.showNotification === 'function') {
            utils.showNotification(`🔒 ${message}`, 'error');
        } else {
            alert(`🔒 ${message}`);
        }
        
        console.warn('🔒 رفض الوصول:', message);
    }

    // Get permissions for specific role
    getRolePermissions(role) {
        return this.rolePermissions[role] || [];
    }

    // Update user permissions
    updateUserPermissions(userId, newPermissions) {
        if (!this.hasPermission('users_manage')) {
            this.showPermissionError('ليس لديك صلاحية إدارة المستخدمين');
            return false;
        }
        
        // Implementation for updating user permissions
        console.log(`🔄 تحديث صلاحيات المستخدم ${userId}:`, newPermissions);
        return true;
    }

    // Create permission-aware wrapper for functions
    createPermissionWrapper(permission, originalFunction) {
        return (...args) => {
            return this.checkPermissionAndExecute(
                permission,
                () => originalFunction.apply(this, args),
                `ليس لديك صلاحية لتنفيذ هذا الإجراء`
            );
        };
    }

    // Get permission status for debugging
    getPermissionStatus() {
        return {
            user: this.currentUser?.name || 'غير مسجل',
            role: this.currentUser?.role || 'غير محدد',
            permissions: this.userPermissions,
            permissionsCount: this.userPermissions.length,
            isAdmin: this.currentUser?.role === 'admin'
        };
    }

    // Export permissions for backup
    exportPermissions() {
        if (!this.hasPermission('system_backup')) {
            this.showPermissionError('ليس لديك صلاحية النسخ الاحتياطي');
            return null;
        }
        
        return {
            rolePermissions: this.rolePermissions,
            availablePermissions: this.availablePermissions,
            exportDate: new Date().toISOString()
        };
    }
}

// Initialize permissions system
window.permissionsSystem = new PermissionsSystem();

// Create global permission check function
window.hasPermission = (permission) => {
    return window.permissionsSystem.hasPermission(permission);
};

// Create global permission wrapper function
window.withPermission = (permission, callback, errorMessage) => {
    return window.permissionsSystem.checkPermissionAndExecute(permission, callback, errorMessage);
};

// Export for use in other files
window.PermissionsSystem = PermissionsSystem;

console.log('🔐 نظام الصلاحيات جاهز للاستخدام');
