/**
 * 🛡️ مدير استقرار الواجهة
 * يمنع ظهور واختفاء المنتجات أثناء المزامنة
 */

class UIStabilityManager {
    constructor() {
        this.isUIUpdateInProgress = false;
        this.pendingUIUpdates = new Set();
        this.lastUIUpdateTime = 0;
        this.uiUpdateCooldown = 1000; // 1 second cooldown between UI updates
        this.updateQueue = [];
        this.isProcessingQueue = false;
        
        // Track table state
        this.lastTableState = null;
        this.tableUpdateInProgress = false;
        
        // Debounce settings
        this.debounceDelay = 500; // 500ms debounce
        this.debounceTimers = new Map();
        
        console.log('🛡️ تم تهيئة مدير استقرار الواجهة');
        this.init();
    }
    
    init() {
        // Override the original loadProductsTable function
        this.overrideUIFunctions();
        
        // Setup mutation observer to detect table changes
        this.setupTableObserver();
        
        // Setup visibility change handler
        this.setupVisibilityHandler();
    }
    
    // Override UI functions to add stability
    overrideUIFunctions() {
        // Store original functions
        if (typeof window.loadProductsTable === 'function') {
            this.originalLoadProductsTable = window.loadProductsTable;
            window.loadProductsTable = (...args) => this.stableLoadProductsTable(...args);
        }
        
        if (typeof window.updateDashboardStats === 'function') {
            this.originalUpdateDashboardStats = window.updateDashboardStats;
            window.updateDashboardStats = (...args) => this.stableUpdateDashboardStats(...args);
        }
        
        console.log('🔧 تم تطبيق حماية الواجهة على الدوال الأساسية');
    }
    
    // Stable version of loadProductsTable
    stableLoadProductsTable(...args) {
        const updateKey = 'loadProductsTable';
        
        // Clear existing debounce timer
        if (this.debounceTimers.has(updateKey)) {
            clearTimeout(this.debounceTimers.get(updateKey));
        }
        
        // Set new debounce timer
        const timer = setTimeout(() => {
            this.executeUIUpdate(updateKey, () => {
                if (this.originalLoadProductsTable) {
                    this.originalLoadProductsTable(...args);
                }
            });
            this.debounceTimers.delete(updateKey);
        }, this.debounceDelay);
        
        this.debounceTimers.set(updateKey, timer);
    }
    
    // Stable version of updateDashboardStats
    stableUpdateDashboardStats(...args) {
        const updateKey = 'updateDashboardStats';
        
        // Clear existing debounce timer
        if (this.debounceTimers.has(updateKey)) {
            clearTimeout(this.debounceTimers.get(updateKey));
        }
        
        // Set new debounce timer
        const timer = setTimeout(() => {
            this.executeUIUpdate(updateKey, () => {
                if (this.originalUpdateDashboardStats) {
                    this.originalUpdateDashboardStats(...args);
                }
            });
            this.debounceTimers.delete(updateKey);
        }, this.debounceDelay);
        
        this.debounceTimers.set(updateKey, timer);
    }
    
    // Execute UI update with stability checks
    executeUIUpdate(updateKey, updateFunction) {
        const now = Date.now();
        
        // Check cooldown
        if (now - this.lastUIUpdateTime < this.uiUpdateCooldown) {
            console.log(`⏸️ تأجيل تحديث الواجهة - فترة انتظار: ${updateKey}`);
            
            // Add to queue for later execution
            this.addToUpdateQueue(updateKey, updateFunction);
            return;
        }
        
        // Check if update is already in progress
        if (this.pendingUIUpdates.has(updateKey)) {
            console.log(`⏸️ تحديث الواجهة قيد التنفيذ: ${updateKey}`);
            return;
        }
        
        // Mark update as in progress
        this.pendingUIUpdates.add(updateKey);
        this.lastUIUpdateTime = now;
        
        try {
            console.log(`🔄 تنفيذ تحديث مستقر للواجهة: ${updateKey}`);
            
            // Execute the update
            updateFunction();
            
            console.log(`✅ تم تحديث الواجهة بنجاح: ${updateKey}`);
            
        } catch (error) {
            console.error(`❌ خطأ في تحديث الواجهة: ${updateKey}`, error);
        } finally {
            // Remove from pending updates
            this.pendingUIUpdates.delete(updateKey);
            
            // Process queue after a short delay
            setTimeout(() => this.processUpdateQueue(), 100);
        }
    }
    
    // Add update to queue
    addToUpdateQueue(updateKey, updateFunction) {
        // Remove existing update of same type
        this.updateQueue = this.updateQueue.filter(item => item.key !== updateKey);
        
        // Add new update
        this.updateQueue.push({
            key: updateKey,
            function: updateFunction,
            timestamp: Date.now()
        });
        
        // Limit queue size
        if (this.updateQueue.length > 5) {
            this.updateQueue = this.updateQueue.slice(-5);
        }
        
        // Process queue if not already processing
        if (!this.isProcessingQueue) {
            setTimeout(() => this.processUpdateQueue(), this.uiUpdateCooldown);
        }
    }
    
    // Process update queue
    processUpdateQueue() {
        if (this.isProcessingQueue || this.updateQueue.length === 0) {
            return;
        }
        
        this.isProcessingQueue = true;
        
        try {
            const update = this.updateQueue.shift();
            if (update) {
                console.log(`🔄 معالجة تحديث من الطابور: ${update.key}`);
                this.executeUIUpdate(update.key, update.function);
            }
        } catch (error) {
            console.error('❌ خطأ في معالجة طابور التحديثات:', error);
        } finally {
            this.isProcessingQueue = false;
            
            // Continue processing if there are more updates
            if (this.updateQueue.length > 0) {
                setTimeout(() => this.processUpdateQueue(), 200);
            }
        }
    }
    
    // Setup table observer to detect unwanted changes
    setupTableObserver() {
        const tableContainer = document.querySelector('#productsTable');
        if (!tableContainer) {
            // Retry after DOM is ready
            setTimeout(() => this.setupTableObserver(), 1000);
            return;
        }
        
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' && mutation.target.tagName === 'TBODY') {
                    this.handleTableChange(mutation);
                }
            });
        });
        
        observer.observe(tableContainer, {
            childList: true,
            subtree: true
        });
        
        console.log('👁️ تم تفعيل مراقب استقرار الجدول');
    }
    
    // Handle table changes
    handleTableChange(mutation) {
        if (this.tableUpdateInProgress) {
            return; // Ignore changes during controlled updates
        }
        
        const currentRowCount = mutation.target.children.length;
        
        // Detect rapid table clearing/filling
        if (currentRowCount === 0 && this.lastTableState && this.lastTableState.rowCount > 0) {
            console.log('⚠️ تم اكتشاف مسح مفاجئ للجدول - استعادة البيانات...');
            
            // Restore table after a short delay
            setTimeout(() => {
                if (mutation.target.children.length === 0 && window.products && window.products.length > 0) {
                    console.log('🔧 استعادة جدول المنتجات...');
                    this.forceTableRestore();
                }
            }, 100);
        }
        
        this.lastTableState = {
            rowCount: currentRowCount,
            timestamp: Date.now()
        };
    }
    
    // Force table restore
    forceTableRestore() {
        if (!window.products || window.products.length === 0) {
            return;
        }
        
        this.tableUpdateInProgress = true;
        
        try {
            const tbody = document.querySelector('#productsTable tbody');
            if (tbody && tbody.children.length === 0) {
                console.log('🔧 إعادة بناء جدول المنتجات يدوياً...');
                
                // Call original function directly
                if (this.originalLoadProductsTable) {
                    this.originalLoadProductsTable();
                }
            }
        } catch (error) {
            console.error('❌ خطأ في استعادة الجدول:', error);
        } finally {
            setTimeout(() => {
                this.tableUpdateInProgress = false;
            }, 500);
        }
    }
    
    // Setup visibility change handler
    setupVisibilityHandler() {
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                // Page became visible - ensure UI is stable
                setTimeout(() => {
                    this.ensureUIStability();
                }, 1000);
            }
        });
    }
    
    // Ensure UI stability
    ensureUIStability() {
        console.log('🔍 فحص استقرار الواجهة...');
        
        const tbody = document.querySelector('#productsTable tbody');
        if (tbody && window.products && window.products.length > 0 && tbody.children.length === 0) {
            console.log('🔧 إصلاح جدول فارغ...');
            this.forceTableRestore();
        }
    }
    
    // Clear all pending updates
    clearPendingUpdates() {
        this.pendingUIUpdates.clear();
        this.updateQueue = [];
        this.debounceTimers.forEach(timer => clearTimeout(timer));
        this.debounceTimers.clear();
        console.log('🧹 تم مسح جميع التحديثات المعلقة');
    }
    
    // Get status
    getStatus() {
        return {
            pendingUpdates: this.pendingUIUpdates.size,
            queueLength: this.updateQueue.length,
            debounceTimers: this.debounceTimers.size,
            lastUpdateTime: this.lastUIUpdateTime,
            tableState: this.lastTableState
        };
    }
}

// Initialize UI Stability Manager
window.uiStabilityManager = new UIStabilityManager();

// Export for use in other files
window.UIStabilityManager = UIStabilityManager;

// Add global functions for easy access
window.clearPendingUIUpdates = () => {
    window.uiStabilityManager.clearPendingUpdates();
};

window.getUIStabilityStatus = () => {
    return window.uiStabilityManager.getStatus();
};

window.ensureUIStability = () => {
    window.uiStabilityManager.ensureUIStability();
};

console.log('🛡️ مدير استقرار الواجهة جاهز للاستخدام');
