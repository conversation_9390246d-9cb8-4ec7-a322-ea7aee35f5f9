// Firebase Configuration for Diamond Eagles Store
// شركة النسور الماسية للتجارة - إعدادات Firebase

console.log('🔥 تحميل إعدادات Firebase...');

// Firebase Configuration
const firebaseConfig = {
    apiKey: "AIzaSyBDjmRuPy0nfCr8VdGhc6THkjKuz6LMr9g",
    authDomain: "diamond-eagles-store.firebaseapp.com",
    projectId: "diamond-eagles-store",
    storageBucket: "diamond-eagles-store.firebasestorage.app",
    messagingSenderId: "241294391606",
    appId: "1:241294391606:web:80d97d1bcdaea1948f9b97"
};

// Initialize Firebase
let firebaseApp;
let db;
let isFirebaseReady = false;

try {
    // Initialize Firebase App
    firebaseApp = firebase.initializeApp(firebaseConfig);
    
    // Initialize Firestore
    db = firebase.firestore();
    
    // Enable offline persistence
    db.enablePersistence()
        .then(() => {
            console.log('✅ Firebase offline persistence enabled');
        })
        .catch((err) => {
            console.warn('⚠️ Firebase offline persistence failed:', err);
        });
    
    isFirebaseReady = true;
    console.log('✅ Firebase initialized successfully');
    
} catch (error) {
    console.error('❌ Firebase initialization failed:', error);
    isFirebaseReady = false;
}

// Firebase Service Class
class FirebaseService {
    constructor() {
        this.isReady = isFirebaseReady;
        this.db = db;
        this.collections = {
            products: 'products',
            customers: 'customers',
            users: 'users',
            settings: 'settings',
            loginCredentials: 'loginCredentials',
            backups: 'backups'
        };
    }

    // Check if Firebase is ready
    isFirebaseReady() {
        return this.isReady && this.db;
    }

    // Save products to Firebase
    async saveProducts(products) {
        if (!this.isFirebaseReady()) {
            console.warn('⚠️ Firebase not ready, saving locally only');
            return false;
        }

        try {
            await this.db.collection(this.collections.products).doc('data').set({
                products: products,
                lastUpdated: firebase.firestore.FieldValue.serverTimestamp(),
                version: Date.now()
            });
            
            console.log('✅ Products saved to Firebase');
            return true;
        } catch (error) {
            console.error('❌ Error saving products to Firebase:', error);
            return false;
        }
    }

    // Load products from Firebase
    async loadProducts() {
        if (!this.isFirebaseReady()) {
            console.warn('⚠️ Firebase not ready, loading from localStorage');
            return null;
        }

        try {
            const doc = await this.db.collection(this.collections.products).doc('data').get();
            
            if (doc.exists) {
                const data = doc.data();
                console.log('✅ Products loaded from Firebase');
                return data.products || [];
            } else {
                console.log('ℹ️ No products found in Firebase');
                return [];
            }
        } catch (error) {
            console.error('❌ Error loading products from Firebase:', error);
            return null;
        }
    }

    // Save customers to Firebase
    async saveCustomers(customers) {
        if (!this.isFirebaseReady()) {
            console.warn('⚠️ Firebase not ready, saving locally only');
            return false;
        }

        try {
            await this.db.collection(this.collections.customers).doc('data').set({
                customers: customers,
                lastUpdated: firebase.firestore.FieldValue.serverTimestamp(),
                version: Date.now()
            });
            
            console.log('✅ Customers saved to Firebase');
            return true;
        } catch (error) {
            console.error('❌ Error saving customers to Firebase:', error);
            return false;
        }
    }

    // Load customers from Firebase
    async loadCustomers() {
        if (!this.isFirebaseReady()) {
            console.warn('⚠️ Firebase not ready, loading from localStorage');
            return null;
        }

        try {
            const doc = await this.db.collection(this.collections.customers).doc('data').get();
            
            if (doc.exists) {
                const data = doc.data();
                console.log('✅ Customers loaded from Firebase');
                return data.customers || [];
            } else {
                console.log('ℹ️ No customers found in Firebase');
                return [];
            }
        } catch (error) {
            console.error('❌ Error loading customers from Firebase:', error);
            return null;
        }
    }

    // Save users to Firebase
    async saveUsers(users) {
        if (!this.isFirebaseReady()) {
            console.warn('⚠️ Firebase not ready, saving locally only');
            return false;
        }

        try {
            await this.db.collection(this.collections.users).doc('data').set({
                users: users,
                lastUpdated: firebase.firestore.FieldValue.serverTimestamp(),
                version: Date.now()
            });
            
            console.log('✅ Users saved to Firebase');
            return true;
        } catch (error) {
            console.error('❌ Error saving users to Firebase:', error);
            return false;
        }
    }

    // Load users from Firebase
    async loadUsers() {
        if (!this.isFirebaseReady()) {
            console.warn('⚠️ Firebase not ready, loading from localStorage');
            return null;
        }

        try {
            const doc = await this.db.collection(this.collections.users).doc('data').get();
            
            if (doc.exists) {
                const data = doc.data();
                console.log('✅ Users loaded from Firebase');
                return data.users || [];
            } else {
                console.log('ℹ️ No users found in Firebase');
                return [];
            }
        } catch (error) {
            console.error('❌ Error loading users from Firebase:', error);
            return null;
        }
    }

    // Save settings to Firebase
    async saveSettings(settings) {
        if (!this.isFirebaseReady()) {
            console.warn('⚠️ Firebase not ready, saving locally only');
            return false;
        }

        try {
            await this.db.collection(this.collections.settings).doc('data').set({
                settings: settings,
                lastUpdated: firebase.firestore.FieldValue.serverTimestamp(),
                version: Date.now()
            });

            console.log('✅ Settings saved to Firebase');
            return true;
        } catch (error) {
            console.error('❌ Error saving settings to Firebase:', error);
            return false;
        }
    }

    // Load settings from Firebase
    async loadSettings() {
        if (!this.isFirebaseReady()) {
            console.warn('⚠️ Firebase not ready, loading from localStorage');
            return null;
        }

        try {
            const doc = await this.db.collection(this.collections.settings).doc('data').get();

            if (doc.exists) {
                const data = doc.data();
                console.log('✅ Settings loaded from Firebase');
                return data.settings || {};
            } else {
                console.log('ℹ️ No settings found in Firebase');
                return {};
            }
        } catch (error) {
            console.error('❌ Error loading settings from Firebase:', error);
            return null;
        }
    }

    // Save login credentials to Firebase
    async saveLoginCredentials(credentials) {
        if (!this.isFirebaseReady()) {
            console.warn('⚠️ Firebase not ready, saving locally only');
            return false;
        }

        try {
            await this.db.collection(this.collections.loginCredentials).doc('data').set({
                credentials: credentials,
                lastUpdated: firebase.firestore.FieldValue.serverTimestamp(),
                version: Date.now()
            });

            console.log('✅ Login credentials saved to Firebase');
            return true;
        } catch (error) {
            console.error('❌ Error saving login credentials to Firebase:', error);
            return false;
        }
    }

    // Load login credentials from Firebase
    async loadLoginCredentials() {
        if (!this.isFirebaseReady()) {
            console.warn('⚠️ Firebase not ready, loading from localStorage');
            return null;
        }

        try {
            const doc = await this.db.collection(this.collections.loginCredentials).doc('data').get();

            if (doc.exists) {
                const data = doc.data();
                console.log('✅ Login credentials loaded from Firebase');
                return data.credentials || {};
            } else {
                console.log('ℹ️ No login credentials found in Firebase');
                return {};
            }
        } catch (error) {
            console.error('❌ Error loading login credentials from Firebase:', error);
            return null;
        }
    }

    // Sync all data to Firebase
    async syncAllToFirebase() {
        if (!this.isFirebaseReady()) {
            console.warn('⚠️ Firebase not ready for sync');
            return false;
        }

        try {
            console.log('🔄 Syncing all data to Firebase...');

            // Get data from localStorage (using correct key names)
            const products = JSON.parse(localStorage.getItem('inventory_products') || '[]');
            const customers = JSON.parse(localStorage.getItem('inventory_customers') || '[]');
            const users = JSON.parse(localStorage.getItem('systemUsers') || '[]');
            const settings = JSON.parse(localStorage.getItem('systemSettings') || '{}');
            const loginCredentials = JSON.parse(localStorage.getItem('loginCredentials') || '{}');

            // Save to Firebase
            const results = await Promise.allSettled([
                this.saveProducts(products),
                this.saveCustomers(customers),
                this.saveUsers(users),
                this.saveSettings(settings),
                this.saveLoginCredentials(loginCredentials)
            ]);

            const successful = results.filter(r => r.status === 'fulfilled' && r.value).length;
            console.log(`✅ Synced ${successful}/5 collections to Firebase`);

            // Show sync status to user
            if (successful === 5) {
                showSyncStatus('✅ تم رفع جميع البيانات إلى Firebase', 'success');
            } else if (successful > 0) {
                showSyncStatus(`⚠️ تم رفع ${successful}/5 من البيانات إلى Firebase`, 'warning');
            } else {
                showSyncStatus('❌ فشل في رفع البيانات إلى Firebase', 'error');
            }

            return successful === 5;
        } catch (error) {
            console.error('❌ Error syncing to Firebase:', error);
            return false;
        }
    }

    // Sync all data from Firebase
    async syncAllFromFirebase() {
        if (!this.isFirebaseReady()) {
            console.warn('⚠️ Firebase not ready for sync');
            return false;
        }

        try {
            console.log('🔄 Syncing all data from Firebase...');

            // Load from Firebase with detailed logging
            console.log('📥 Loading products from Firebase...');
            const products = await this.loadProducts();

            console.log('📥 Loading customers from Firebase...');
            const customers = await this.loadCustomers();

            console.log('📥 Loading users from Firebase...');
            const users = await this.loadUsers();

            console.log('📥 Loading settings from Firebase...');
            const settings = await this.loadSettings();

            console.log('📥 Loading login credentials from Firebase...');
            const loginCredentials = await this.loadLoginCredentials();

            let syncedCount = 0;

            // Save products to localStorage (using correct key names)
            if (products !== null) {
                localStorage.setItem('inventory_products', JSON.stringify(products));
                if (typeof window.products !== 'undefined') {
                    window.products = products;
                }
                console.log(`✅ Products synced: ${products.length} items`);
                syncedCount++;
            } else {
                console.log('⚠️ No products data from Firebase');
            }

            // Save customers to localStorage (using correct key names)
            if (customers !== null) {
                localStorage.setItem('inventory_customers', JSON.stringify(customers));
                if (typeof window.customers !== 'undefined') {
                    window.customers = customers;
                }
                console.log(`✅ Customers synced: ${customers.length} items`);
                syncedCount++;
            } else {
                console.log('⚠️ No customers data from Firebase');
            }

            // Save users to localStorage
            if (users !== null) {
                localStorage.setItem('systemUsers', JSON.stringify(users));
                console.log(`✅ Users synced: ${users.length} items`);
                syncedCount++;
            } else {
                console.log('⚠️ No users data from Firebase');
            }

            // Save settings to localStorage
            if (settings !== null && Object.keys(settings).length > 0) {
                localStorage.setItem('systemSettings', JSON.stringify(settings));
                if (typeof window.settings !== 'undefined') {
                    window.settings = settings;
                }
                console.log('✅ Settings synced from Firebase');
                syncedCount++;
            } else {
                console.log('⚠️ No settings data from Firebase');
            }

            // Save login credentials to localStorage
            if (loginCredentials !== null && Object.keys(loginCredentials).length > 0) {
                localStorage.setItem('loginCredentials', JSON.stringify(loginCredentials));
                console.log('✅ Login credentials synced from Firebase');
                syncedCount++;
            } else {
                console.log('⚠️ No login credentials data from Firebase');
            }

            console.log(`✅ Sync completed: ${syncedCount}/5 collections synced from Firebase`);

            // Show sync status to user
            if (syncedCount === 5) {
                showSyncStatus('✅ تم تحميل جميع البيانات من Firebase', 'success');
            } else if (syncedCount > 0) {
                showSyncStatus(`✅ تم تحميل ${syncedCount}/5 من البيانات من Firebase`, 'success');
            } else {
                showSyncStatus('ℹ️ لا توجد بيانات جديدة في Firebase', 'info');
            }

            // Trigger UI updates
            this.triggerUIUpdates();

            return syncedCount > 0;
        } catch (error) {
            console.error('❌ Error syncing from Firebase:', error);
            return false;
        }
    }

    // Trigger UI updates after sync
    triggerUIUpdates() {
        try {
            console.log('🔄 بدء تحديث الواجهة المستقر...');

            // Force reload global variables
            this.reloadGlobalVariables();

            // Use stable UI updates if available
            if (window.uiStabilityManager) {
                console.log('🛡️ استخدام مدير استقرار الواجهة');

                // Update dashboard stats with stability
                if (typeof updateDashboardStats === 'function') {
                    window.uiStabilityManager.stableUpdateDashboardStats();
                    console.log('✅ Dashboard stats updated (stable)');
                }

                // Update products table with stability
                if (typeof loadProductsTable === 'function') {
                    window.uiStabilityManager.stableLoadProductsTable();
                    console.log('✅ Products table updated (stable)');
                }
            } else {
                // Fallback to original functions with delay
                console.log('⚠️ مدير الاستقرار غير متاح - استخدام التحديث العادي');

                setTimeout(() => {
                    if (typeof updateDashboardStats === 'function') {
                        updateDashboardStats();
                        console.log('✅ Dashboard stats updated');
                    }
                }, 100);

                setTimeout(() => {
                    if (typeof loadProductsTable === 'function') {
                        loadProductsTable();
                        console.log('✅ Products table updated');
                    }
                }, 200);
            }

            // Update customers table (less frequent updates needed)
            setTimeout(() => {
                if (typeof loadCustomersTable === 'function') {
                    loadCustomersTable();
                    console.log('✅ Customers table updated');
                }
            }, 300);

            // Update users table (less frequent updates needed)
            setTimeout(() => {
                if (typeof updateUsersTable === 'function') {
                    updateUsersTable();
                    console.log('✅ Users table updated');
                }
            }, 400);

            // Force refresh page sections
            this.forceRefreshSections();

            // Show success notification
            showSyncStatus('✅ تم تحديث الواجهة بالبيانات الجديدة', 'success');

            console.log('✅ All UI components updated successfully');
        } catch (error) {
            console.error('❌ Error updating UI:', error);
            showSyncStatus('⚠️ تم تحميل البيانات لكن فشل تحديث الواجهة', 'warning');
        }
    }

    // Reload global variables from localStorage
    reloadGlobalVariables() {
        try {
            // Reload products array
            if (typeof window.products !== 'undefined') {
                const savedProducts = localStorage.getItem('inventory_products');
                if (savedProducts) {
                    window.products = JSON.parse(savedProducts);
                    console.log(`🔄 Reloaded ${window.products.length} products to global variable`);
                }
            }

            // Reload customers array
            if (typeof window.customers !== 'undefined') {
                const savedCustomers = localStorage.getItem('inventory_customers');
                if (savedCustomers) {
                    window.customers = JSON.parse(savedCustomers);
                    console.log(`🔄 Reloaded ${window.customers.length} customers to global variable`);
                }
            }

            // Reload users array
            if (typeof window.systemUsers !== 'undefined') {
                const savedUsers = localStorage.getItem('systemUsers');
                if (savedUsers) {
                    window.systemUsers = JSON.parse(savedUsers);
                    console.log(`🔄 Reloaded ${window.systemUsers.length} users to global variable`);
                }
            }

            // Reload settings
            if (typeof window.settings !== 'undefined') {
                const savedSettings = localStorage.getItem('systemSettings');
                if (savedSettings) {
                    window.settings = JSON.parse(savedSettings);
                    console.log('🔄 Reloaded settings to global variable');
                }
            }

        } catch (error) {
            console.error('❌ Error reloading global variables:', error);
        }
    }

    // Force refresh page sections
    forceRefreshSections() {
        try {
            // Refresh dashboard if visible
            const dashboard = document.querySelector('.dashboard');
            if (dashboard && dashboard.style.display !== 'none') {
                console.log('🔄 Refreshing dashboard...');
                if (typeof showDashboard === 'function') {
                    showDashboard();
                }
            }

            // Refresh products section if visible
            const productsSection = document.querySelector('.products-section');
            if (productsSection && productsSection.style.display !== 'none') {
                console.log('🔄 Refreshing products section...');
                if (typeof showProducts === 'function') {
                    showProducts();
                }
            }

            // Refresh customers section if visible
            const customersSection = document.querySelector('.customers-section');
            if (customersSection && customersSection.style.display !== 'none') {
                console.log('🔄 Refreshing customers section...');
                if (typeof showCustomers === 'function') {
                    showCustomers();
                }
            }

            // Refresh any visible tables
            const tables = document.querySelectorAll('table tbody');
            tables.forEach(table => {
                if (table.children.length === 0) {
                    console.log('🔄 Found empty table, triggering reload...');
                    // Trigger a general refresh
                    setTimeout(() => {
                        if (typeof loadProductsTable === 'function') loadProductsTable();
                        if (typeof loadCustomersTable === 'function') loadCustomersTable();
                    }, 500);
                }
            });

        } catch (error) {
            console.error('❌ Error refreshing sections:', error);
        }
    }

    // Get Firebase status
    getStatus() {
        return {
            isReady: this.isFirebaseReady(),
            hasConnection: this.isFirebaseReady(),
            collections: this.collections
        };
    }
}

// Create global Firebase service instance
window.firebaseService = new FirebaseService();

// Auto-sync functions
window.syncToFirebase = () => window.firebaseService.syncAllToFirebase();
window.syncFromFirebase = () => window.firebaseService.syncAllFromFirebase();

// Individual sync functions
window.syncProductsToFirebase = async () => {
    console.log('📤 رفع المنتجات إلى Firebase...');

    try {
        // Check if Firebase is ready
        if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
            console.log('⚠️ Firebase غير متاح للمزامنة');
            return false;
        }

        // Check permissions
        if (window.hasPermission && !window.hasPermission('products_write')) {
            console.log('⚠️ ليس لديك صلاحية رفع المنتجات');
            return false;
        }

        // Get products from localStorage or global variable
        let products = [];

        if (typeof window.products !== 'undefined' && Array.isArray(window.products)) {
            products = window.products;
        } else {
            const productsData = localStorage.getItem('inventory_products');
            if (productsData) {
                products = JSON.parse(productsData);
            }
        }

        if (products.length === 0) {
            console.log('ℹ️ لا توجد منتجات للرفع');
            return true; // Not an error, just no data
        }

        // Upload to Firebase
        const result = await window.firebaseService.saveProducts(products);

        if (result) {
            console.log(`✅ تم رفع ${products.length} منتج إلى Firebase بنجاح`);

            // Show notification
            if (typeof utils !== 'undefined' && utils.showNotification) {
                utils.showNotification(`تم رفع ${products.length} منتج إلى السحابة`, 'success');
            }

            return true;
        } else {
            console.log('❌ فشل في رفع المنتجات إلى Firebase');
            return false;
        }

    } catch (error) {
        console.error('❌ خطأ في رفع المنتجات إلى Firebase:', error);
        return false;
    }
};

window.loadProductsFromFirebase = async () => {
    console.log('📥 تحميل المنتجات من Firebase...');

    try {
        // Check if Firebase is ready
        if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
            console.log('⚠️ Firebase غير متاح للتحميل');
            return false;
        }

        // Check permissions
        if (window.hasPermission && !window.hasPermission('products_read')) {
            console.log('⚠️ ليس لديك صلاحية تحميل المنتجات');
            return false;
        }

        // Load from Firebase
        const firebaseProducts = await window.firebaseService.loadProducts();

        if (firebaseProducts && firebaseProducts.length > 0) {
            console.log(`📥 تم تحميل ${firebaseProducts.length} منتج من Firebase`);

            // Update localStorage
            localStorage.setItem('inventory_products', JSON.stringify(firebaseProducts));

            // Update global variable
            if (typeof window.products !== 'undefined') {
                window.products.length = 0;
                window.products.push(...firebaseProducts);
            }

            // Update UI
            if (typeof loadProductsTable === 'function') {
                loadProductsTable();
            }
            if (typeof updateDashboardStats === 'function') {
                updateDashboardStats();
            }

            // Notification disabled to prevent spam
            console.log(`✅ تم تحميل ${firebaseProducts.length} منتج من السحابة (إشعار معطل)`);

            // Only show notification for manual sync
            if (window.isManualSync) {
                if (typeof utils !== 'undefined' && utils.showNotification) {
                    utils.showNotification(`تم تحميل ${firebaseProducts.length} منتج من السحابة`, 'success');
                }
                window.isManualSync = false;
            }

            console.log('✅ تم تحديث المنتجات من Firebase بنجاح');
            return true;
        } else {
            console.log('ℹ️ لا توجد منتجات في Firebase');
            return false;
        }

    } catch (error) {
        console.error('❌ خطأ في تحميل المنتجات من Firebase:', error);
        return false;
    }
};

window.syncCustomersToFirebase = () => {
    const customers = JSON.parse(localStorage.getItem('inventory_customers') || '[]');
    return window.firebaseService.saveCustomers(customers);
};

window.syncProductsBidirectional = async () => {
    console.log('🔄 مزامنة المنتجات ثنائية الاتجاه...');

    try {
        // Check if Firebase is ready
        if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
            console.log('⚠️ Firebase غير متاح للمزامنة');
            return false;
        }

        // Get local products
        let localProducts = [];
        if (typeof window.products !== 'undefined' && Array.isArray(window.products)) {
            localProducts = window.products;
        } else {
            const productsData = localStorage.getItem('inventory_products');
            if (productsData) {
                localProducts = JSON.parse(productsData);
            }
        }

        // Get Firebase products
        const firebaseProducts = await window.firebaseService.loadProducts();

        console.log(`📊 المنتجات المحلية: ${localProducts.length}`);
        console.log(`📊 المنتجات في Firebase: ${firebaseProducts ? firebaseProducts.length : 0}`);

        // Determine sync direction
        if (!firebaseProducts || firebaseProducts.length === 0) {
            // No products in Firebase, upload local products
            if (localProducts.length > 0) {
                console.log('📤 رفع المنتجات المحلية إلى Firebase...');
                return await window.syncProductsToFirebase();
            } else {
                console.log('ℹ️ لا توجد منتجات للمزامنة');
                return true;
            }
        } else if (localProducts.length === 0) {
            // No local products, download from Firebase
            console.log('📥 تحميل المنتجات من Firebase...');
            return await window.loadProductsFromFirebase();
        } else {
            // Both have products, compare timestamps or use Firebase as source of truth
            console.log('🔄 كلا المكانين يحتوي على منتجات، استخدام Firebase كمصدر الحقيقة...');
            return await window.loadProductsFromFirebase();
        }

    } catch (error) {
        console.error('❌ خطأ في المزامنة ثنائية الاتجاه:', error);
        return false;
    }
};

window.syncUsersToFirebase = () => {
    const users = JSON.parse(localStorage.getItem('systemUsers') || '[]');
    return window.firebaseService.saveUsers(users);
};

window.syncSettingsToFirebase = () => {
    const settings = JSON.parse(localStorage.getItem('systemSettings') || '{}');
    return window.firebaseService.saveSettings(settings);
};

// Password reset functions
window.sendPasswordResetEmail = async (email) => {
    console.log('📧 إرسال بريد استرداد كلمة المرور إلى:', email);

    try {
        // Check if Firebase Auth is available
        if (!firebase.auth) {
            throw new Error('Firebase Auth غير متاح');
        }

        // Configure action code settings
        const actionCodeSettings = {
            url: window.location.origin + '/reset-password.html',
            handleCodeInApp: true
        };

        // Send password reset email
        await firebase.auth().sendPasswordResetEmail(email, actionCodeSettings);

        console.log('✅ تم إرسال بريد استرداد كلمة المرور بنجاح');
        return { success: true, message: 'تم إرسال رابط استرداد كلمة المرور إلى بريدك الإلكتروني' };

    } catch (error) {
        console.error('❌ خطأ في إرسال بريد استرداد كلمة المرور:', error);

        let errorMessage = 'حدث خطأ في إرسال بريد الاسترداد';

        switch (error.code) {
            case 'auth/user-not-found':
                errorMessage = 'البريد الإلكتروني غير مسجل في النظام';
                break;
            case 'auth/invalid-email':
                errorMessage = 'البريد الإلكتروني غير صحيح';
                break;
            case 'auth/too-many-requests':
                errorMessage = 'تم إرسال عدد كبير من الطلبات. يرجى المحاولة لاحقاً';
                break;
            case 'auth/network-request-failed':
                errorMessage = 'خطأ في الاتصال بالإنترنت';
                break;
            default:
                errorMessage = error.message || errorMessage;
        }

        return { success: false, message: errorMessage };
    }
};

window.verifyPasswordResetCode = async (actionCode) => {
    console.log('🔍 التحقق من رمز استرداد كلمة المرور...');

    try {
        // Verify the action code and get the email
        const email = await firebase.auth().verifyPasswordResetCode(actionCode);

        console.log('✅ رمز استرداد كلمة المرور صحيح للبريد:', email);
        return { success: true, email: email };

    } catch (error) {
        console.error('❌ خطأ في التحقق من رمز الاسترداد:', error);

        let errorMessage = 'رابط استرداد كلمة المرور غير صحيح أو منتهي الصلاحية';

        switch (error.code) {
            case 'auth/expired-action-code':
                errorMessage = 'رابط استرداد كلمة المرور منتهي الصلاحية';
                break;
            case 'auth/invalid-action-code':
                errorMessage = 'رابط استرداد كلمة المرور غير صحيح';
                break;
            case 'auth/user-disabled':
                errorMessage = 'هذا الحساب معطل';
                break;
        }

        return { success: false, message: errorMessage };
    }
};

window.confirmPasswordReset = async (actionCode, newPassword) => {
    console.log('🔐 تأكيد تغيير كلمة المرور...');

    try {
        // Confirm the password reset
        await firebase.auth().confirmPasswordReset(actionCode, newPassword);

        console.log('✅ تم تغيير كلمة المرور بنجاح');
        return { success: true, message: 'تم تغيير كلمة المرور بنجاح' };

    } catch (error) {
        console.error('❌ خطأ في تغيير كلمة المرور:', error);

        let errorMessage = 'حدث خطأ في تغيير كلمة المرور';

        switch (error.code) {
            case 'auth/expired-action-code':
                errorMessage = 'رابط استرداد كلمة المرور منتهي الصلاحية';
                break;
            case 'auth/invalid-action-code':
                errorMessage = 'رابط استرداد كلمة المرور غير صحيح';
                break;
            case 'auth/weak-password':
                errorMessage = 'كلمة المرور ضعيفة جداً';
                break;
            default:
                errorMessage = error.message || errorMessage;
        }

        return { success: false, message: errorMessage };
    }
};

// Setup realtime listeners for products
window.setupProductsRealtimeSync = () => {
    console.log('🔗 إعداد المزامنة اللحظية للمنتجات...');

    try {
        if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
            console.log('⚠️ Firebase غير متاح للمزامنة اللحظية');
            return false;
        }

        // Listen for products changes in Firebase
        const productsCollection = window.firebaseService.db.collection('products');

        const unsubscribe = productsCollection.onSnapshot((snapshot) => {
            console.log('🔄 تغيير في المنتجات في Firebase...');

            snapshot.docChanges().forEach((change) => {
                if (change.type === 'modified' || change.type === 'added') {
                    const data = change.doc.data();
                    if (data.products && Array.isArray(data.products)) {
                        console.log(`📥 تحديث المنتجات: ${data.products.length} منتج`);

                        // Update local storage
                        localStorage.setItem('inventory_products', JSON.stringify(data.products));

                        // Update global variable
                        if (typeof window.products !== 'undefined') {
                            window.products.length = 0;
                            window.products.push(...data.products);
                        }

                        // Update UI with stability manager
                        if (window.uiStabilityManager) {
                            console.log('🛡️ تحديث الواجهة باستخدام مدير الاستقرار');
                            window.uiStabilityManager.stableLoadProductsTable();
                            window.uiStabilityManager.stableUpdateDashboardStats();
                        } else {
                            // Fallback with delay
                            setTimeout(() => {
                                if (typeof loadProductsTable === 'function') {
                                    loadProductsTable();
                                }
                            }, 200);
                            setTimeout(() => {
                                if (typeof updateDashboardStats === 'function') {
                                    updateDashboardStats();
                                }
                            }, 400);
                        }

                        // Notification disabled to prevent spam
                        console.log('✅ تم تحديث المنتجات من السحابة (إشعار معطل)');

                        // Only show notification for manual sync
                        if (window.isManualSync) {
                            if (typeof utils !== 'undefined' && utils.showNotification) {
                                utils.showNotification('تم تحديث المنتجات من السحابة', 'info');
                            }
                            window.isManualSync = false;
                        }
                    }
                }
            });
        }, (error) => {
            console.error('❌ خطأ في مستمع المنتجات:', error);
        });

        // Store unsubscribe function
        window.productsRealtimeUnsubscribe = unsubscribe;

        console.log('✅ تم إعداد المزامنة اللحظية للمنتجات');
        return true;

    } catch (error) {
        console.error('❌ خطأ في إعداد المزامنة اللحظية للمنتجات:', error);
        return false;
    }
};

window.syncLoginCredentialsToFirebase = () => {
    const credentials = JSON.parse(localStorage.getItem('loginCredentials') || '{}');
    return window.firebaseService.saveLoginCredentials(credentials);
};

// Force UI refresh function
window.forceUIRefresh = () => {
    console.log('🔄 إجبار تحديث الواجهة...');

    if (window.firebaseService) {
        window.firebaseService.triggerUIUpdates();
    } else {
        // Manual refresh if firebaseService not available
        try {
            // Reload global variables manually
            const products = JSON.parse(localStorage.getItem('inventory_products') || '[]');
            const customers = JSON.parse(localStorage.getItem('inventory_customers') || '[]');
            const users = JSON.parse(localStorage.getItem('systemUsers') || '[]');

            if (typeof window.products !== 'undefined') {
                window.products = products;
            }
            if (typeof window.customers !== 'undefined') {
                window.customers = customers;
            }
            if (typeof window.systemUsers !== 'undefined') {
                window.systemUsers = users;
            }

            // Call update functions
            if (typeof updateDashboardStats === 'function') updateDashboardStats();
            if (typeof loadProductsTable === 'function') loadProductsTable();
            if (typeof loadCustomersTable === 'function') loadCustomersTable();
            if (typeof updateUsersTable === 'function') updateUsersTable();

            console.log('✅ تم تحديث الواجهة يدوياً');
            showSyncStatus('✅ تم تحديث الواجهة بالبيانات الجديدة', 'success');

        } catch (error) {
            console.error('❌ خطأ في تحديث الواجهة:', error);
            showSyncStatus('❌ فشل في تحديث الواجهة', 'error');
        }
    }
};

console.log('🔥 Firebase service ready:', window.firebaseService.getStatus());

// Auto-sync when data changes
function setupAutoSync() {
    // Track sync operations to prevent loops
    let syncInProgress = new Set();
    let lastSyncTime = new Map();
    const SYNC_COOLDOWN = 3000; // 3 seconds cooldown

    // Override localStorage.setItem to trigger auto-sync with permissions
    const originalSetItem = localStorage.setItem;
    localStorage.setItem = function(key, value) {
        originalSetItem.call(this, key, value);

        console.log(`💾 localStorage updated: ${key}`);

        try {
            // Check if sync is already in progress for this key
            if (syncInProgress.has(key)) {
                console.log(`⏸️ Sync already in progress for ${key}, skipping...`);
                return;
            }

            // Check cooldown period
            const now = Date.now();
            const lastSync = lastSyncTime.get(key) || 0;
            if (now - lastSync < SYNC_COOLDOWN) {
                console.log(`⏸️ Sync cooldown active for ${key}, skipping...`);
                return;
            }

            // Auto-sync specific data types to Firebase with permission checks
            if (window.firebaseService && window.firebaseService.isFirebaseReady()) {
                switch(key) {
                    case 'inventory_products':
                        if (!window.hasPermission || window.hasPermission('products_write')) {
                            syncInProgress.add(key);
                            lastSyncTime.set(key, now);
                            setTimeout(() => {
                                console.log('🔄 Auto-syncing products to Firebase...');
                                window.syncProductsToFirebase().finally(() => {
                                    syncInProgress.delete(key);
                                });
                            }, 1000);
                        }
                        break;
                    case 'inventory_customers':
                        if (!window.hasPermission || window.hasPermission('customers_write')) {
                            syncInProgress.add(key);
                            lastSyncTime.set(key, now);
                            setTimeout(() => {
                                console.log('🔄 Auto-syncing customers to Firebase...');
                                window.syncCustomersToFirebase().finally(() => {
                                    syncInProgress.delete(key);
                                });
                            }, 1000);
                        }
                        break;
                    case 'systemUsers':
                        if (!window.hasPermission || window.hasPermission('users_write')) {
                            syncInProgress.add(key);
                            lastSyncTime.set(key, now);
                            setTimeout(() => {
                                console.log('🔄 Auto-syncing users to Firebase...');
                                window.syncUsersToFirebase().finally(() => {
                                    syncInProgress.delete(key);
                                });
                            }, 1000);
                        }
                        break;
                    case 'systemSettings':
                        if (!window.hasPermission || window.hasPermission('settings_write')) {
                            syncInProgress.add(key);
                            lastSyncTime.set(key, now);
                            setTimeout(() => {
                                console.log('🔄 Auto-syncing settings to Firebase...');
                                window.syncSettingsToFirebase().finally(() => {
                                    syncInProgress.delete(key);
                                });
                            }, 1000);
                        }
                        break;
                    case 'loginCredentials':
                        if (!window.hasPermission || window.hasPermission('system_admin')) {
                            syncInProgress.add(key);
                            lastSyncTime.set(key, now);
                            setTimeout(() => {
                                console.log('🔄 Auto-syncing login credentials to Firebase...');
                                window.syncLoginCredentialsToFirebase().finally(() => {
                                    syncInProgress.delete(key);
                                });
                            }, 1000);
                        }
                        break;
                }
            }

            // Notify realtime sync system (with throttling)
            if (window.realtimeSyncSystem && window.realtimeSyncSystem.isInitialized && !window.realtimeSyncSystem.isSyncing) {
                window.realtimeSyncSystem.handleLocalChange && window.realtimeSyncSystem.handleLocalChange(key, value);
            }

        } catch (error) {
            console.error('❌ Error in auto-sync:', error);
            // Clear sync flag on error
            syncInProgress.delete(key);
        }
    };
}

// Setup auto-sync when Firebase is ready
if (isFirebaseReady) {
    setupAutoSync();
    console.log('✅ Auto-sync enabled for Firebase');

    // Setup realtime sync for products
    setTimeout(() => {
        if (window.setupProductsRealtimeSync) {
            window.setupProductsRealtimeSync();
        }

        // Auto-sync products if needed
        if (window.syncProductsBidirectional) {
            window.syncProductsBidirectional();
        }
    }, 3000);

    // Show sync status in UI if available
    setTimeout(() => {
        showSyncStatus('✅ Firebase متصل - المزامنة التلقائية مفعلة', 'success');
    }, 1000);
} else {
    console.log('⚠️ Auto-sync disabled - Firebase not ready');
    setTimeout(() => {
        showSyncStatus('⚠️ Firebase غير متصل - العمل في وضع محلي', 'warning');
    }, 1000);
}

// Function to show sync status in UI
function showSyncStatus(message, type = 'info') {
    try {
        // Try to find existing sync status element
        let statusElement = document.getElementById('firebase-sync-status');

        if (!statusElement) {
            // Create sync status element if it doesn't exist
            statusElement = document.createElement('div');
            statusElement.id = 'firebase-sync-status';
            statusElement.style.cssText = `
                position: fixed;
                top: 10px;
                right: 10px;
                padding: 10px 15px;
                border-radius: 5px;
                font-size: 14px;
                font-weight: bold;
                z-index: 10000;
                max-width: 300px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.2);
                transition: all 0.3s ease;
            `;
            document.body.appendChild(statusElement);
        }

        // Set message and style based on type
        statusElement.textContent = message;

        switch(type) {
            case 'success':
                statusElement.style.backgroundColor = '#d4edda';
                statusElement.style.color = '#155724';
                statusElement.style.border = '1px solid #c3e6cb';
                break;
            case 'error':
                statusElement.style.backgroundColor = '#f8d7da';
                statusElement.style.color = '#721c24';
                statusElement.style.border = '1px solid #f5c6cb';
                break;
            case 'warning':
                statusElement.style.backgroundColor = '#fff3cd';
                statusElement.style.color = '#856404';
                statusElement.style.border = '1px solid #ffeaa7';
                break;
            default:
                statusElement.style.backgroundColor = '#d1ecf1';
                statusElement.style.color = '#0c5460';
                statusElement.style.border = '1px solid #bee5eb';
        }

        // Auto-hide after 5 seconds
        setTimeout(() => {
            if (statusElement && statusElement.parentNode) {
                statusElement.style.opacity = '0';
                setTimeout(() => {
                    if (statusElement && statusElement.parentNode) {
                        statusElement.parentNode.removeChild(statusElement);
                    }
                }, 300);
            }
        }, 5000);

    } catch (error) {
        console.error('Error showing sync status:', error);
    }
}

// Function to check sync status
window.checkFirebaseSyncStatus = async function() {
    if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
        console.log('❌ Firebase not ready');
        return false;
    }

    console.log('🔍 Checking Firebase sync status...');

    try {
        // Get local data counts (using correct key names)
        const localProducts = JSON.parse(localStorage.getItem('inventory_products') || '[]');
        const localCustomers = JSON.parse(localStorage.getItem('inventory_customers') || '[]');
        const localUsers = JSON.parse(localStorage.getItem('systemUsers') || '[]');
        const localSettings = JSON.parse(localStorage.getItem('systemSettings') || '{}');
        const localCredentials = JSON.parse(localStorage.getItem('loginCredentials') || '{}');

        // Get Firebase data counts
        const firebaseProducts = await window.firebaseService.loadProducts();
        const firebaseCustomers = await window.firebaseService.loadCustomers();
        const firebaseUsers = await window.firebaseService.loadUsers();
        const firebaseSettings = await window.firebaseService.loadSettings();
        const firebaseCredentials = await window.firebaseService.loadLoginCredentials();

        console.log('📊 Sync Status Report:');
        console.log(`📦 Products - Local: ${localProducts.length}, Firebase: ${firebaseProducts ? firebaseProducts.length : 0}`);
        console.log(`👥 Customers - Local: ${localCustomers.length}, Firebase: ${firebaseCustomers ? firebaseCustomers.length : 0}`);
        console.log(`🔐 Users - Local: ${localUsers.length}, Firebase: ${firebaseUsers ? firebaseUsers.length : 0}`);
        console.log(`⚙️ Settings - Local: ${Object.keys(localSettings).length} keys, Firebase: ${firebaseSettings ? Object.keys(firebaseSettings).length : 0} keys`);
        console.log(`🔑 Credentials - Local: ${Object.keys(localCredentials).length} keys, Firebase: ${firebaseCredentials ? Object.keys(firebaseCredentials).length : 0} keys`);

        return {
            products: { local: localProducts.length, firebase: firebaseProducts ? firebaseProducts.length : 0 },
            customers: { local: localCustomers.length, firebase: firebaseCustomers ? firebaseCustomers.length : 0 },
            users: { local: localUsers.length, firebase: firebaseUsers ? firebaseUsers.length : 0 },
            settings: { local: Object.keys(localSettings).length, firebase: firebaseSettings ? Object.keys(firebaseSettings).length : 0 },
            credentials: { local: Object.keys(localCredentials).length, firebase: firebaseCredentials ? Object.keys(firebaseCredentials).length : 0 }
        };
    } catch (error) {
        console.error('❌ Error checking sync status:', error);
        return false;
    }
};
