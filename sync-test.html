<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 اختبار المزامنة - النسور الماسية</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 10px;
        }

        .test-section {
            margin-bottom: 20px;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .btn.primary {
            background: #007bff;
            color: white;
        }

        .btn.success {
            background: #28a745;
            color: white;
        }

        .btn.warning {
            background: #ffc107;
            color: #212529;
        }

        .btn.danger {
            background: #dc3545;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .log {
            background: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 15px;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 اختبار مزامنة المنتجات</h1>
            <p>أداة تشخيص وإصلاح مشاكل المزامنة</p>
        </div>

        <!-- Status Overview -->
        <div class="test-section">
            <h3>📊 حالة النظام</h3>
            <div class="stats" id="statsContainer">
                <!-- Will be populated by JavaScript -->
            </div>
            <div id="systemStatus"></div>
        </div>

        <!-- Quick Tests -->
        <div class="test-section">
            <h3>🧪 اختبارات سريعة</h3>
            <button class="btn primary" onclick="testFirebaseConnection()">
                🔥 اختبار اتصال Firebase
            </button>
            <button class="btn success" onclick="testProductsSync()">
                📦 اختبار مزامنة المنتجات
            </button>
            <button class="btn warning" onclick="testBidirectionalSync()">
                🔄 اختبار المزامنة ثنائية الاتجاه
            </button>
            <button class="btn danger" onclick="clearTestLog()">
                🗑️ مسح السجل
            </button>
        </div>

        <!-- Manual Sync -->
        <div class="test-section">
            <h3>🔧 مزامنة يدوية</h3>
            <button class="btn primary" onclick="manualUpload()">
                📤 رفع المنتجات إلى Firebase
            </button>
            <button class="btn success" onclick="manualDownload()">
                📥 تحميل المنتجات من Firebase
            </button>
            <button class="btn warning" onclick="forceSync()">
                ⚡ مزامنة قسرية
            </button>
        </div>

        <!-- Test Log -->
        <div class="test-section">
            <h3>📝 سجل الاختبار</h3>
            <div class="log" id="testLog">
                <div>[INFO] مرحباً بك في أداة اختبار المزامنة</div>
                <div>[INFO] جاري تهيئة النظام...</div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>
    <script src="firebase-config.js"></script>
    
    <script>
        // Initialize test tool
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 تهيئة أداة اختبار المزامنة...', 'info');
            
            setTimeout(() => {
                updateSystemStatus();
                runInitialTests();
            }, 2000);
        });

        // Log function
        function log(message, type = 'info') {
            const logContainer = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                info: '#00ff00',
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107'
            };
            
            const logEntry = document.createElement('div');
            logEntry.style.color = colors[type] || colors.info;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // Clear test log
        function clearTestLog() {
            document.getElementById('testLog').innerHTML = '';
            log('تم مسح السجل', 'info');
        }

        // Update system status
        function updateSystemStatus() {
            const statusContainer = document.getElementById('systemStatus');
            const statsContainer = document.getElementById('statsContainer');
            
            // Check Firebase status
            const isFirebaseReady = window.firebaseService && window.firebaseService.isFirebaseReady();
            
            // Check sync functions
            const hasSyncToFirebase = typeof window.syncProductsToFirebase === 'function';
            const hasLoadFromFirebase = typeof window.loadProductsFromFirebase === 'function';
            const hasBidirectionalSync = typeof window.syncProductsBidirectional === 'function';
            
            // Get products count
            let localProductsCount = 0;
            try {
                const productsData = localStorage.getItem('inventory_products');
                if (productsData) {
                    localProductsCount = JSON.parse(productsData).length;
                }
            } catch (error) {
                log(`خطأ في قراءة المنتجات المحلية: ${error.message}`, 'error');
            }

            // Update stats
            const stats = [
                {
                    label: 'حالة Firebase',
                    value: isFirebaseReady ? 'متصل' : 'غير متصل',
                    color: isFirebaseReady ? '#28a745' : '#dc3545'
                },
                {
                    label: 'المنتجات المحلية',
                    value: localProductsCount,
                    color: localProductsCount > 0 ? '#28a745' : '#ffc107'
                },
                {
                    label: 'وظائف المزامنة',
                    value: `${[hasSyncToFirebase, hasLoadFromFirebase, hasBidirectionalSync].filter(Boolean).length}/3`,
                    color: hasSyncToFirebase && hasLoadFromFirebase && hasBidirectionalSync ? '#28a745' : '#ffc107'
                }
            ];

            let statsHtml = '';
            stats.forEach(stat => {
                statsHtml += `
                    <div class="stat-card">
                        <div class="stat-value" style="color: ${stat.color}">${stat.value}</div>
                        <div class="stat-label">${stat.label}</div>
                    </div>
                `;
            });
            statsContainer.innerHTML = statsHtml;

            // Update status message
            let statusClass = 'success';
            let statusMessage = '✅ جميع الأنظمة تعمل بشكل طبيعي';
            
            if (!isFirebaseReady) {
                statusClass = 'error';
                statusMessage = '❌ Firebase غير متصل';
            } else if (!hasSyncToFirebase || !hasLoadFromFirebase || !hasBidirectionalSync) {
                statusClass = 'warning';
                statusMessage = '⚠️ بعض وظائف المزامنة غير متاحة';
            } else if (localProductsCount === 0) {
                statusClass = 'warning';
                statusMessage = '⚠️ لا توجد منتجات محلية';
            }

            statusContainer.innerHTML = `<div class="status ${statusClass}">${statusMessage}</div>`;
        }

        // Run initial tests
        function runInitialTests() {
            log('🔍 تشغيل الاختبارات الأولية...', 'info');
            
            // Test Firebase connection
            testFirebaseConnection();
            
            // Test sync functions availability
            setTimeout(() => {
                testSyncFunctionsAvailability();
            }, 1000);
        }

        // Test Firebase connection
        function testFirebaseConnection() {
            log('🔥 اختبار اتصال Firebase...', 'info');
            
            if (window.firebaseService && window.firebaseService.isFirebaseReady()) {
                log('✅ Firebase متصل ويعمل بشكل طبيعي', 'success');
                return true;
            } else {
                log('❌ Firebase غير متصل أو غير جاهز', 'error');
                return false;
            }
        }

        // Test sync functions availability
        function testSyncFunctionsAvailability() {
            log('🔧 اختبار توفر وظائف المزامنة...', 'info');
            
            const functions = [
                { name: 'syncProductsToFirebase', label: 'رفع المنتجات' },
                { name: 'loadProductsFromFirebase', label: 'تحميل المنتجات' },
                { name: 'syncProductsBidirectional', label: 'المزامنة ثنائية الاتجاه' }
            ];

            functions.forEach(func => {
                if (typeof window[func.name] === 'function') {
                    log(`✅ ${func.label}: متاح`, 'success');
                } else {
                    log(`❌ ${func.label}: غير متاح`, 'error');
                }
            });
        }

        // Test products sync
        async function testProductsSync() {
            log('📦 اختبار مزامنة المنتجات...', 'info');
            
            try {
                if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
                    log('❌ Firebase غير متاح للاختبار', 'error');
                    return;
                }

                // Test upload
                if (typeof window.syncProductsToFirebase === 'function') {
                    log('📤 اختبار رفع المنتجات...', 'info');
                    const uploadResult = await window.syncProductsToFirebase();
                    if (uploadResult) {
                        log('✅ نجح اختبار رفع المنتجات', 'success');
                    } else {
                        log('⚠️ فشل اختبار رفع المنتجات', 'warning');
                    }
                }

                // Test download
                if (typeof window.loadProductsFromFirebase === 'function') {
                    log('📥 اختبار تحميل المنتجات...', 'info');
                    const downloadResult = await window.loadProductsFromFirebase();
                    if (downloadResult) {
                        log('✅ نجح اختبار تحميل المنتجات', 'success');
                    } else {
                        log('⚠️ فشل اختبار تحميل المنتجات', 'warning');
                    }
                }

                updateSystemStatus();
                
            } catch (error) {
                log(`❌ خطأ في اختبار مزامنة المنتجات: ${error.message}`, 'error');
            }
        }

        // Test bidirectional sync
        async function testBidirectionalSync() {
            log('🔄 اختبار المزامنة ثنائية الاتجاه...', 'info');
            
            try {
                if (typeof window.syncProductsBidirectional === 'function') {
                    const result = await window.syncProductsBidirectional();
                    if (result) {
                        log('✅ نجح اختبار المزامنة ثنائية الاتجاه', 'success');
                    } else {
                        log('⚠️ فشل اختبار المزامنة ثنائية الاتجاه', 'warning');
                    }
                } else {
                    log('❌ وظيفة المزامنة ثنائية الاتجاه غير متاحة', 'error');
                }

                updateSystemStatus();
                
            } catch (error) {
                log(`❌ خطأ في اختبار المزامنة ثنائية الاتجاه: ${error.message}`, 'error');
            }
        }

        // Manual upload
        async function manualUpload() {
            log('📤 رفع يدوي للمنتجات...', 'info');
            
            try {
                if (typeof window.syncProductsToFirebase === 'function') {
                    const result = await window.syncProductsToFirebase();
                    if (result) {
                        log('✅ تم رفع المنتجات بنجاح', 'success');
                    } else {
                        log('❌ فشل في رفع المنتجات', 'error');
                    }
                } else {
                    log('❌ وظيفة رفع المنتجات غير متاحة', 'error');
                }
                
                updateSystemStatus();
                
            } catch (error) {
                log(`❌ خطأ في الرفع اليدوي: ${error.message}`, 'error');
            }
        }

        // Manual download
        async function manualDownload() {
            log('📥 تحميل يدوي للمنتجات...', 'info');
            
            try {
                if (typeof window.loadProductsFromFirebase === 'function') {
                    const result = await window.loadProductsFromFirebase();
                    if (result) {
                        log('✅ تم تحميل المنتجات بنجاح', 'success');
                    } else {
                        log('❌ فشل في تحميل المنتجات', 'error');
                    }
                } else {
                    log('❌ وظيفة تحميل المنتجات غير متاحة', 'error');
                }
                
                updateSystemStatus();
                
            } catch (error) {
                log(`❌ خطأ في التحميل اليدوي: ${error.message}`, 'error');
            }
        }

        // Force sync
        async function forceSync() {
            log('⚡ مزامنة قسرية...', 'info');
            
            try {
                // Try all sync methods
                const methods = [
                    { name: 'syncProductsBidirectional', label: 'المزامنة ثنائية الاتجاه' },
                    { name: 'syncProductsToFirebase', label: 'رفع المنتجات' },
                    { name: 'loadProductsFromFirebase', label: 'تحميل المنتجات' }
                ];

                for (const method of methods) {
                    if (typeof window[method.name] === 'function') {
                        log(`🔄 تجربة ${method.label}...`, 'info');
                        const result = await window[method.name]();
                        if (result) {
                            log(`✅ نجح ${method.label}`, 'success');
                            break;
                        } else {
                            log(`⚠️ فشل ${method.label}`, 'warning');
                        }
                    }
                }
                
                updateSystemStatus();
                
            } catch (error) {
                log(`❌ خطأ في المزامنة القسرية: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
