// Firebase Configuration for Netlify Deployment
// This version handles environment variables and deployment issues

// Detect environment
const isProduction = window.location.hostname !== 'localhost' && window.location.hostname !== '127.0.0.1';
const isNetlify = window.location.hostname.includes('netlify.app');

console.log('🌐 Environment detected:', {
    isProduction,
    isNetlify,
    hostname: window.location.hostname,
    protocol: window.location.protocol
});

// Firebase Configuration with fallbacks
const firebaseConfig = {
    apiKey: "AIzaSyBDjmRuPy0nfCr8VdGhc6THkjKuz6LMr9g",
    authDomain: "diamond-eagles-store.firebaseapp.com",
    projectId: "diamond-eagles-store",
    storageBucket: "diamond-eagles-store.firebasestorage.app",
    messagingSenderId: "241294391606",
    appId: "1:241294391606:web:80d97d1bcdaea1948f9b97"
};

// Enhanced Firebase initialization for Netlify
let app;
let db;
let isFirebaseReady = false;

try {
    console.log('🔥 Initializing Firebase for Netlify...');
    
    // Initialize Firebase
    app = firebase.initializeApp(firebaseConfig);
    db = firebase.firestore();
    
    // Configure Firestore for production
    if (isProduction) {
        console.log('🏭 Production mode: Configuring Firestore settings...');
        
        // Enable offline persistence for better performance
        db.enablePersistence({
            synchronizeTabs: true
        }).then(() => {
            console.log('✅ Firestore offline persistence enabled');
        }).catch((err) => {
            console.warn('⚠️ Firestore persistence failed:', err.code);
            if (err.code === 'failed-precondition') {
                console.log('ℹ️ Multiple tabs open, persistence only available in one tab at a time');
            } else if (err.code === 'unimplemented') {
                console.log('ℹ️ Browser doesn\'t support persistence');
            }
        });
    }
    
    // Test connection
    db.collection('test').doc('connection').set({
        timestamp: firebase.firestore.FieldValue.serverTimestamp(),
        environment: isNetlify ? 'netlify' : (isProduction ? 'production' : 'development'),
        hostname: window.location.hostname,
        userAgent: navigator.userAgent.substring(0, 100)
    }).then(() => {
        console.log('✅ Firebase connection test successful');
        isFirebaseReady = true;
        
        // Show success notification
        if (typeof showSyncStatus === 'function') {
            showSyncStatus('✅ Firebase متصل ويعمل على Netlify', 'success');
        }
    }).catch((error) => {
        console.error('❌ Firebase connection test failed:', error);
        
        // Show error notification
        if (typeof showSyncStatus === 'function') {
            showSyncStatus('❌ فشل الاتصال بـ Firebase على Netlify', 'error');
        }
        
        // Try to diagnose the issue
        diagnoseProblem(error);
    });
    
} catch (error) {
    console.error('❌ Firebase initialization failed:', error);
    diagnoseProblem(error);
}

// Diagnose Firebase connection problems
function diagnoseProblem(error) {
    console.log('🔍 Diagnosing Firebase connection problem...');
    
    const diagnosis = {
        error: error.message,
        code: error.code,
        environment: {
            hostname: window.location.hostname,
            protocol: window.location.protocol,
            isNetlify: isNetlify,
            isProduction: isProduction
        },
        suggestions: []
    };
    
    // Common issues and solutions
    if (error.code === 'permission-denied') {
        diagnosis.suggestions.push('تحقق من قواعد Firestore Security Rules');
        diagnosis.suggestions.push('تأكد من إضافة نطاق Netlify إلى Authorized domains');
    }
    
    if (error.message.includes('CORS')) {
        diagnosis.suggestions.push('مشكلة CORS - تحقق من إعدادات Firebase');
        diagnosis.suggestions.push('أضف نطاق Netlify إلى Firebase Console');
    }
    
    if (error.code === 'unavailable') {
        diagnosis.suggestions.push('مشكلة في الشبكة أو خدمة Firebase');
        diagnosis.suggestions.push('تحقق من حالة خدمة Firebase');
    }
    
    console.log('📋 Firebase Diagnosis:', diagnosis);
    
    // Show user-friendly error
    if (typeof showSyncStatus === 'function') {
        const message = `❌ مشكلة في الاتصال بـ Firebase\nالكود: ${error.code || 'unknown'}\nالحل: ${diagnosis.suggestions[0] || 'تحقق من الإعدادات'}`;
        showSyncStatus(message, 'error');
    }
}

// Enhanced Firebase Service for Netlify
class NetlifyFirebaseService {
    constructor() {
        this.app = app;
        this.db = db;
        this.collections = {
            products: 'products',
            customers: 'customers',
            users: 'users',
            settings: 'settings',
            loginCredentials: 'loginCredentials',
            backups: 'backups'
        };
    }

    isFirebaseReady() {
        return isFirebaseReady && this.db !== null;
    }

    getStatus() {
        return {
            isReady: this.isFirebaseReady(),
            hasConnection: isFirebaseReady,
            environment: isNetlify ? 'netlify' : (isProduction ? 'production' : 'development'),
            hostname: window.location.hostname
        };
    }

    // Test connection specifically for Netlify
    async testNetlifyConnection() {
        console.log('🧪 Testing Firebase connection on Netlify...');
        
        try {
            // Test write
            await this.db.collection('test').doc('netlify-test').set({
                timestamp: firebase.firestore.FieldValue.serverTimestamp(),
                test: 'netlify-connection',
                hostname: window.location.hostname
            });
            
            // Test read
            const doc = await this.db.collection('test').doc('netlify-test').get();
            
            if (doc.exists) {
                console.log('✅ Netlify Firebase connection test passed');
                return true;
            } else {
                throw new Error('Document not found after write');
            }
            
        } catch (error) {
            console.error('❌ Netlify Firebase connection test failed:', error);
            diagnoseProblem(error);
            return false;
        }
    }

    // All other methods from original firebase-config.js
    // (Copy them from the original file)
    
    async saveProducts(products) {
        if (!this.isFirebaseReady()) {
            console.warn('⚠️ Firebase not ready, saving locally only');
            return false;
        }

        try {
            await this.db.collection(this.collections.products).doc('data').set({
                products: products,
                lastUpdated: firebase.firestore.FieldValue.serverTimestamp(),
                version: Date.now(),
                environment: isNetlify ? 'netlify' : 'other'
            });
            
            console.log('✅ Products saved to Firebase from Netlify');
            return true;
        } catch (error) {
            console.error('❌ Error saving products to Firebase:', error);
            diagnoseProblem(error);
            return false;
        }
    }

    async loadProducts() {
        if (!this.isFirebaseReady()) {
            console.warn('⚠️ Firebase not ready, loading from localStorage');
            return null;
        }

        try {
            const doc = await this.db.collection(this.collections.products).doc('data').get();
            
            if (doc.exists) {
                const data = doc.data();
                console.log('✅ Products loaded from Firebase on Netlify');
                return data.products || [];
            } else {
                console.log('ℹ️ No products found in Firebase');
                return [];
            }
        } catch (error) {
            console.error('❌ Error loading products from Firebase:', error);
            diagnoseProblem(error);
            return null;
        }
    }

    // Add other methods as needed...
}

// Initialize the service
window.netlifyFirebaseService = new NetlifyFirebaseService();

// Test connection on load
if (isNetlify) {
    setTimeout(() => {
        window.netlifyFirebaseService.testNetlifyConnection();
    }, 2000);
}

// Export for compatibility
window.firebaseService = window.netlifyFirebaseService;

console.log('🔥 Netlify Firebase service ready:', window.netlifyFirebaseService.getStatus());
