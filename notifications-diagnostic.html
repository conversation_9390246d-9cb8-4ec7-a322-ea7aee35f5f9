<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 تشخيص الإشعارات</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            direction: rtl;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        
        .header p {
            color: #666;
            font-size: 1.1em;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        
        .card-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.2em;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .card-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .card-detail {
            color: #6c757d;
            font-size: 0.9em;
            margin: 5px 0;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
        }
        
        .status-active { background: #28a745; }
        .status-paused { background: #ffc107; }
        .status-disabled { background: #dc3545; }
        
        .controls {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            justify-content: center;
            margin: 30px 0;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .log-container {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }
        
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        
        .log-info { background: rgba(59, 130, 246, 0.2); }
        .log-success { background: rgba(34, 197, 94, 0.2); }
        .log-warning { background: rgba(245, 158, 11, 0.2); }
        .log-error { background: rgba(239, 68, 68, 0.2); }
        
        .test-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .test-section h3 {
            color: #856404;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 تشخيص نظام الإشعارات</h1>
            <p>مراقبة وتحكم في إشعارات المنتجات والمزامنة</p>
        </div>

        <!-- Status Cards -->
        <div class="grid" id="statusCards">
            <!-- Will be populated by JavaScript -->
        </div>

        <!-- Controls -->
        <div class="controls">
            <button class="btn btn-primary" onclick="refreshStatus()">
                <i class="fas fa-sync-alt"></i> تحديث الحالة
            </button>
            <button class="btn btn-success" onclick="testNotification()">
                <i class="fas fa-bell"></i> اختبار إشعار
            </button>
            <button class="btn btn-warning" onclick="toggleProductsNotifications()">
                <i class="fas fa-box"></i> تبديل إشعارات المنتجات
            </button>
            <button class="btn btn-danger" onclick="clearAllNotifications()">
                <i class="fas fa-bell-slash"></i> مسح جميع الإشعارات
            </button>
            <button class="btn btn-primary" onclick="checkUIStability()">
                <i class="fas fa-shield-alt"></i> فحص استقرار الواجهة
            </button>
            <button class="btn btn-success" onclick="toggleSyncCoordinator()">
                <i class="fas fa-traffic-light"></i> تبديل منسق المزامنة
            </button>
            <button class="btn btn-warning" onclick="forceSyncNow()">
                <i class="fas fa-bolt"></i> مزامنة قسرية
            </button>
        </div>

        <!-- Test Section -->
        <div class="test-section">
            <h3>🧪 اختبار الإشعارات</h3>
            <div class="controls">
                <button class="btn btn-primary" onclick="testProductsLoadNotification()">
                    اختبار "تم تحميل منتج"
                </button>
                <button class="btn btn-primary" onclick="testProductsUpdateNotification()">
                    اختبار "تم تحديث المنتجات"
                </button>
                <button class="btn btn-primary" onclick="testMultipleNotifications()">
                    اختبار إشعارات متعددة
                </button>
            </div>
        </div>

        <!-- Log Container -->
        <div class="log-container" id="logContainer">
            <div class="log-entry log-info">📋 سجل الأحداث - جاهز للمراقبة</div>
        </div>
    </div>

    <!-- Load notification manager, UI stability manager, and sync coordinator -->
    <script src="sync-coordinator.js"></script>
    <script src="notification-manager.js"></script>
    <script src="ui-stability-manager.js"></script>
    
    <script>
        let logContainer;
        let statusUpdateInterval;

        // Initialize diagnostic
        document.addEventListener('DOMContentLoaded', function() {
            logContainer = document.getElementById('logContainer');
            log('🚀 تم تهيئة نظام التشخيص', 'info');
            
            // Initial status update
            refreshStatus();
            
            // Auto-refresh every 5 seconds
            statusUpdateInterval = setInterval(refreshStatus, 5000);
            
            // Override console.log to capture logs
            const originalLog = console.log;
            console.log = function(...args) {
                originalLog.apply(console, args);
                if (args[0] && typeof args[0] === 'string') {
                    if (args[0].includes('⏸️') || args[0].includes('📢') || args[0].includes('🔔')) {
                        log(args.join(' '), 'info');
                    }
                }
            };
        });

        // Log function
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // Keep only last 50 entries
            while (logContainer.children.length > 50) {
                logContainer.removeChild(logContainer.firstChild);
            }
        }

        // Refresh status
        function refreshStatus() {
            const statusCards = document.getElementById('statusCards');
            
            // Get notification manager status
            const notificationManager = window.notificationManager;
            const stats = notificationManager ? notificationManager.getStats() : null;
            
            const cards = [
                {
                    title: '📢 حالة مدير الإشعارات',
                    value: notificationManager ? 'مفعل' : 'غير مفعل',
                    status: notificationManager ? 'active' : 'disabled',
                    details: notificationManager ? [
                        `الإشعارات المعروضة: ${stats.visibleCount}`,
                        `طابور الانتظار: ${stats.queueLength}`,
                        `إجمالي الإشعارات: ${stats.totalShown}`
                    ] : ['مدير الإشعارات غير محمل']
                },
                {
                    title: '📦 إشعارات المنتجات',
                    value: notificationManager && notificationManager.productsNotificationsEnabled ? 'مفعلة' : 'معطلة',
                    status: notificationManager && notificationManager.productsNotificationsEnabled ? 'active' : 'disabled',
                    details: [
                        `فترة الانتظار: ${notificationManager ? notificationManager.productsNotificationCooldown/1000 : 0} ثانية`,
                        `التجميع: ${notificationManager ? notificationManager.groupingDelay/1000 : 0} ثانية`,
                        `الإشعارات المجمعة: ${notificationManager ? notificationManager.groupedNotifications.size : 0}`
                    ]
                },
                {
                    title: '⏸️ حالة الإيقاف المؤقت',
                    value: notificationManager && notificationManager.areNotificationsPaused() ? 'موقف مؤقتاً' : 'نشط',
                    status: notificationManager && notificationManager.areNotificationsPaused() ? 'paused' : 'active',
                    details: [
                        `الحد الأقصى للإشعارات: ${notificationManager ? notificationManager.maxNotifications : 0}`,
                        `مدة العرض: ${notificationManager ? notificationManager.notificationDuration/1000 : 0} ثانية`
                    ]
                },
                {
                    title: '🛡️ استقرار الواجهة',
                    value: window.uiStabilityManager ? 'مفعل' : 'غير مفعل',
                    status: window.uiStabilityManager ? 'active' : 'disabled',
                    details: window.uiStabilityManager ? [
                        `التحديثات المعلقة: ${window.uiStabilityManager.getStatus().pendingUpdates}`,
                        `طابور التحديثات: ${window.uiStabilityManager.getStatus().queueLength}`,
                        `مؤقتات التأخير: ${window.uiStabilityManager.getStatus().debounceTimers}`
                    ] : ['مدير استقرار الواجهة غير محمل']
                },
                {
                    title: '🎯 منسق المزامنة',
                    value: window.syncCoordinator ? (window.syncCoordinator.syncEnabled ? 'نشط' : 'معطل') : 'غير متاح',
                    status: window.syncCoordinator ? (window.syncCoordinator.syncEnabled ? 'active' : 'paused') : 'disabled',
                    details: window.syncCoordinator ? [
                        `المزامنات النشطة: ${window.syncCoordinator.getStatus().activeSyncs}`,
                        `طابور المزامنة: ${window.syncCoordinator.getStatus().queueLength}`,
                        `آخر مزامنة: ${window.syncCoordinator.getStatus().lastSyncTime ? new Date(window.syncCoordinator.getStatus().lastSyncTime).toLocaleTimeString('ar-SA') : 'لا توجد'}`,
                        `الإشعارات: ${window.syncCoordinator.getStatus().notificationsEnabled ? 'مفعلة' : 'معطلة'}`
                    ] : ['منسق المزامنة غير محمل']
                }
            ];
            
            statusCards.innerHTML = cards.map(card => `
                <div class="card">
                    <div class="card-title">
                        ${card.title}
                        <span class="status-indicator status-${card.status}"></span>
                    </div>
                    <div class="card-value">${card.value}</div>
                    ${card.details.map(detail => `<div class="card-detail">${detail}</div>`).join('')}
                </div>
            `).join('');
        }

        // Test functions
        function testNotification() {
            if (window.notificationManager) {
                window.notificationManager.showNotification('🧪 اختبار الإشعار - يعمل بشكل صحيح!', 'success');
                log('تم إرسال إشعار اختبار', 'success');
            } else {
                log('مدير الإشعارات غير متاح', 'error');
            }
        }

        function testProductsLoadNotification() {
            if (window.notificationManager) {
                window.notificationManager.showNotification('تم تحميل 5 منتج من السحابة', 'success');
                log('تم اختبار إشعار تحميل المنتجات', 'info');
            }
        }

        function testProductsUpdateNotification() {
            if (window.notificationManager) {
                window.notificationManager.showNotification('تم تحديث المنتجات من السحابة', 'info');
                log('تم اختبار إشعار تحديث المنتجات', 'info');
            }
        }

        function testMultipleNotifications() {
            if (window.notificationManager) {
                // Send multiple similar notifications to test grouping
                for (let i = 1; i <= 5; i++) {
                    setTimeout(() => {
                        window.notificationManager.showNotification(`تم تحميل ${i} منتج من السحابة`, 'success');
                    }, i * 500);
                }
                log('تم اختبار الإشعارات المتعددة (تجميع)', 'warning');
            }
        }

        function toggleProductsNotifications() {
            if (window.toggleProductsNotifications) {
                const enabled = window.toggleProductsNotifications();
                log(`تم ${enabled ? 'تفعيل' : 'تعطيل'} إشعارات المنتجات`, enabled ? 'success' : 'warning');
                refreshStatus();
            }
        }

        function clearAllNotifications() {
            if (window.clearAllNotifications) {
                window.clearAllNotifications();
                log('تم مسح جميع الإشعارات', 'info');
                refreshStatus();
            }
        }

        function checkUIStability() {
            if (window.uiStabilityManager) {
                const status = window.uiStabilityManager.getStatus();
                log(`🛡️ حالة استقرار الواجهة:`, 'info');
                log(`- التحديثات المعلقة: ${status.pendingUpdates}`, 'info');
                log(`- طابور التحديثات: ${status.queueLength}`, 'info');
                log(`- مؤقتات التأخير: ${status.debounceTimers}`, 'info');
                log(`- آخر تحديث: ${new Date(status.lastUpdateTime).toLocaleTimeString('ar-SA')}`, 'info');

                // Test UI stability
                window.uiStabilityManager.ensureUIStability();
                log('تم تشغيل فحص استقرار الواجهة', 'success');

                refreshStatus();
            } else {
                log('❌ مدير استقرار الواجهة غير متاح', 'error');
            }
        }

        function toggleSyncCoordinator() {
            if (window.syncCoordinator) {
                const currentStatus = window.syncCoordinator.syncEnabled;
                window.syncCoordinator.setSyncEnabled(!currentStatus);

                const newStatus = !currentStatus ? 'مفعل' : 'معطل';
                log(`🎯 تم ${newStatus} منسق المزامنة`, !currentStatus ? 'success' : 'warning');

                refreshStatus();
            } else {
                log('❌ منسق المزامنة غير متاح', 'error');
            }
        }

        function forceSyncNow() {
            if (window.syncCoordinator) {
                log('🚨 بدء المزامنة القسرية...', 'warning');

                window.syncCoordinator.forceSyncNow().then(result => {
                    if (result) {
                        log('✅ نجحت المزامنة القسرية', 'success');
                    } else {
                        log('❌ فشلت المزامنة القسرية', 'error');
                    }
                    refreshStatus();
                }).catch(error => {
                    log(`❌ خطأ في المزامنة القسرية: ${error.message}`, 'error');
                });
            } else if (window.forceSyncNow) {
                log('🔄 استخدام المزامنة القسرية البديلة...', 'info');
                window.forceSyncNow();
            } else {
                log('❌ المزامنة القسرية غير متاحة', 'error');
            }
        }

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            if (statusUpdateInterval) {
                clearInterval(statusUpdateInterval);
            }
        });
    </script>
</body>
</html>
