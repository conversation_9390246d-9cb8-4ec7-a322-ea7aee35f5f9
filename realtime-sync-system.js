// 🔄 نظام المزامنة اللحظية الشامل - Firebase Realtime Sync System
// يدير المزامنة اللحظية لجميع البيانات مع نظام الصلاحيات

class RealtimeSyncSystem {
    constructor() {
        this.isInitialized = false;
        this.syncEnabled = true;
        this.listeners = new Map();
        this.syncQueue = [];
        this.isOnline = navigator.onLine;
        this.currentUser = null;
        this.userPermissions = [];

        // Prevent sync loops
        this.isSyncing = false;
        this.lastSyncTime = new Map();
        this.syncCooldown = 2000; // 2 seconds cooldown between syncs
        this.notificationCooldown = 5000; // 5 seconds between notifications
        this.lastNotificationTime = new Map();

        // Data types to sync
        this.dataTypes = {
            PRODUCTS: 'products',
            CUSTOMERS: 'customers',
            USERS: 'users',
            SETTINGS: 'settings',
            ORDERS: 'orders',
            REPORTS: 'reports',
            NOTIFICATIONS: 'notifications'
        };

        // Permission levels
        this.permissions = {
            READ: 'read',
            WRITE: 'write',
            DELETE: 'delete',
            ADMIN: 'admin'
        };

        this.init();
    }

    // Initialize the sync system
    async init() {
        console.log('🚀 تهيئة نظام المزامنة اللحظية...');
        
        try {
            // Wait for Firebase to be ready
            await this.waitForFirebase();
            
            // Load current user and permissions
            await this.loadCurrentUser();
            
            // Setup real-time listeners
            await this.setupRealtimeListeners();
            
            // Setup offline/online detection
            this.setupNetworkDetection();
            
            // Setup periodic sync
            this.setupPeriodicSync();
            
            this.isInitialized = true;
            console.log('✅ تم تهيئة نظام المزامنة اللحظية بنجاح');
            
            // Add to notifications hub only (no popup)
            console.log('🔄 نظام المزامنة اللحظية نشط');

            if (typeof addNotificationToHistory === 'function') {
                addNotificationToHistory('🔄 نظام المزامنة اللحظية نشط', 'success');
            }
            
        } catch (error) {
            console.error('❌ فشل في تهيئة نظام المزامنة:', error);
            this.showNotification('❌ فشل في تهيئة نظام المزامنة', 'error');
        }
    }

    // Wait for Firebase to be ready
    async waitForFirebase() {
        return new Promise((resolve, reject) => {
            let attempts = 0;
            const maxAttempts = 30;
            
            const checkFirebase = () => {
                attempts++;
                
                if (window.firebaseService && window.firebaseService.isFirebaseReady()) {
                    console.log('✅ Firebase جاهز للمزامنة اللحظية');
                    resolve();
                } else if (attempts >= maxAttempts) {
                    reject(new Error('Firebase not ready after maximum attempts'));
                } else {
                    setTimeout(checkFirebase, 1000);
                }
            };
            
            checkFirebase();
        });
    }

    // Load current user and permissions
    async loadCurrentUser() {
        try {
            const currentUserData = localStorage.getItem('currentUser');
            if (currentUserData) {
                this.currentUser = JSON.parse(currentUserData);
                this.userPermissions = this.currentUser.permissions || [];
                
                console.log('👤 المستخدم الحالي:', this.currentUser.name);
                console.log('🔐 الصلاحيات:', this.userPermissions);
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل بيانات المستخدم:', error);
        }
    }

    // Check if user has permission for specific action
    hasPermission(dataType, action) {
        if (!this.currentUser) return false;
        
        // Admin has all permissions
        if (this.currentUser.role === 'admin') return true;
        
        // Check specific permissions
        const permissionKey = `${dataType}_${action}`;
        return this.userPermissions.includes(permissionKey) || 
               this.userPermissions.includes(`${dataType}_all`);
    }

    // Setup real-time listeners - notifications go to hub only
    async setupRealtimeListeners() {
        console.log('🔗 إعداد المستمعين للمزامنة اللحظية (إشعارات في المركز فقط)...');

        for (const [key, dataType] of Object.entries(this.dataTypes)) {
            if (this.hasPermission(dataType, this.permissions.READ)) {
                await this.setupDataListener(dataType);
            }
        }
    }

    // Setup listener for specific data type
    async setupDataListener(dataType) {
        try {
            console.log(`🔗 إعداد مستمع للبيانات: ${dataType}`);
            
            const collection = window.firebaseService.db.collection(dataType);
            
            // Listen for real-time changes
            const unsubscribe = collection.onSnapshot((snapshot) => {
                snapshot.docChanges().forEach((change) => {
                    this.handleDataChange(dataType, change);
                });
            }, (error) => {
                console.error(`❌ خطأ في مستمع ${dataType}:`, error);
            });
            
            // Store unsubscribe function
            this.listeners.set(dataType, unsubscribe);
            
            console.log(`✅ تم إعداد مستمع ${dataType}`);
            
        } catch (error) {
            console.error(`❌ فشل في إعداد مستمع ${dataType}:`, error);
        }
    }

    // Handle real-time data changes
    handleDataChange(dataType, change) {
        const { type, doc } = change;
        const data = doc.data();

        // Prevent sync loops
        if (this.isSyncing) {
            console.log(`⏸️ تجاهل التغيير في ${dataType} - المزامنة قيد التشغيل`);
            return;
        }

        // Check cooldown period
        const now = Date.now();
        const lastSync = this.lastSyncTime.get(dataType) || 0;
        if (now - lastSync < this.syncCooldown) {
            console.log(`⏸️ تجاهل التغيير في ${dataType} - فترة انتظار`);
            return;
        }

        console.log(`🔄 تغيير في ${dataType}: ${type}`, doc.id);

        // Set syncing flag
        this.isSyncing = true;
        this.lastSyncTime.set(dataType, now);

        try {
            switch (type) {
                case 'added':
                    this.handleDataAdded(dataType, doc.id, data);
                    break;
                case 'modified':
                    this.handleDataModified(dataType, doc.id, data);
                    break;
                case 'removed':
                    this.handleDataRemoved(dataType, doc.id);
                    break;
            }

            // Update local storage
            this.updateLocalStorage(dataType, data);

            // Refresh UI
            this.refreshUI(dataType);

            // Show notification (with cooldown)
            this.showChangeNotificationWithCooldown(dataType, type);

        } finally {
            // Clear syncing flag after a delay
            setTimeout(() => {
                this.isSyncing = false;
            }, 1000);
        }
    }

    // Handle data added
    handleDataAdded(dataType, docId, data) {
        console.log(`➕ تم إضافة بيانات جديدة في ${dataType}`);
        
        // Update local data based on type
        switch (dataType) {
            case this.dataTypes.PRODUCTS:
                this.updateLocalProducts(data.products || []);
                break;
            case this.dataTypes.CUSTOMERS:
                this.updateLocalCustomers(data.customers || []);
                break;
            case this.dataTypes.USERS:
                this.updateLocalUsers(data.users || []);
                break;
            case this.dataTypes.SETTINGS:
                this.updateLocalSettings(data.settings || {});
                break;
        }
    }

    // Handle data modified
    handleDataModified(dataType, docId, data) {
        console.log(`✏️ تم تعديل بيانات في ${dataType}`);
        this.handleDataAdded(dataType, docId, data); // Same logic as added
    }

    // Handle data removed
    handleDataRemoved(dataType, docId) {
        console.log(`🗑️ تم حذف بيانات من ${dataType}`);
        // Handle removal logic here
    }

    // Update local storage with new data
    updateLocalStorage(dataType, data) {
        try {
            switch (dataType) {
                case this.dataTypes.PRODUCTS:
                    if (data.products) {
                        localStorage.setItem('inventory_products', JSON.stringify(data.products));
                    }
                    break;
                case this.dataTypes.CUSTOMERS:
                    if (data.customers) {
                        localStorage.setItem('inventory_customers', JSON.stringify(data.customers));
                    }
                    break;
                case this.dataTypes.USERS:
                    if (data.users) {
                        localStorage.setItem('systemUsers', JSON.stringify(data.users));
                    }
                    break;
                case this.dataTypes.SETTINGS:
                    if (data.settings) {
                        localStorage.setItem('systemSettings', JSON.stringify(data.settings));
                    }
                    break;
            }
        } catch (error) {
            console.error(`❌ خطأ في تحديث التخزين المحلي لـ ${dataType}:`, error);
        }
    }

    // Update local products array
    updateLocalProducts(products) {
        if (typeof window.products !== 'undefined' && Array.isArray(products)) {
            window.products.length = 0;
            window.products.push(...products);
            console.log(`✅ تم تحديث المنتجات المحلية: ${products.length} منتج`);

            // Show notification about products update
            this.showChangeNotificationWithCooldown('products', 'modified');
        }
    }

    // Update local customers array
    updateLocalCustomers(customers) {
        if (typeof window.customers !== 'undefined' && Array.isArray(customers)) {
            window.customers.length = 0;
            window.customers.push(...customers);
            console.log(`✅ تم تحديث العملاء المحليين: ${customers.length} عميل`);
        }
    }

    // Update local users array
    updateLocalUsers(users) {
        if (typeof window.systemUsers !== 'undefined' && Array.isArray(users)) {
            window.systemUsers.length = 0;
            window.systemUsers.push(...users);
            console.log(`✅ تم تحديث المستخدمين المحليين: ${users.length} مستخدم`);
        }
    }

    // Update local settings
    updateLocalSettings(settings) {
        if (typeof window.settings !== 'undefined' && typeof settings === 'object') {
            Object.assign(window.settings, settings);
            console.log('✅ تم تحديث الإعدادات المحلية');
        }
    }

    // Refresh UI based on data type
    refreshUI(dataType) {
        try {
            switch (dataType) {
                case this.dataTypes.PRODUCTS:
                    if (typeof loadProductsTable === 'function') {
                        loadProductsTable();
                    }
                    if (typeof updateDashboardStats === 'function') {
                        updateDashboardStats();
                    }
                    break;
                case this.dataTypes.CUSTOMERS:
                    if (typeof loadCustomersTable === 'function') {
                        loadCustomersTable();
                    }
                    break;
                case this.dataTypes.USERS:
                    if (typeof updateUsersTable === 'function') {
                        updateUsersTable();
                    }
                    break;
                case this.dataTypes.SETTINGS:
                    if (typeof updateSettingsUI === 'function') {
                        updateSettingsUI();
                    }
                    if (typeof updateLogoInApp === 'function') {
                        updateLogoInApp();
                    }
                    break;
            }
        } catch (error) {
            console.error(`❌ خطأ في تحديث الواجهة لـ ${dataType}:`, error);
        }
    }

    // Show change notification with cooldown
    showChangeNotificationWithCooldown(dataType, changeType) {
        const now = Date.now();
        const notificationKey = `${dataType}_${changeType}`;
        const lastNotification = this.lastNotificationTime.get(notificationKey) || 0;

        // Check if enough time has passed since last notification
        if (now - lastNotification < this.notificationCooldown) {
            console.log(`⏸️ تجاهل الإشعار لـ ${dataType} - فترة انتظار الإشعارات`);
            return;
        }

        this.lastNotificationTime.set(notificationKey, now);

        const messages = {
            added: 'تم إضافة بيانات جديدة',
            modified: 'تم تحديث البيانات',
            removed: 'تم حذف بيانات'
        };

        const dataNames = {
            products: 'المنتجات',
            customers: 'العملاء',
            users: 'المستخدمين',
            settings: 'الإعدادات'
        };

        const message = `🔄 ${messages[changeType]} في ${dataNames[dataType] || dataType}`;
        console.log(message);

        // Add to notifications hub only (no popup)
        if (typeof addNotificationToHistory === 'function') {
            addNotificationToHistory(message, 'info');
        }
    }

    // Show change notification (legacy - kept for compatibility)
    showChangeNotification(dataType, changeType) {
        this.showChangeNotificationWithCooldown(dataType, changeType);
    }

    // Setup network detection
    setupNetworkDetection() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            console.log('🌐 الاتصال متاح - استئناف المزامنة');
            this.showNotification('🌐 تم استئناف المزامنة', 'success');
            this.processSyncQueue();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            console.log('📴 الاتصال منقطع - إيقاف المزامنة مؤقتاً');
            this.showNotification('📴 المزامنة متوقفة - لا يوجد اتصال', 'warning');
        });
    }

    // Setup periodic sync - DISABLED (handled by sync coordinator)
    setupPeriodicSync() {
        console.log('⚠️ المزامنة الدورية معطلة - يتم التحكم بواسطة منسق المزامنة المركزي');

        // Check if sync coordinator is available
        if (window.syncCoordinator) {
            console.log('✅ منسق المزامنة المركزي نشط - لا حاجة للمزامنة الدورية');
            return;
        }

        // Note: Periodic sync is now handled by the central sync coordinator
    }

    // Perform periodic sync
    async performPeriodicSync() {
        console.log('🔄 مزامنة دورية...');
        
        try {
            // Sync all data types the user has access to
            for (const [key, dataType] of Object.entries(this.dataTypes)) {
                if (this.hasPermission(dataType, this.permissions.READ)) {
                    await this.syncDataType(dataType);
                }
            }
        } catch (error) {
            console.error('❌ خطأ في المزامنة الدورية:', error);
        }
    }

    // Sync specific data type
    async syncDataType(dataType) {
        try {
            switch (dataType) {
                case this.dataTypes.PRODUCTS:
                    await this.syncProducts();
                    break;
                case this.dataTypes.CUSTOMERS:
                    await this.syncCustomers();
                    break;
                case this.dataTypes.USERS:
                    await this.syncUsers();
                    break;
                case this.dataTypes.SETTINGS:
                    await this.syncSettings();
                    break;
            }
        } catch (error) {
            console.error(`❌ خطأ في مزامنة ${dataType}:`, error);
        }
    }

    // Sync products specifically
    async syncProducts() {
        console.log('📦 مزامنة المنتجات...');

        try {
            if (window.syncProductsToFirebase && typeof window.syncProductsToFirebase === 'function') {
                const result = await window.syncProductsToFirebase();
                if (result) {
                    console.log('✅ تم رفع المنتجات إلى Firebase');
                    return true;
                } else {
                    console.log('⚠️ فشل في رفع المنتجات إلى Firebase');
                    return false;
                }
            } else {
                console.log('⚠️ وظيفة مزامنة المنتجات غير متاحة');
                return false;
            }
        } catch (error) {
            console.error('❌ خطأ في مزامنة المنتجات:', error);
            return false;
        }
    }

    // Sync customers specifically
    async syncCustomers() {
        console.log('👥 مزامنة العملاء...');

        try {
            if (window.syncCustomersToFirebase && typeof window.syncCustomersToFirebase === 'function') {
                const result = await window.syncCustomersToFirebase();
                if (result) {
                    console.log('✅ تم رفع العملاء إلى Firebase');
                    return true;
                } else {
                    console.log('⚠️ فشل في رفع العملاء إلى Firebase');
                    return false;
                }
            } else {
                console.log('⚠️ وظيفة مزامنة العملاء غير متاحة');
                return false;
            }
        } catch (error) {
            console.error('❌ خطأ في مزامنة العملاء:', error);
            return false;
        }
    }

    // Sync users specifically
    async syncUsers() {
        console.log('🔐 مزامنة المستخدمين...');

        try {
            if (window.syncUsersToFirebase && typeof window.syncUsersToFirebase === 'function') {
                const result = await window.syncUsersToFirebase();
                if (result) {
                    console.log('✅ تم رفع المستخدمين إلى Firebase');
                    return true;
                } else {
                    console.log('⚠️ فشل في رفع المستخدمين إلى Firebase');
                    return false;
                }
            } else {
                console.log('⚠️ وظيفة مزامنة المستخدمين غير متاحة');
                return false;
            }
        } catch (error) {
            console.error('❌ خطأ في مزامنة المستخدمين:', error);
            return false;
        }
    }

    // Sync settings specifically
    async syncSettings() {
        console.log('⚙️ مزامنة الإعدادات...');

        try {
            if (window.syncSettingsToFirebase && typeof window.syncSettingsToFirebase === 'function') {
                const result = await window.syncSettingsToFirebase();
                if (result) {
                    console.log('✅ تم رفع الإعدادات إلى Firebase');
                    return true;
                } else {
                    console.log('⚠️ فشل في رفع الإعدادات إلى Firebase');
                    return false;
                }
            } else {
                console.log('⚠️ وظيفة مزامنة الإعدادات غير متاحة');
                return false;
            }
        } catch (error) {
            console.error('❌ خطأ في مزامنة الإعدادات:', error);
            return false;
        }
    }

    // Show notification
    showNotification(message, type = 'info', duration = 5000) {
        if (typeof utils !== 'undefined' && typeof utils.showNotification === 'function') {
            utils.showNotification(message, type);
        } else {
            console.log(`📢 ${message}`);
        }
    }

    // Add to sync queue for offline operations
    addToSyncQueue(operation) {
        this.syncQueue.push({
            ...operation,
            timestamp: Date.now()
        });
    }

    // Process sync queue when back online
    async processSyncQueue() {
        console.log(`🔄 معالجة ${this.syncQueue.length} عملية مؤجلة...`);
        
        while (this.syncQueue.length > 0) {
            const operation = this.syncQueue.shift();
            try {
                await this.executeOperation(operation);
            } catch (error) {
                console.error('❌ فشل في تنفيذ عملية مؤجلة:', error);
            }
        }
    }

    // Execute queued operation
    async executeOperation(operation) {
        // Implementation for executing queued operations
        console.log('⚡ تنفيذ عملية مؤجلة:', operation.type);
    }
}

// Initialize the realtime sync system
window.realtimeSyncSystem = new RealtimeSyncSystem();

// Export for use in other files
window.RealtimeSyncSystem = RealtimeSyncSystem;
