<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تشخيص مشكلة المزامنة</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            margin: 20px;
            background: #f0f2f5;
            color: #333;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 25px;
            margin: 10px 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover { background: #45a049; }
        .btn.danger { background: #f44336; }
        .btn.danger:hover { background: #da190b; }
        .btn.warning { background: #ff9800; }
        .btn.warning:hover { background: #e68900; }
        
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        .data-table th {
            background: #f2f2f2;
            font-weight: bold;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        #log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h1>🔍 تشخيص مشكلة المزامنة</h1>
        
        <div class="section">
            <h3>🔧 أدوات التشخيص</h3>
            <button class="btn" onclick="fullDiagnosis()">🔍 تشخيص شامل</button>
            <button class="btn" onclick="compareData()">📊 مقارنة البيانات</button>
            <button class="btn" onclick="checkLocalStorage()">💾 فحص التخزين المحلي</button>
            <button class="btn" onclick="checkFirebaseData()">🔥 فحص Firebase</button>
            <button class="btn warning" onclick="forceSync()">🔄 مزامنة إجبارية</button>
            <button class="btn danger" onclick="resetLocalData()">🗑️ مسح البيانات المحلية</button>
        </div>

        <div class="section">
            <h3>📊 مقارنة البيانات</h3>
            <div id="data-comparison"></div>
        </div>

        <div class="section">
            <h3>🔥 حالة Firebase</h3>
            <div id="firebase-status"></div>
        </div>

        <div class="section">
            <h3>💾 البيانات المحلية</h3>
            <div id="local-data"></div>
        </div>

        <div class="section">
            <h3>📋 سجل التشخيص</h3>
            <div id="log"></div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore-compat.js"></script>
    
    <!-- Firebase Configuration -->
    <script src="firebase-config.js"></script>

    <script>
        const logElement = document.getElementById('log');
        
        function addToLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logMessage = `[${timestamp}] ${message}\n`;
            logElement.textContent += logMessage;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        // Full diagnosis
        async function fullDiagnosis() {
            addToLog('🔍 بدء التشخيص الشامل...');
            
            // Check Firebase connection
            await checkFirebaseConnection();
            
            // Check local data
            checkLocalStorageData();
            
            // Check Firebase data
            await checkFirebaseDataContent();
            
            // Compare data
            await compareLocalAndFirebase();
            
            addToLog('✅ انتهى التشخيص الشامل');
        }

        // Check Firebase connection
        async function checkFirebaseConnection() {
            addToLog('🔥 فحص اتصال Firebase...');
            
            const statusDiv = document.getElementById('firebase-status');
            
            try {
                if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
                    throw new Error('Firebase service not ready');
                }
                
                // Test write
                const testData = { test: true, timestamp: Date.now() };
                await window.firebaseService.db.collection('test').doc('connection').set(testData);
                
                // Test read
                const doc = await window.firebaseService.db.collection('test').doc('connection').get();
                
                if (doc.exists) {
                    statusDiv.innerHTML = '<div class="status success">✅ Firebase متصل ويعمل بشكل صحيح</div>';
                    addToLog('✅ Firebase connection successful');
                    return true;
                } else {
                    throw new Error('Failed to read test document');
                }
                
            } catch (error) {
                statusDiv.innerHTML = `<div class="status error">❌ خطأ في Firebase: ${error.message}</div>`;
                addToLog('❌ Firebase connection failed: ' + error.message);
                return false;
            }
        }

        // Check local storage data
        function checkLocalStorageData() {
            addToLog('💾 فحص البيانات المحلية...');
            
            const localDiv = document.getElementById('local-data');
            
            try {
                const products = JSON.parse(localStorage.getItem('inventory_products') || '[]');
                const customers = JSON.parse(localStorage.getItem('inventory_customers') || '[]');
                const users = JSON.parse(localStorage.getItem('systemUsers') || '[]');
                const settings = JSON.parse(localStorage.getItem('systemSettings') || '{}');
                const credentials = JSON.parse(localStorage.getItem('loginCredentials') || '{}');
                
                const html = `
                    <table class="data-table">
                        <tr><th>نوع البيانات</th><th>العدد/الحجم</th><th>آخر تحديث</th></tr>
                        <tr><td>المنتجات</td><td>${products.length} عنصر</td><td>${getLastModified('inventory_products')}</td></tr>
                        <tr><td>العملاء</td><td>${customers.length} عنصر</td><td>${getLastModified('inventory_customers')}</td></tr>
                        <tr><td>المستخدمين</td><td>${users.length} عنصر</td><td>${getLastModified('systemUsers')}</td></tr>
                        <tr><td>الإعدادات</td><td>${Object.keys(settings).length} مفتاح</td><td>${getLastModified('systemSettings')}</td></tr>
                        <tr><td>بيانات الدخول</td><td>${Object.keys(credentials).length} مفتاح</td><td>${getLastModified('loginCredentials')}</td></tr>
                    </table>
                `;
                
                localDiv.innerHTML = html;
                addToLog(`💾 البيانات المحلية: ${products.length} منتج, ${customers.length} عميل, ${users.length} مستخدم`);
                
            } catch (error) {
                localDiv.innerHTML = `<div class="status error">❌ خطأ في قراءة البيانات المحلية: ${error.message}</div>`;
                addToLog('❌ Error reading local data: ' + error.message);
            }
        }

        // Check Firebase data
        async function checkFirebaseDataContent() {
            addToLog('🔥 فحص محتوى Firebase...');
            
            try {
                if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
                    addToLog('⚠️ Firebase not ready');
                    return;
                }
                
                const firebaseProducts = await window.firebaseService.loadProducts();
                const firebaseCustomers = await window.firebaseService.loadCustomers();
                const firebaseUsers = await window.firebaseService.loadUsers();
                const firebaseSettings = await window.firebaseService.loadSettings();
                const firebaseCredentials = await window.firebaseService.loadLoginCredentials();
                
                addToLog(`🔥 Firebase data: ${firebaseProducts ? firebaseProducts.length : 0} منتج, ${firebaseCustomers ? firebaseCustomers.length : 0} عميل, ${firebaseUsers ? firebaseUsers.length : 0} مستخدم`);
                
            } catch (error) {
                addToLog('❌ Error reading Firebase data: ' + error.message);
            }
        }

        // Compare local and Firebase data
        async function compareLocalAndFirebase() {
            addToLog('📊 مقارنة البيانات المحلية مع Firebase...');
            
            const comparisonDiv = document.getElementById('data-comparison');
            
            try {
                // Get local data
                const localProducts = JSON.parse(localStorage.getItem('inventory_products') || '[]');
                const localCustomers = JSON.parse(localStorage.getItem('inventory_customers') || '[]');
                const localUsers = JSON.parse(localStorage.getItem('systemUsers') || '[]');
                
                // Get Firebase data
                const firebaseProducts = await window.firebaseService.loadProducts() || [];
                const firebaseCustomers = await window.firebaseService.loadCustomers() || [];
                const firebaseUsers = await window.firebaseService.loadUsers() || [];
                
                const html = `
                    <table class="data-table">
                        <tr><th>نوع البيانات</th><th>محلي</th><th>Firebase</th><th>الحالة</th></tr>
                        <tr>
                            <td>المنتجات</td>
                            <td>${localProducts.length}</td>
                            <td>${firebaseProducts.length}</td>
                            <td>${localProducts.length === firebaseProducts.length ? '✅ متطابق' : '❌ مختلف'}</td>
                        </tr>
                        <tr>
                            <td>العملاء</td>
                            <td>${localCustomers.length}</td>
                            <td>${firebaseCustomers.length}</td>
                            <td>${localCustomers.length === firebaseCustomers.length ? '✅ متطابق' : '❌ مختلف'}</td>
                        </tr>
                        <tr>
                            <td>المستخدمين</td>
                            <td>${localUsers.length}</td>
                            <td>${firebaseUsers.length}</td>
                            <td>${localUsers.length === firebaseUsers.length ? '✅ متطابق' : '❌ مختلف'}</td>
                        </tr>
                    </table>
                `;
                
                comparisonDiv.innerHTML = html;
                
                // Log differences
                if (localProducts.length !== firebaseProducts.length) {
                    addToLog(`⚠️ اختلاف في المنتجات: محلي ${localProducts.length}, Firebase ${firebaseProducts.length}`);
                }
                if (localCustomers.length !== firebaseCustomers.length) {
                    addToLog(`⚠️ اختلاف في العملاء: محلي ${localCustomers.length}, Firebase ${firebaseCustomers.length}`);
                }
                if (localUsers.length !== firebaseUsers.length) {
                    addToLog(`⚠️ اختلاف في المستخدمين: محلي ${localUsers.length}, Firebase ${firebaseUsers.length}`);
                }
                
            } catch (error) {
                comparisonDiv.innerHTML = `<div class="status error">❌ خطأ في المقارنة: ${error.message}</div>`;
                addToLog('❌ Error comparing data: ' + error.message);
            }
        }

        // Helper function to get last modified time
        function getLastModified(key) {
            try {
                const data = localStorage.getItem(key);
                if (data) {
                    return 'موجود';
                } else {
                    return 'غير موجود';
                }
            } catch (error) {
                return 'خطأ';
            }
        }

        // Force sync
        async function forceSync() {
            addToLog('🔄 بدء المزامنة الإجبارية...');
            
            try {
                // First sync to Firebase
                addToLog('📤 رفع البيانات المحلية إلى Firebase...');
                await window.syncToFirebase();
                
                // Then sync from Firebase
                addToLog('📥 تحميل البيانات من Firebase...');
                await window.syncFromFirebase();
                
                addToLog('✅ انتهت المزامنة الإجبارية');
                
                // Refresh comparison
                await compareLocalAndFirebase();
                
            } catch (error) {
                addToLog('❌ خطأ في المزامنة الإجبارية: ' + error.message);
            }
        }

        // Reset local data
        function resetLocalData() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات المحلية؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                addToLog('🗑️ مسح البيانات المحلية...');
                
                localStorage.removeItem('inventory_products');
                localStorage.removeItem('inventory_customers');
                localStorage.removeItem('systemUsers');
                localStorage.removeItem('systemSettings');
                localStorage.removeItem('loginCredentials');
                
                addToLog('✅ تم مسح البيانات المحلية');
                
                // Refresh displays
                checkLocalStorageData();
                compareLocalAndFirebase();
            }
        }

        // Shortcut functions
        function compareData() { compareLocalAndFirebase(); }
        function checkLocalStorage() { checkLocalStorageData(); }
        function checkFirebaseData() { checkFirebaseDataContent(); }

        // Auto-run diagnosis on load
        window.addEventListener('load', function() {
            setTimeout(fullDiagnosis, 1000);
        });
    </script>
</body>
</html>
