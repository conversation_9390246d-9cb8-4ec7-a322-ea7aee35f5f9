<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 تشخيص عرض المنتجات</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 10px;
        }
        .btn {
            padding: 15px 25px;
            margin: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn.primary { background: #007bff; color: white; }
        .btn.success { background: #28a745; color: white; }
        .btn.warning { background: #ffc107; color: #212529; }
        .btn.danger { background: #dc3545; color: white; }
        .btn:hover { transform: translateY(-2px); }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .log {
            background: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }
        .card-title {
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
        }
        .card-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 تشخيص عرض المنتجات</h1>
            <p>حل مشكلة "تم تحميل منتج من السحابة" لكن لا يظهر</p>
        </div>

        <!-- Status Cards -->
        <div class="grid" id="statusCards">
            <!-- Will be populated by JavaScript -->
        </div>

        <!-- Actions -->
        <div style="text-align: center; margin: 30px 0;">
            <button class="btn primary" onclick="runDiagnosis()">
                🔍 تشخيص شامل
            </button>
            <button class="btn success" onclick="forceUIUpdate()">
                🔄 تحديث الواجهة قسرياً
            </button>
            <button class="btn warning" onclick="syncProducts()">
                ☁️ مزامنة المنتجات
            </button>
            <button class="btn danger" onclick="clearLog()">
                🗑️ مسح السجل
            </button>
        </div>

        <!-- Current Status -->
        <div id="currentStatus" class="status info">جاري التحميل...</div>

        <!-- Log -->
        <div class="log" id="log">
            <div>[INFO] مرحباً بك في أداة تشخيص عرض المنتجات</div>
        </div>
    </div>

    <!-- Load required scripts -->
    <script src="firebase-config.js"></script>
    <script src="simple-sync-fix.js"></script>

    <script>
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 بدء تشخيص عرض المنتجات...', 'info');
            setTimeout(() => {
                runDiagnosis();
            }, 2000);
        });

        // Run comprehensive diagnosis
        function runDiagnosis() {
            log('🔍 بدء التشخيص الشامل...', 'info');
            updateStatus('جاري التشخيص...', 'info');

            try {
                const diagnosis = {
                    windowProducts: checkWindowProducts(),
                    localStorage: checkLocalStorage(),
                    productsTable: checkProductsTable(),
                    functions: checkFunctions(),
                    firebase: checkFirebase()
                };

                log('📊 نتائج التشخيص:', 'info');
                console.table(diagnosis);

                updateStatusCards(diagnosis);
                
                // Determine overall status
                const hasProducts = diagnosis.windowProducts.count > 0 || diagnosis.localStorage.count > 0;
                const hasTable = diagnosis.productsTable.exists;
                const hasFunctions = diagnosis.functions.loadProductsTable;

                if (hasProducts && hasTable && hasFunctions) {
                    updateStatus('✅ جميع المكونات متاحة - المشكلة في التحديث', 'warning');
                    log('💡 الحل: جرب "تحديث الواجهة قسرياً"', 'warning');
                } else if (!hasProducts) {
                    updateStatus('❌ لا توجد منتجات - جرب المزامنة', 'error');
                    log('💡 الحل: جرب "مزامنة المنتجات"', 'warning');
                } else if (!hasTable) {
                    updateStatus('❌ جدول المنتجات غير موجود', 'error');
                    log('💡 تأكد من أنك في صفحة المنتجات', 'warning');
                } else if (!hasFunctions) {
                    updateStatus('❌ وظائف التحديث غير متاحة', 'error');
                    log('💡 تأكد من تحميل جميع ملفات JavaScript', 'warning');
                } else {
                    updateStatus('⚠️ مشكلة غير محددة', 'warning');
                }

            } catch (error) {
                log(`❌ خطأ في التشخيص: ${error.message}`, 'error');
                updateStatus('❌ خطأ في التشخيص', 'error');
            }
        }

        // Check window.products
        function checkWindowProducts() {
            const products = window.products;
            const result = {
                exists: typeof products !== 'undefined',
                isArray: Array.isArray(products),
                count: products ? products.length : 0,
                type: typeof products
            };

            log(`📊 window.products: ${result.exists ? 'موجود' : 'غير موجود'}`, result.exists ? 'success' : 'error');
            log(`📊 نوع البيانات: ${result.type}`, 'info');
            log(`📊 هل هو مصفوفة: ${result.isArray}`, result.isArray ? 'success' : 'error');
            log(`📊 عدد المنتجات: ${result.count}`, result.count > 0 ? 'success' : 'warning');

            if (products && products.length > 0) {
                log(`📦 أول منتج: ${JSON.stringify(products[0])}`, 'info');
            }

            return result;
        }

        // Check localStorage
        function checkLocalStorage() {
            const localProducts = localStorage.getItem('inventory_products');
            const result = {
                exists: !!localProducts,
                count: 0,
                valid: false
            };

            if (localProducts) {
                try {
                    const parsed = JSON.parse(localProducts);
                    result.count = Array.isArray(parsed) ? parsed.length : 0;
                    result.valid = Array.isArray(parsed);
                    
                    log(`💾 localStorage: ${result.count} منتج`, result.count > 0 ? 'success' : 'warning');
                    
                    if (parsed.length > 0) {
                        log(`💾 أول منتج في localStorage: ${JSON.stringify(parsed[0])}`, 'info');
                    }
                } catch (error) {
                    log(`❌ خطأ في قراءة localStorage: ${error.message}`, 'error');
                }
            } else {
                log('💾 localStorage فارغ', 'warning');
            }

            return result;
        }

        // Check products table
        function checkProductsTable() {
            const table = document.querySelector('#productsTable');
            const tbody = document.querySelector('#productsTable tbody');
            
            const result = {
                exists: !!table,
                hasBody: !!tbody,
                rowCount: tbody ? tbody.children.length : 0,
                isEmpty: tbody ? tbody.innerHTML.trim().length === 0 : true
            };

            log(`🗃️ جدول المنتجات: ${result.exists ? 'موجود' : 'غير موجود'}`, result.exists ? 'success' : 'error');
            log(`🗃️ tbody: ${result.hasBody ? 'موجود' : 'غير موجود'}`, result.hasBody ? 'success' : 'error');
            log(`🗃️ عدد الصفوف: ${result.rowCount}`, result.rowCount > 0 ? 'success' : 'warning');
            log(`🗃️ فارغ: ${result.isEmpty}`, result.isEmpty ? 'warning' : 'success');

            return result;
        }

        // Check functions
        function checkFunctions() {
            const functions = {
                loadProductsTable: typeof loadProductsTable === 'function',
                updateDashboardStats: typeof updateDashboardStats === 'function',
                quickSyncProducts: typeof quickSyncProducts === 'function',
                forceCompleteUIUpdate: typeof forceCompleteUIUpdate === 'function'
            };

            Object.keys(functions).forEach(func => {
                log(`🔧 ${func}: ${functions[func] ? 'متاح' : 'غير متاح'}`, functions[func] ? 'success' : 'error');
            });

            return functions;
        }

        // Check Firebase
        function checkFirebase() {
            const result = {
                sdk: typeof firebase !== 'undefined',
                service: typeof window.firebaseService !== 'undefined',
                simpleSync: typeof window.SIMPLE_SYNC !== 'undefined'
            };

            log(`🔥 Firebase SDK: ${result.sdk ? 'محمل' : 'غير محمل'}`, result.sdk ? 'success' : 'error');
            log(`🔥 Firebase Service: ${result.service ? 'متاح' : 'غير متاح'}`, result.service ? 'success' : 'error');
            log(`🔥 Simple Sync: ${result.simpleSync ? 'متاح' : 'غير متاح'}`, result.simpleSync ? 'success' : 'error');

            return result;
        }

        // Update status cards
        function updateStatusCards(diagnosis) {
            const cardsContainer = document.getElementById('statusCards');
            
            cardsContainer.innerHTML = `
                <div class="card">
                    <div class="card-title">المنتجات في الذاكرة</div>
                    <div class="card-value">${diagnosis.windowProducts.count}</div>
                </div>
                <div class="card">
                    <div class="card-title">المنتجات المحلية</div>
                    <div class="card-value">${diagnosis.localStorage.count}</div>
                </div>
                <div class="card">
                    <div class="card-title">صفوف الجدول</div>
                    <div class="card-value">${diagnosis.productsTable.rowCount}</div>
                </div>
                <div class="card">
                    <div class="card-title">الوظائف المتاحة</div>
                    <div class="card-value">${Object.values(diagnosis.functions).filter(Boolean).length}/4</div>
                </div>
            `;
        }

        // Force UI update
        function forceUIUpdate() {
            log('🔄 تحديث الواجهة قسرياً...', 'info');
            
            try {
                // Method 1: Use global debug function
                if (typeof window.debugProductsUI === 'function') {
                    log('🔧 استخدام debugProductsUI...', 'info');
                    window.debugProductsUI();
                }

                // Method 2: Use simple sync
                if (window.SIMPLE_SYNC && window.SIMPLE_SYNC.forceUpdateUI) {
                    log('🔧 استخدام SIMPLE_SYNC.forceUpdateUI...', 'info');
                    window.SIMPLE_SYNC.forceUpdateUI();
                }

                // Method 3: Use global function
                if (typeof forceCompleteUIUpdate === 'function') {
                    log('🔧 استخدام forceCompleteUIUpdate...', 'info');
                    forceCompleteUIUpdate();
                }

                // Method 4: Manual update
                log('🔧 تحديث يدوي...', 'info');
                if (typeof loadProductsTable === 'function') {
                    loadProductsTable();
                }

                log('✅ تم تحديث الواجهة', 'success');
                
                // Re-run diagnosis after update
                setTimeout(() => {
                    runDiagnosis();
                }, 2000);

            } catch (error) {
                log(`❌ خطأ في تحديث الواجهة: ${error.message}`, 'error');
            }
        }

        // Sync products
        function syncProducts() {
            log('☁️ بدء مزامنة المنتجات...', 'info');
            
            try {
                if (typeof quickSyncProducts === 'function') {
                    log('🔄 استخدام quickSyncProducts...', 'info');
                    quickSyncProducts();
                } else if (window.SIMPLE_SYNC && window.SIMPLE_SYNC.syncProductsNow) {
                    log('🔄 استخدام SIMPLE_SYNC.syncProductsNow...', 'info');
                    window.SIMPLE_SYNC.syncProductsNow();
                } else {
                    log('❌ وظائف المزامنة غير متاحة', 'error');
                }

                // Re-run diagnosis after sync
                setTimeout(() => {
                    runDiagnosis();
                }, 5000);

            } catch (error) {
                log(`❌ خطأ في المزامنة: ${error.message}`, 'error');
            }
        }

        // Update status
        function updateStatus(message, type) {
            const statusEl = document.getElementById('currentStatus');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        // Log function
        function log(message, type = 'info') {
            const logEl = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                info: '#00ff00',
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107'
            };
            
            const logEntry = document.createElement('div');
            logEntry.style.color = colors[type] || colors.info;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            
            logEl.appendChild(logEntry);
            logEl.scrollTop = logEl.scrollHeight;

            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // Clear log
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            log('تم مسح السجل', 'info');
        }

        // Global functions
        window.debugProductsDisplay = runDiagnosis;
        window.forceUIUpdateNow = forceUIUpdate;
    </script>
</body>
</html>
