<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>👥 اختبار إدارة المستخدمين - النسور الماسية</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-section h3 {
            color: #555;
            margin-top: 0;
        }
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 13px;
            transition: transform 0.2s;
        }
        button:hover { transform: translateY(-2px); }
        button.success { background: linear-gradient(45deg, #4caf50, #45a049); }
        button.warning { background: linear-gradient(45deg, #ff9800, #f57c00); }
        button.danger { background: linear-gradient(45deg, #f44336, #d32f2f); }
        
        .user-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .user-info h4 {
            margin: 0 0 5px 0;
            color: #333;
        }
        .user-info p {
            margin: 0;
            color: #666;
            font-size: 12px;
        }
        .role-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: bold;
        }
        .role-admin { background: #ffebee; color: #c62828; }
        .role-manager { background: #e3f2fd; color: #1976d2; }
        .role-employee { background: #e8f5e8; color: #2e7d32; }
        .role-viewer { background: #fff3e0; color: #f57c00; }
        
        #log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 3px 0;
            padding: 3px;
            border-radius: 3px;
        }
        .log-success { background: #d4edda; color: #155724; }
        .log-error { background: #f8d7da; color: #721c24; }
        .log-info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>👥 اختبار نظام إدارة المستخدمين</h1>
        
        <div class="test-section">
            <h3>🔐 اختبار النظام الأساسي</h3>
            <button onclick="testUserManager()">🧪 اختبار UserManager</button>
            <button onclick="testPermissions()">🔑 اختبار الصلاحيات</button>
            <button onclick="testRoles()">👤 اختبار الأدوار</button>
            <button onclick="clearLog()">🗑️ مسح السجل</button>
        </div>

        <div class="test-section">
            <h3>👥 إدارة المستخدمين</h3>
            <button class="success" onclick="addTestUser('admin')">➕ إضافة مدير</button>
            <button class="success" onclick="addTestUser('manager')">➕ إضافة مدير قسم</button>
            <button class="success" onclick="addTestUser('employee')">➕ إضافة موظف</button>
            <button class="success" onclick="addTestUser('viewer')">➕ إضافة مشاهد</button>
            <button class="warning" onclick="listAllUsers()">📋 عرض جميع المستخدمين</button>
            <button class="danger" onclick="clearAllTestUsers()">🗑️ حذف المستخدمين التجريبيين</button>
        </div>

        <div class="test-section">
            <h3>🔐 اختبار تسجيل الدخول</h3>
            <button onclick="testLogin('admin')">🔑 دخول كمدير</button>
            <button onclick="testLogin('manager')">🔑 دخول كمدير قسم</button>
            <button onclick="testLogin('employee')">🔑 دخول كموظف</button>
            <button onclick="testLogin('viewer')">🔑 دخول كمشاهد</button>
            <button class="warning" onclick="testLogout()">🚪 تسجيل خروج</button>
        </div>

        <div class="test-section">
            <h3>🎛️ اختبار الصلاحيات</h3>
            <button onclick="testPermission('view_dashboard')">📊 عرض لوحة التحكم</button>
            <button onclick="testPermission('add_products')">📦 إضافة منتجات</button>
            <button onclick="testPermission('edit_customers')">👤 تعديل عملاء</button>
            <button onclick="testPermission('manage_users')">👥 إدارة مستخدمين</button>
            <button onclick="testPermission('delete_products')">🗑️ حذف منتجات</button>
        </div>

        <div id="usersDisplay" class="test-section" style="display: none;">
            <h3>👥 المستخدمون الحاليون</h3>
            <div id="usersList"></div>
        </div>

        <div id="log"></div>
    </div>

    <!-- Load User Management System -->
    <script src="user-management.js"></script>
    
    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.innerHTML = `[${new Date().toLocaleTimeString('ar-SA')}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function testUserManager() {
            log('🧪 اختبار UserManager...', 'info');
            
            if (typeof userManager !== 'undefined') {
                log('✅ UserManager محمل بنجاح', 'success');
                log(`📊 عدد المستخدمين: ${userManager.getAllUsers().length}`, 'info');
                
                const currentUser = userManager.getCurrentUser();
                if (currentUser) {
                    log(`👤 المستخدم الحالي: ${currentUser.name} (${currentUser.role})`, 'info');
                } else {
                    log('⚠️ لا يوجد مستخدم مسجل دخول', 'info');
                }
            } else {
                log('❌ UserManager غير محمل', 'error');
            }
        }

        function testPermissions() {
            log('🔑 اختبار نظام الصلاحيات...', 'info');
            
            if (typeof PERMISSIONS !== 'undefined') {
                log('✅ الصلاحيات محملة بنجاح', 'success');
                log(`📋 عدد الصلاحيات: ${Object.keys(PERMISSIONS).length}`, 'info');
                
                Object.entries(PERMISSIONS).forEach(([key, value]) => {
                    log(`🔐 ${key}: ${value}`, 'info');
                });
            } else {
                log('❌ نظام الصلاحيات غير محمل', 'error');
            }
        }

        function testRoles() {
            log('👤 اختبار الأدوار...', 'info');
            
            if (typeof USER_ROLES !== 'undefined' && typeof ROLE_PERMISSIONS !== 'undefined') {
                log('✅ الأدوار محملة بنجاح', 'success');
                
                Object.entries(ROLE_PERMISSIONS).forEach(([role, permissions]) => {
                    log(`👤 ${role}: ${permissions.length} صلاحية`, 'info');
                });
            } else {
                log('❌ نظام الأدوار غير محمل', 'error');
            }
        }

        function addTestUser(role) {
            const testUsers = {
                admin: { name: 'مدير تجريبي', email: '<EMAIL>', password: 'test123' },
                manager: { name: 'مدير قسم تجريبي', email: '<EMAIL>', password: 'test123' },
                employee: { name: 'موظف تجريبي', email: '<EMAIL>', password: 'test123' },
                viewer: { name: 'مشاهد تجريبي', email: '<EMAIL>', password: 'test123' }
            };

            const userData = { ...testUsers[role], role: role };
            const result = userManager.addUser(userData);

            if (result.success) {
                log(`✅ تم إضافة ${userData.name} بنجاح`, 'success');
            } else {
                log(`❌ فشل في إضافة ${userData.name}: ${result.error}`, 'error');
            }
        }

        function testLogin(role) {
            const testCredentials = {
                admin: { email: '<EMAIL>', password: 'test123' },
                manager: { email: '<EMAIL>', password: 'test123' },
                employee: { email: '<EMAIL>', password: 'test123' },
                viewer: { email: '<EMAIL>', password: 'test123' }
            };

            const credentials = testCredentials[role];
            if (!credentials) {
                log(`❌ بيانات تسجيل دخول غير متوفرة للدور: ${role}`, 'error');
                return;
            }

            const result = userManager.authenticateUser(credentials.email, credentials.password);
            if (result.success) {
                log(`✅ تم تسجيل الدخول كـ ${result.user.name} (${result.user.role})`, 'success');
            } else {
                log(`❌ فشل تسجيل الدخول: ${result.error}`, 'error');
            }
        }

        function testLogout() {
            const currentUser = userManager.getCurrentUser();
            if (currentUser) {
                userManager.logout();
                log(`✅ تم تسجيل خروج ${currentUser.name}`, 'success');
            } else {
                log('⚠️ لا يوجد مستخدم مسجل دخول', 'info');
            }
        }

        function testPermission(permission) {
            const hasPermission = userManager.hasPermission(permission);
            const currentUser = userManager.getCurrentUser();
            
            if (currentUser) {
                if (hasPermission) {
                    log(`✅ ${currentUser.name} لديه صلاحية: ${permission}`, 'success');
                } else {
                    log(`❌ ${currentUser.name} ليس لديه صلاحية: ${permission}`, 'error');
                }
            } else {
                log('⚠️ لا يوجد مستخدم مسجل دخول لاختبار الصلاحية', 'info');
            }
        }

        function listAllUsers() {
            const users = userManager.getAllUsers();
            const usersDisplay = document.getElementById('usersDisplay');
            const usersList = document.getElementById('usersList');
            
            if (users.length === 0) {
                log('📭 لا يوجد مستخدمون', 'info');
                usersDisplay.style.display = 'none';
                return;
            }

            log(`📋 عرض ${users.length} مستخدم`, 'info');
            
            usersList.innerHTML = users.map(user => `
                <div class="user-card">
                    <div class="user-info">
                        <h4>${user.name}</h4>
                        <p>${user.email}</p>
                        <p>آخر دخول: ${user.lastLogin ? new Date(user.lastLogin).toLocaleDateString('ar-SA') : 'لم يسجل دخول'}</p>
                    </div>
                    <div>
                        <span class="role-badge role-${user.role}">${user.role}</span>
                        <p style="margin: 5px 0 0 0; font-size: 11px;">${user.permissions.length} صلاحية</p>
                    </div>
                </div>
            `).join('');
            
            usersDisplay.style.display = 'block';
        }

        function clearAllTestUsers() {
            const users = userManager.getAllUsers();
            const testEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];
            
            let deletedCount = 0;
            users.forEach(user => {
                if (testEmails.includes(user.email)) {
                    const result = userManager.deleteUser(user.id);
                    if (result.success) {
                        deletedCount++;
                    }
                }
            });
            
            log(`🗑️ تم حذف ${deletedCount} مستخدم تجريبي`, 'success');
            document.getElementById('usersDisplay').style.display = 'none';
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            log('🗑️ تم مسح السجل', 'info');
        }

        // Initialize on load
        window.onload = function() {
            log('👥 مرحباً بك في اختبار نظام إدارة المستخدمين', 'info');
            log('🧪 اضغط "اختبار UserManager" للبدء', 'info');
        };
    </script>
</body>
</html>
