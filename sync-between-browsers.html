<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مزامنة البيانات بين المتصفحات</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 900px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px 5px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }
        .btn.danger { background: linear-gradient(45deg, #f44336, #d32f2f); }
        .btn.success { background: linear-gradient(45deg, #4CAF50, #388E3C); }
        .btn.warning { background: linear-gradient(45deg, #ff9800, #f57c00); }
        
        .section {
            margin: 20px 0;
            padding: 20px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(5px);
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: bold;
            text-align: center;
        }
        .success { background: rgba(76, 175, 80, 0.8); }
        .error { background: rgba(244, 67, 54, 0.8); }
        .warning { background: rgba(255, 152, 0, 0.8); }
        .info { background: rgba(33, 150, 243, 0.8); }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            border-radius: 8px;
            overflow: hidden;
        }
        .comparison-table th, .comparison-table td {
            padding: 12px;
            text-align: center;
            border-bottom: 1px solid #ddd;
        }
        .comparison-table th {
            background: rgba(102, 126, 234, 0.8);
            color: white;
            font-weight: bold;
        }
        .match { background: rgba(76, 175, 80, 0.2); }
        .mismatch { background: rgba(244, 67, 54, 0.2); }
        
        #log {
            background: rgba(0, 0, 0, 0.7);
            color: #00ff00;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            height: 300px;
            overflow-y: auto;
            margin-top: 20px;
            white-space: pre-wrap;
        }
        
        .browser-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h1>🔄 مزامنة البيانات بين المتصفحات</h1>
        
        <div class="section">
            <h3>🌐 معلومات المتصفح الحالي</h3>
            <div id="browser-info" class="browser-info">
                <span id="browser-name">جاري التحقق...</span>
                <span id="browser-data">جاري فحص البيانات...</span>
            </div>
        </div>

        <div class="section">
            <h3>🔧 أدوات المزامنة السريعة</h3>
            <button class="btn success" onclick="fullSync()">🔄 مزامنة شاملة</button>
            <button class="btn" onclick="uploadToFirebase()">📤 رفع البيانات المحلية</button>
            <button class="btn" onclick="downloadFromFirebase()">📥 تحميل من Firebase</button>
            <button class="btn warning" onclick="compareWithFirebase()">📊 مقارنة البيانات</button>
            <button class="btn danger" onclick="resetAndSync()">🔄 إعادة تعيين ومزامنة</button>
        </div>

        <div class="section">
            <h3>🖼️ مزامنة الشعار والإعدادات</h3>
            <button class="btn" onclick="syncLogoToFirebase()">📤 رفع الشعار إلى Firebase</button>
            <button class="btn" onclick="loadLogoFromFirebase()">📥 تحميل الشعار من Firebase</button>
            <button class="btn warning" onclick="checkLogoSync()">🔍 فحص مزامنة الشعار</button>
        </div>

        <div class="section">
            <h3>👥 مزامنة المستخدمين</h3>
            <button class="btn" onclick="syncUsersToFirebase()">📤 رفع المستخدمين إلى Firebase</button>
            <button class="btn" onclick="loadUsersFromFirebase()">📥 تحميل المستخدمين من Firebase</button>
            <button class="btn warning" onclick="checkUsersSync()">🔍 فحص مزامنة المستخدمين</button>
        </div>

        <div id="status" class="status info">جاري فحص حالة المزامنة...</div>

        <div class="section">
            <h3>📊 مقارنة البيانات</h3>
            <div id="comparison-results">
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>نوع البيانات</th>
                            <th>المحلي</th>
                            <th>Firebase</th>
                            <th>الحالة</th>
                        </tr>
                    </thead>
                    <tbody id="comparison-body">
                        <tr><td colspan="4">جاري التحميل...</td></tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="section">
            <h3>📋 سجل العمليات</h3>
            <div id="log"></div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore-compat.js"></script>
    
    <!-- Firebase Configuration -->
    <script src="firebase-config.js"></script>

    <script>
        const logElement = document.getElementById('log');
        const statusElement = document.getElementById('status');
        
        function addToLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logMessage = `[${timestamp}] ${message}\n`;
            logElement.textContent += logMessage;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        // Detect browser
        function detectBrowser() {
            const userAgent = navigator.userAgent;
            let browserName = 'Unknown';
            
            if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
                browserName = '🟢 Google Chrome';
            } else if (userAgent.includes('Firefox')) {
                browserName = '🟠 Mozilla Firefox';
            } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
                browserName = '🔵 Safari';
            } else if (userAgent.includes('Edg')) {
                browserName = '🔷 Microsoft Edge';
            }
            
            return browserName;
        }

        // Check current browser data
        function checkBrowserData() {
            const browserName = detectBrowser();
            document.getElementById('browser-name').textContent = browserName;
            
            try {
                const products = JSON.parse(localStorage.getItem('inventory_products') || '[]');
                const customers = JSON.parse(localStorage.getItem('inventory_customers') || '[]');
                const users = JSON.parse(localStorage.getItem('systemUsers') || '[]');
                
                const dataInfo = `📦 ${products.length} منتج | 👥 ${customers.length} عميل | 🔐 ${users.length} مستخدم`;
                document.getElementById('browser-data').textContent = dataInfo;
                
                addToLog(`🌐 المتصفح: ${browserName}`);
                addToLog(`📊 البيانات المحلية: ${products.length} منتج، ${customers.length} عميل، ${users.length} مستخدم`);
                
            } catch (error) {
                document.getElementById('browser-data').textContent = '❌ خطأ في قراءة البيانات';
                addToLog('❌ خطأ في قراءة البيانات المحلية: ' + error.message);
            }
        }

        // Compare with Firebase
        async function compareWithFirebase() {
            addToLog('📊 مقارنة البيانات مع Firebase...');
            statusElement.className = 'status info';
            statusElement.textContent = '📊 جاري مقارنة البيانات...';
            
            try {
                if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
                    throw new Error('Firebase not ready');
                }
                
                // Get local data
                const localProducts = JSON.parse(localStorage.getItem('inventory_products') || '[]');
                const localCustomers = JSON.parse(localStorage.getItem('inventory_customers') || '[]');
                const localUsers = JSON.parse(localStorage.getItem('systemUsers') || '[]');
                
                // Get Firebase data
                const firebaseProducts = await window.firebaseService.loadProducts() || [];
                const firebaseCustomers = await window.firebaseService.loadCustomers() || [];
                const firebaseUsers = await window.firebaseService.loadUsers() || [];
                
                // Update comparison table
                const tbody = document.getElementById('comparison-body');
                tbody.innerHTML = `
                    <tr class="${localProducts.length === firebaseProducts.length ? 'match' : 'mismatch'}">
                        <td>📦 المنتجات</td>
                        <td>${localProducts.length}</td>
                        <td>${firebaseProducts.length}</td>
                        <td>${localProducts.length === firebaseProducts.length ? '✅ متطابق' : '❌ مختلف'}</td>
                    </tr>
                    <tr class="${localCustomers.length === firebaseCustomers.length ? 'match' : 'mismatch'}">
                        <td>👥 العملاء</td>
                        <td>${localCustomers.length}</td>
                        <td>${firebaseCustomers.length}</td>
                        <td>${localCustomers.length === firebaseCustomers.length ? '✅ متطابق' : '❌ مختلف'}</td>
                    </tr>
                    <tr class="${localUsers.length === firebaseUsers.length ? 'match' : 'mismatch'}">
                        <td>🔐 المستخدمون</td>
                        <td>${localUsers.length}</td>
                        <td>${firebaseUsers.length}</td>
                        <td>${localUsers.length === firebaseUsers.length ? '✅ متطابق' : '❌ مختلف'}</td>
                    </tr>
                `;
                
                // Check if data matches
                const allMatch = localProducts.length === firebaseProducts.length && 
                               localCustomers.length === firebaseCustomers.length && 
                               localUsers.length === firebaseUsers.length;
                
                if (allMatch) {
                    statusElement.className = 'status success';
                    statusElement.textContent = '✅ جميع البيانات متطابقة مع Firebase';
                    addToLog('✅ جميع البيانات متطابقة مع Firebase');
                } else {
                    statusElement.className = 'status warning';
                    statusElement.textContent = '⚠️ البيانات غير متطابقة - يحتاج مزامنة';
                    addToLog('⚠️ البيانات غير متطابقة مع Firebase');
                    addToLog(`📊 المحلي: ${localProducts.length}/${localCustomers.length}/${localUsers.length}`);
                    addToLog(`🔥 Firebase: ${firebaseProducts.length}/${firebaseCustomers.length}/${firebaseUsers.length}`);
                }
                
            } catch (error) {
                statusElement.className = 'status error';
                statusElement.textContent = '❌ خطأ في المقارنة: ' + error.message;
                addToLog('❌ خطأ في مقارنة البيانات: ' + error.message);
            }
        }

        // Upload to Firebase
        async function uploadToFirebase() {
            addToLog('📤 رفع البيانات المحلية إلى Firebase...');
            statusElement.className = 'status info';
            statusElement.textContent = '📤 جاري رفع البيانات...';
            
            try {
                const result = await window.syncToFirebase();
                if (result) {
                    statusElement.className = 'status success';
                    statusElement.textContent = '✅ تم رفع البيانات إلى Firebase بنجاح';
                    addToLog('✅ تم رفع جميع البيانات إلى Firebase');
                    
                    // Update comparison
                    setTimeout(compareWithFirebase, 1000);
                } else {
                    statusElement.className = 'status error';
                    statusElement.textContent = '❌ فشل في رفع البيانات';
                    addToLog('❌ فشل في رفع البيانات إلى Firebase');
                }
            } catch (error) {
                statusElement.className = 'status error';
                statusElement.textContent = '❌ خطأ في الرفع: ' + error.message;
                addToLog('❌ خطأ في رفع البيانات: ' + error.message);
            }
        }

        // Download from Firebase
        async function downloadFromFirebase() {
            addToLog('📥 تحميل البيانات من Firebase...');
            statusElement.className = 'status info';
            statusElement.textContent = '📥 جاري تحميل البيانات...';
            
            try {
                const result = await window.syncFromFirebase();
                if (result) {
                    statusElement.className = 'status success';
                    statusElement.textContent = '✅ تم تحميل البيانات من Firebase بنجاح';
                    addToLog('✅ تم تحميل جميع البيانات من Firebase');
                    
                    // Force UI refresh
                    if (typeof window.forceUIRefresh === 'function') {
                        window.forceUIRefresh();
                        addToLog('✅ تم تحديث الواجهة');
                    }
                    
                    // Update browser data display
                    checkBrowserData();
                    
                    // Update comparison
                    setTimeout(compareWithFirebase, 1000);
                } else {
                    statusElement.className = 'status warning';
                    statusElement.textContent = '⚠️ لا توجد بيانات جديدة في Firebase';
                    addToLog('⚠️ لا توجد بيانات جديدة في Firebase');
                }
            } catch (error) {
                statusElement.className = 'status error';
                statusElement.textContent = '❌ خطأ في التحميل: ' + error.message;
                addToLog('❌ خطأ في تحميل البيانات: ' + error.message);
            }
        }

        // Full sync
        async function fullSync() {
            addToLog('🔄 بدء المزامنة الشاملة...');
            statusElement.className = 'status info';
            statusElement.textContent = '🔄 جاري المزامنة الشاملة...';
            
            try {
                // First upload local data
                addToLog('📤 الخطوة 1: رفع البيانات المحلية...');
                await uploadToFirebase();
                
                // Wait a moment
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // Then download to ensure we have the latest
                addToLog('📥 الخطوة 2: تحميل أحدث البيانات...');
                await downloadFromFirebase();
                
                statusElement.className = 'status success';
                statusElement.textContent = '✅ تمت المزامنة الشاملة بنجاح';
                addToLog('✅ تمت المزامنة الشاملة بنجاح');
                
            } catch (error) {
                statusElement.className = 'status error';
                statusElement.textContent = '❌ خطأ في المزامنة الشاملة: ' + error.message;
                addToLog('❌ خطأ في المزامنة الشاملة: ' + error.message);
            }
        }

        // Reset and sync
        async function resetAndSync() {
            if (!confirm('هل أنت متأكد من إعادة تعيين البيانات المحلية والمزامنة من Firebase؟')) {
                return;
            }
            
            addToLog('🔄 إعادة تعيين ومزامنة...');
            statusElement.className = 'status info';
            statusElement.textContent = '🔄 جاري إعادة التعيين والمزامنة...';
            
            try {
                // Clear local data
                localStorage.removeItem('inventory_products');
                localStorage.removeItem('inventory_customers');
                localStorage.removeItem('systemUsers');
                addToLog('🗑️ تم مسح البيانات المحلية');
                
                // Download from Firebase
                await downloadFromFirebase();
                
                statusElement.className = 'status success';
                statusElement.textContent = '✅ تم إعادة التعيين والمزامنة بنجاح';
                addToLog('✅ تم إعادة التعيين والمزامنة بنجاح');
                
            } catch (error) {
                statusElement.className = 'status error';
                statusElement.textContent = '❌ خطأ في إعادة التعيين: ' + error.message;
                addToLog('❌ خطأ في إعادة التعيين: ' + error.message);
            }
        }

        // Initialize on load
        window.addEventListener('load', function() {
            checkBrowserData();
            setTimeout(compareWithFirebase, 1000);
        });

        // Auto-refresh every 30 seconds
        setInterval(() => {
            checkBrowserData();
            compareWithFirebase();
        }, 30000);

        // Logo sync functions
        async function syncLogoToFirebase() {
            addToLog('🖼️ رفع الشعار إلى Firebase...');
            statusElement.className = 'status info';
            statusElement.textContent = '🖼️ جاري رفع الشعار...';

            try {
                if (typeof window.syncLogoToFirebase === 'function') {
                    const result = await window.syncLogoToFirebase();
                    if (result) {
                        statusElement.className = 'status success';
                        statusElement.textContent = '✅ تم رفع الشعار إلى Firebase بنجاح';
                        addToLog('✅ تم رفع الشعار إلى Firebase بنجاح');
                    } else {
                        statusElement.className = 'status warning';
                        statusElement.textContent = '⚠️ فشل في رفع الشعار أو لا يوجد شعار';
                        addToLog('⚠️ فشل في رفع الشعار أو لا يوجد شعار');
                    }
                } else {
                    statusElement.className = 'status error';
                    statusElement.textContent = '❌ وظيفة رفع الشعار غير متاحة';
                    addToLog('❌ وظيفة رفع الشعار غير متاحة');
                }
            } catch (error) {
                statusElement.className = 'status error';
                statusElement.textContent = '❌ خطأ في رفع الشعار: ' + error.message;
                addToLog('❌ خطأ في رفع الشعار: ' + error.message);
            }
        }

        async function loadLogoFromFirebase() {
            addToLog('🖼️ تحميل الشعار من Firebase...');
            statusElement.className = 'status info';
            statusElement.textContent = '🖼️ جاري تحميل الشعار...';

            try {
                if (typeof window.loadLogoFromFirebase === 'function') {
                    const result = await window.loadLogoFromFirebase();
                    if (result) {
                        statusElement.className = 'status success';
                        statusElement.textContent = '✅ تم تحميل الشعار من Firebase بنجاح';
                        addToLog('✅ تم تحميل الشعار من Firebase بنجاح');

                        // Update browser data display
                        checkBrowserData();
                    } else {
                        statusElement.className = 'status warning';
                        statusElement.textContent = '⚠️ لا يوجد شعار في Firebase';
                        addToLog('⚠️ لا يوجد شعار في Firebase');
                    }
                } else {
                    statusElement.className = 'status error';
                    statusElement.textContent = '❌ وظيفة تحميل الشعار غير متاحة';
                    addToLog('❌ وظيفة تحميل الشعار غير متاحة');
                }
            } catch (error) {
                statusElement.className = 'status error';
                statusElement.textContent = '❌ خطأ في تحميل الشعار: ' + error.message;
                addToLog('❌ خطأ في تحميل الشعار: ' + error.message);
            }
        }

        async function checkLogoSync() {
            addToLog('🔍 فحص مزامنة الشعار...');
            statusElement.className = 'status info';
            statusElement.textContent = '🔍 جاري فحص مزامنة الشعار...';

            try {
                // Check local logo
                const localSettings = JSON.parse(localStorage.getItem('systemSettings') || '{}');
                const hasLocalLogo = !!(localSettings.logo && localSettings.logo.data);

                addToLog(`📱 الشعار المحلي: ${hasLocalLogo ? 'موجود' : 'غير موجود'}`);

                if (hasLocalLogo) {
                    addToLog(`📏 حجم الشعار المحلي: ${localSettings.logo.data.length} حرف`);
                    addToLog(`🏷️ اسم الشعار المحلي: ${localSettings.logo.name || 'غير محدد'}`);
                }

                // Check Firebase logo
                if (window.firebaseService && window.firebaseService.isFirebaseReady()) {
                    const firebaseSettings = await window.firebaseService.loadSettings();
                    const hasFirebaseLogo = !!(firebaseSettings && firebaseSettings.logo && firebaseSettings.logo.data);

                    addToLog(`🔥 الشعار في Firebase: ${hasFirebaseLogo ? 'موجود' : 'غير موجود'}`);

                    if (hasFirebaseLogo) {
                        addToLog(`📏 حجم الشعار في Firebase: ${firebaseSettings.logo.data.length} حرف`);
                        addToLog(`🏷️ اسم الشعار في Firebase: ${firebaseSettings.logo.name || 'غير محدد'}`);
                    }

                    // Compare logos
                    if (hasLocalLogo && hasFirebaseLogo) {
                        const isIdentical = localSettings.logo.data === firebaseSettings.logo.data;
                        addToLog(`🔄 الشعارات متطابقة: ${isIdentical ? 'نعم ✅' : 'لا ❌'}`);

                        if (isIdentical) {
                            statusElement.className = 'status success';
                            statusElement.textContent = '✅ الشعارات متطابقة في المحلي و Firebase';
                        } else {
                            statusElement.className = 'status warning';
                            statusElement.textContent = '⚠️ الشعارات مختلفة - يحتاج مزامنة';
                        }
                    } else if (hasLocalLogo && !hasFirebaseLogo) {
                        statusElement.className = 'status warning';
                        statusElement.textContent = '⚠️ يوجد شعار محلي لكن لا يوجد في Firebase';
                        addToLog('💡 يمكنك رفع الشعار المحلي إلى Firebase');
                    } else if (!hasLocalLogo && hasFirebaseLogo) {
                        statusElement.className = 'status warning';
                        statusElement.textContent = '⚠️ يوجد شعار في Firebase لكن لا يوجد محلياً';
                        addToLog('💡 يمكنك تحميل الشعار من Firebase');
                    } else {
                        statusElement.className = 'status info';
                        statusElement.textContent = 'ℹ️ لا يوجد شعار في المحلي أو Firebase';
                    }
                } else {
                    statusElement.className = 'status error';
                    statusElement.textContent = '❌ Firebase غير متاح للفحص';
                    addToLog('❌ Firebase غير متاح للفحص');
                }

            } catch (error) {
                statusElement.className = 'status error';
                statusElement.textContent = '❌ خطأ في فحص مزامنة الشعار: ' + error.message;
                addToLog('❌ خطأ في فحص مزامنة الشعار: ' + error.message);
            }
        }

        // Users sync functions
        async function syncUsersToFirebase() {
            addToLog('👥 رفع المستخدمين إلى Firebase...');
            statusElement.className = 'status info';
            statusElement.textContent = '👥 جاري رفع المستخدمين...';

            try {
                if (typeof window.userManager !== 'undefined' && typeof window.userManager.syncUsersToFirebase === 'function') {
                    const result = await window.userManager.syncUsersToFirebase();
                    if (result) {
                        statusElement.className = 'status success';
                        statusElement.textContent = '✅ تم رفع المستخدمين إلى Firebase بنجاح';
                        addToLog('✅ تم رفع المستخدمين إلى Firebase بنجاح');
                    } else {
                        statusElement.className = 'status warning';
                        statusElement.textContent = '⚠️ فشل في رفع المستخدمين أو لا توجد مستخدمين';
                        addToLog('⚠️ فشل في رفع المستخدمين أو لا توجد مستخدمين');
                    }
                } else {
                    statusElement.className = 'status error';
                    statusElement.textContent = '❌ وظيفة رفع المستخدمين غير متاحة';
                    addToLog('❌ وظيفة رفع المستخدمين غير متاحة');
                }
            } catch (error) {
                statusElement.className = 'status error';
                statusElement.textContent = '❌ خطأ في رفع المستخدمين: ' + error.message;
                addToLog('❌ خطأ في رفع المستخدمين: ' + error.message);
            }
        }

        async function loadUsersFromFirebase() {
            addToLog('👥 تحميل المستخدمين من Firebase...');
            statusElement.className = 'status info';
            statusElement.textContent = '👥 جاري تحميل المستخدمين...';

            try {
                if (typeof window.userManager !== 'undefined' && typeof window.userManager.loadUsersFromFirebase === 'function') {
                    const result = await window.userManager.loadUsersFromFirebase();
                    if (result) {
                        statusElement.className = 'status success';
                        statusElement.textContent = '✅ تم تحميل المستخدمين من Firebase بنجاح';
                        addToLog('✅ تم تحميل المستخدمين من Firebase بنجاح');

                        // Update browser data display
                        checkBrowserData();
                    } else {
                        statusElement.className = 'status warning';
                        statusElement.textContent = '⚠️ لا توجد مستخدمين في Firebase';
                        addToLog('⚠️ لا توجد مستخدمين في Firebase');
                    }
                } else {
                    statusElement.className = 'status error';
                    statusElement.textContent = '❌ وظيفة تحميل المستخدمين غير متاحة';
                    addToLog('❌ وظيفة تحميل المستخدمين غير متاحة');
                }
            } catch (error) {
                statusElement.className = 'status error';
                statusElement.textContent = '❌ خطأ في تحميل المستخدمين: ' + error.message;
                addToLog('❌ خطأ في تحميل المستخدمين: ' + error.message);
            }
        }

        async function checkUsersSync() {
            addToLog('🔍 فحص مزامنة المستخدمين...');
            statusElement.className = 'status info';
            statusElement.textContent = '🔍 جاري فحص مزامنة المستخدمين...';

            try {
                // Check local users
                const localUsers = JSON.parse(localStorage.getItem('systemUsers') || '[]');
                const hasLocalUsers = localUsers.length > 0;

                addToLog(`📱 المستخدمون المحليون: ${hasLocalUsers ? localUsers.length + ' مستخدم' : 'لا يوجد'}`);

                if (hasLocalUsers) {
                    addToLog('👥 المستخدمون المحليون:');
                    localUsers.forEach((user, index) => {
                        addToLog(`  ${index + 1}. ${user.name} (${user.email}) - ${user.role} - نشط: ${user.isActive}`);
                    });
                }

                // Check Firebase users
                if (window.firebaseService && window.firebaseService.isFirebaseReady()) {
                    const firebaseUsers = await window.firebaseService.loadUsers();
                    const hasFirebaseUsers = firebaseUsers && firebaseUsers.length > 0;

                    addToLog(`🔥 المستخدمون في Firebase: ${hasFirebaseUsers ? firebaseUsers.length + ' مستخدم' : 'لا يوجد'}`);

                    if (hasFirebaseUsers) {
                        addToLog('👥 المستخدمون في Firebase:');
                        firebaseUsers.forEach((user, index) => {
                            addToLog(`  ${index + 1}. ${user.name} (${user.email}) - ${user.role} - نشط: ${user.isActive}`);
                        });
                    }

                    // Compare users
                    if (hasLocalUsers && hasFirebaseUsers) {
                        const isIdentical = localUsers.length === firebaseUsers.length &&
                            localUsers.every(localUser =>
                                firebaseUsers.some(firebaseUser =>
                                    firebaseUser.email === localUser.email &&
                                    firebaseUser.password === localUser.password
                                )
                            );

                        addToLog(`🔄 المستخدمون متطابقون: ${isIdentical ? 'نعم ✅' : 'لا ❌'}`);

                        if (isIdentical) {
                            statusElement.className = 'status success';
                            statusElement.textContent = '✅ المستخدمون متطابقون في المحلي و Firebase';
                        } else {
                            statusElement.className = 'status warning';
                            statusElement.textContent = '⚠️ المستخدمون مختلفون - يحتاج مزامنة';
                        }
                    } else if (hasLocalUsers && !hasFirebaseUsers) {
                        statusElement.className = 'status warning';
                        statusElement.textContent = '⚠️ يوجد مستخدمون محلياً لكن لا يوجد في Firebase';
                        addToLog('💡 يمكنك رفع المستخدمين المحليين إلى Firebase');
                    } else if (!hasLocalUsers && hasFirebaseUsers) {
                        statusElement.className = 'status warning';
                        statusElement.textContent = '⚠️ يوجد مستخدمون في Firebase لكن لا يوجد محلياً';
                        addToLog('💡 يمكنك تحميل المستخدمين من Firebase');
                    } else {
                        statusElement.className = 'status info';
                        statusElement.textContent = 'ℹ️ لا يوجد مستخدمون في المحلي أو Firebase';
                    }
                } else {
                    statusElement.className = 'status error';
                    statusElement.textContent = '❌ Firebase غير متاح للفحص';
                    addToLog('❌ Firebase غير متاح للفحص');
                }

            } catch (error) {
                statusElement.className = 'status error';
                statusElement.textContent = '❌ خطأ في فحص مزامنة المستخدمين: ' + error.message;
                addToLog('❌ خطأ في فحص مزامنة المستخدمين: ' + error.message);
            }
        }
    </script>
</body>
</html>
