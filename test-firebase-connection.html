<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار اتصال Firebase</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            margin: 20px;
            background: #f0f2f5;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 25px;
            margin: 10px 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background: #45a049;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        #log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h1>🔥 اختبار اتصال Firebase</h1>
        
        <div id="status" class="status info">جاري التحقق من الاتصال...</div>
        
        <div>
            <button class="btn" onclick="testConnection()">🧪 اختبار الاتصال</button>
            <button class="btn" onclick="testWrite()">✍️ اختبار الكتابة</button>
            <button class="btn" onclick="testRead()">📖 اختبار القراءة</button>
            <button class="btn" onclick="clearLog()">🗑️ مسح السجل</button>
        </div>
        
        <h3>📋 سجل الأحداث:</h3>
        <div id="log"></div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore-compat.js"></script>
    
    <!-- Firebase Configuration -->
    <script src="firebase-config.js"></script>

    <script>
        const logElement = document.getElementById('log');
        const statusElement = document.getElementById('status');
        
        // Override console.log to show in page
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function addToLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logMessage = `[${timestamp}] ${message}\n`;
            logElement.textContent += logMessage;
            logElement.scrollTop = logElement.scrollHeight;
            
            // Also log to browser console
            if (type === 'error') {
                originalError(message);
            } else if (type === 'warn') {
                originalWarn(message);
            } else {
                originalLog(message);
            }
        }
        
        console.log = (...args) => addToLog(args.join(' '), 'info');
        console.error = (...args) => addToLog('❌ ' + args.join(' '), 'error');
        console.warn = (...args) => addToLog('⚠️ ' + args.join(' '), 'warn');

        // Check Firebase status on load
        window.addEventListener('load', function() {
            setTimeout(checkInitialStatus, 1000);
        });

        function checkInitialStatus() {
            if (window.firebaseService && window.firebaseService.isFirebaseReady()) {
                statusElement.className = 'status success';
                statusElement.textContent = '✅ Firebase متصل وجاهز';
                addToLog('✅ Firebase initialized successfully');
            } else {
                statusElement.className = 'status error';
                statusElement.textContent = '❌ Firebase غير متصل';
                addToLog('❌ Firebase initialization failed');
            }
        }

        async function testConnection() {
            addToLog('🔍 بدء اختبار الاتصال...');
            
            try {
                if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
                    throw new Error('Firebase service not ready');
                }
                
                addToLog('✅ Firebase service is ready');
                addToLog('📊 Firebase status: ' + JSON.stringify(window.firebaseService.getStatus()));
                
                statusElement.className = 'status success';
                statusElement.textContent = '✅ الاتصال ناجح';
                
            } catch (error) {
                addToLog('❌ Connection test failed: ' + error.message);
                statusElement.className = 'status error';
                statusElement.textContent = '❌ فشل الاتصال: ' + error.message;
            }
        }

        async function testWrite() {
            addToLog('✍️ بدء اختبار الكتابة...');
            
            try {
                if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
                    throw new Error('Firebase service not ready');
                }
                
                const testData = {
                    test: true,
                    timestamp: Date.now(),
                    message: 'اختبار الكتابة من التطبيق'
                };
                
                addToLog('📝 Writing test data...');
                await window.firebaseService.db.collection('test').doc('write-test').set(testData);
                addToLog('✅ Write test successful');
                
                statusElement.className = 'status success';
                statusElement.textContent = '✅ اختبار الكتابة نجح';
                
            } catch (error) {
                addToLog('❌ Write test failed: ' + error.message);
                statusElement.className = 'status error';
                statusElement.textContent = '❌ فشل اختبار الكتابة: ' + error.message;
            }
        }

        async function testRead() {
            addToLog('📖 بدء اختبار القراءة...');
            
            try {
                if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
                    throw new Error('Firebase service not ready');
                }
                
                addToLog('📖 Reading test data...');
                const doc = await window.firebaseService.db.collection('test').doc('write-test').get();
                
                if (doc.exists) {
                    const data = doc.data();
                    addToLog('✅ Read test successful');
                    addToLog('📄 Data: ' + JSON.stringify(data, null, 2));
                } else {
                    addToLog('⚠️ No test document found');
                }
                
                statusElement.className = 'status success';
                statusElement.textContent = '✅ اختبار القراءة نجح';
                
            } catch (error) {
                addToLog('❌ Read test failed: ' + error.message);
                statusElement.className = 'status error';
                statusElement.textContent = '❌ فشل اختبار القراءة: ' + error.message;
            }
        }

        function clearLog() {
            logElement.textContent = '';
            addToLog('🗑️ تم مسح السجل');
        }
    </script>
</body>
</html>
