<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📦 تشخيص مزامنة المنتجات - النسور الماسية</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .diagnostic-container {
            max-width: 1200px;
            margin: 20px auto;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 15px;
        }

        .diagnostic-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 15px;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .status-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            text-align: center;
        }

        .status-card.success {
            border-left: 5px solid #28a745;
        }

        .status-card.warning {
            border-left: 5px solid #ffc107;
        }

        .status-card.error {
            border-left: 5px solid #dc3545;
        }

        .status-card.info {
            border-left: 5px solid #17a2b8;
        }

        .actions-section {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s ease;
        }

        .btn.primary {
            background: #007bff;
            color: white;
        }

        .btn.success {
            background: #28a745;
            color: white;
        }

        .btn.warning {
            background: #ffc107;
            color: #212529;
        }

        .btn.danger {
            background: #dc3545;
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .log-container {
            background: #1a1a1a;
            color: #00ff00;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-entry.success {
            color: #28a745;
        }

        .log-entry.error {
            color: #dc3545;
        }

        .log-entry.warning {
            color: #ffc107;
        }

        .log-entry.info {
            color: #17a2b8;
        }

        .products-comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .products-list {
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            max-height: 300px;
            overflow-y: auto;
        }

        .product-item {
            padding: 8px;
            border-bottom: 1px solid #eee;
            font-size: 12px;
        }

        .product-item:last-child {
            border-bottom: none;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="diagnostic-container">
        <!-- Header -->
        <div class="diagnostic-header">
            <h1><i class="fas fa-boxes"></i> تشخيص مزامنة المنتجات</h1>
            <p>فحص وإصلاح مشاكل مزامنة المنتجات بين المتصفحات</p>
        </div>

        <!-- Status Overview -->
        <div class="status-grid" id="statusGrid">
            <!-- Will be populated by JavaScript -->
        </div>

        <!-- Quick Actions -->
        <div class="actions-section">
            <h3><i class="fas fa-tools"></i> إجراءات سريعة</h3>
            <div style="text-align: center;">
                <button class="btn primary" onclick="runFullDiagnostic()">
                    <i class="fas fa-search"></i> فحص شامل
                </button>
                <button class="btn success" onclick="syncProductsToFirebase()">
                    <i class="fas fa-upload"></i> رفع إلى Firebase
                </button>
                <button class="btn warning" onclick="loadProductsFromFirebase()">
                    <i class="fas fa-download"></i> تحميل من Firebase
                </button>
                <button class="btn" onclick="bidirectionalSync()">
                    <i class="fas fa-sync-alt"></i> مزامنة ثنائية
                </button>
                <button class="btn danger" onclick="clearLog()">
                    <i class="fas fa-trash"></i> مسح السجل
                </button>
            </div>
        </div>

        <!-- Products Comparison -->
        <div class="products-comparison">
            <div class="products-list">
                <h4><i class="fas fa-laptop"></i> المنتجات المحلية</h4>
                <div id="localProductsList">جاري التحميل...</div>
            </div>
            <div class="products-list">
                <h4><i class="fas fa-cloud"></i> المنتجات في Firebase</h4>
                <div id="firebaseProductsList">جاري التحميل...</div>
            </div>
        </div>

        <!-- Diagnostic Log -->
        <div class="actions-section">
            <h3><i class="fas fa-file-alt"></i> سجل التشخيص</h3>
            <div class="log-container" id="diagnosticLog">
                <div class="log-entry info">[INFO] مرحباً بك في أداة تشخيص مزامنة المنتجات</div>
                <div class="log-entry info">[INFO] جاري تهيئة النظام...</div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>
    <script src="firebase-config.js"></script>
    <script src="products-sync.js"></script>
    
    <script>
        // Initialize diagnostic tool
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📦 تهيئة أداة تشخيص مزامنة المنتجات...');
            
            setTimeout(() => {
                initializeDiagnostic();
            }, 2000);
        });

        // Initialize diagnostic
        async function initializeDiagnostic() {
            addToLog('🚀 بدء تشخيص مزامنة المنتجات...', 'info');
            
            try {
                // Check Firebase status
                await checkFirebaseStatus();
                
                // Load and display products
                await loadLocalProducts();
                await loadFirebaseProducts();
                
                // Update status overview
                updateStatusOverview();
                
                addToLog('✅ تم تهيئة أداة التشخيص بنجاح', 'success');
                
            } catch (error) {
                addToLog(`❌ خطأ في تهيئة أداة التشخيص: ${error.message}`, 'error');
            }
        }

        // Check Firebase status
        async function checkFirebaseStatus() {
            addToLog('🔍 فحص حالة Firebase...', 'info');
            
            if (window.firebaseService && window.firebaseService.isFirebaseReady()) {
                addToLog('✅ Firebase متصل ويعمل', 'success');
                return true;
            } else {
                addToLog('❌ Firebase غير متاح أو غير متصل', 'error');
                return false;
            }
        }

        // Load local products
        async function loadLocalProducts() {
            addToLog('📱 تحميل المنتجات المحلية...', 'info');
            
            try {
                let localProducts = [];
                
                // Try global variable first
                if (typeof window.products !== 'undefined' && Array.isArray(window.products)) {
                    localProducts = window.products;
                } else {
                    // Try localStorage
                    const productsData = localStorage.getItem('inventory_products');
                    if (productsData) {
                        localProducts = JSON.parse(productsData);
                    }
                }
                
                addToLog(`📊 تم العثور على ${localProducts.length} منتج محلي`, 'info');
                displayProductsList(localProducts, 'localProductsList', 'محلي');
                
                return localProducts;
                
            } catch (error) {
                addToLog(`❌ خطأ في تحميل المنتجات المحلية: ${error.message}`, 'error');
                return [];
            }
        }

        // Load Firebase products
        async function loadFirebaseProducts() {
            addToLog('☁️ تحميل المنتجات من Firebase...', 'info');
            
            try {
                if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
                    addToLog('⚠️ Firebase غير متاح', 'warning');
                    document.getElementById('firebaseProductsList').innerHTML = '<p style="color: orange;">Firebase غير متاح</p>';
                    return [];
                }
                
                const firebaseProducts = await window.firebaseService.loadProducts();
                
                if (firebaseProducts && firebaseProducts.length > 0) {
                    addToLog(`📊 تم العثور على ${firebaseProducts.length} منتج في Firebase`, 'success');
                    displayProductsList(firebaseProducts, 'firebaseProductsList', 'Firebase');
                    return firebaseProducts;
                } else {
                    addToLog('ℹ️ لا توجد منتجات في Firebase', 'info');
                    document.getElementById('firebaseProductsList').innerHTML = '<p style="color: blue;">لا توجد منتجات في Firebase</p>';
                    return [];
                }
                
            } catch (error) {
                addToLog(`❌ خطأ في تحميل المنتجات من Firebase: ${error.message}`, 'error');
                document.getElementById('firebaseProductsList').innerHTML = `<p style="color: red;">خطأ: ${error.message}</p>`;
                return [];
            }
        }

        // Display products list
        function displayProductsList(products, containerId, source) {
            const container = document.getElementById(containerId);
            
            if (!products || products.length === 0) {
                container.innerHTML = `<p style="color: gray;">لا توجد منتجات في ${source}</p>`;
                return;
            }
            
            let html = `<p><strong>عدد المنتجات:</strong> ${products.length}</p>`;
            
            products.slice(0, 10).forEach((product, index) => {
                html += `
                    <div class="product-item">
                        <strong>${index + 1}.</strong> ${product.manufacturer || 'غير محدد'} - 
                        ${product.category || 'غير محدد'} - 
                        ${product.availableQuantity || 0} قطعة
                    </div>
                `;
            });
            
            if (products.length > 10) {
                html += `<div class="product-item"><em>... و ${products.length - 10} منتج آخر</em></div>`;
            }
            
            container.innerHTML = html;
        }

        // Update status overview
        function updateStatusOverview() {
            const statusGrid = document.getElementById('statusGrid');
            
            // Get current status
            let localProducts = [];
            if (typeof window.products !== 'undefined' && Array.isArray(window.products)) {
                localProducts = window.products;
            } else {
                const productsData = localStorage.getItem('inventory_products');
                if (productsData) {
                    localProducts = JSON.parse(productsData);
                }
            }
            
            const isFirebaseReady = window.firebaseService && window.firebaseService.isFirebaseReady();
            const isSyncSystemReady = typeof window.syncProductsToFirebase === 'function';
            
            const statusCards = [
                {
                    title: 'المنتجات المحلية',
                    value: localProducts.length,
                    status: localProducts.length > 0 ? 'success' : 'warning',
                    icon: 'fas fa-laptop'
                },
                {
                    title: 'حالة Firebase',
                    value: isFirebaseReady ? 'متصل' : 'غير متصل',
                    status: isFirebaseReady ? 'success' : 'error',
                    icon: 'fas fa-cloud'
                },
                {
                    title: 'نظام المزامنة',
                    value: isSyncSystemReady ? 'جاهز' : 'غير جاهز',
                    status: isSyncSystemReady ? 'success' : 'error',
                    icon: 'fas fa-sync-alt'
                },
                {
                    title: 'حالة المزامنة',
                    value: 'جاري الفحص...',
                    status: 'info',
                    icon: 'fas fa-chart-line'
                }
            ];
            
            let html = '';
            statusCards.forEach(card => {
                html += `
                    <div class="status-card ${card.status}">
                        <h4><i class="${card.icon}"></i> ${card.title}</h4>
                        <div class="stat-value">${card.value}</div>
                    </div>
                `;
            });
            
            statusGrid.innerHTML = html;
        }

        // Run full diagnostic
        async function runFullDiagnostic() {
            addToLog('🔍 بدء الفحص الشامل...', 'info');
            
            try {
                // Clear previous results
                clearLog();
                addToLog('🚀 بدء الفحص الشامل لمزامنة المنتجات...', 'info');
                
                // Check all components
                await checkFirebaseStatus();
                const localProducts = await loadLocalProducts();
                const firebaseProducts = await loadFirebaseProducts();
                
                // Compare products
                compareProducts(localProducts, firebaseProducts);
                
                // Update status
                updateStatusOverview();
                
                addToLog('✅ تم الانتهاء من الفحص الشامل', 'success');
                
            } catch (error) {
                addToLog(`❌ خطأ في الفحص الشامل: ${error.message}`, 'error');
            }
        }

        // Compare products between local and Firebase
        function compareProducts(localProducts, firebaseProducts) {
            addToLog('🔄 مقارنة المنتجات بين المحلي و Firebase...', 'info');
            
            addToLog(`📊 المحلي: ${localProducts.length} منتج`, 'info');
            addToLog(`📊 Firebase: ${firebaseProducts.length} منتج`, 'info');
            
            if (localProducts.length === firebaseProducts.length) {
                addToLog('✅ عدد المنتجات متطابق', 'success');
            } else {
                addToLog('⚠️ عدد المنتجات مختلف - يحتاج مزامنة', 'warning');
            }
            
            // Check for differences
            if (localProducts.length > 0 && firebaseProducts.length > 0) {
                const localIds = localProducts.map(p => p.id);
                const firebaseIds = firebaseProducts.map(p => p.id);
                
                const missingInFirebase = localProducts.filter(p => !firebaseIds.includes(p.id));
                const missingInLocal = firebaseProducts.filter(p => !localIds.includes(p.id));
                
                if (missingInFirebase.length > 0) {
                    addToLog(`⚠️ ${missingInFirebase.length} منتج موجود محلياً لكن غير موجود في Firebase`, 'warning');
                }
                
                if (missingInLocal.length > 0) {
                    addToLog(`⚠️ ${missingInLocal.length} منتج موجود في Firebase لكن غير موجود محلياً`, 'warning');
                }
                
                if (missingInFirebase.length === 0 && missingInLocal.length === 0) {
                    addToLog('✅ جميع المنتجات متطابقة', 'success');
                }
            }
        }

        // Sync products to Firebase
        async function syncProductsToFirebase() {
            addToLog('📤 رفع المنتجات إلى Firebase...', 'info');
            
            try {
                if (window.syncProductsToFirebase && typeof window.syncProductsToFirebase === 'function') {
                    const result = await window.syncProductsToFirebase();
                    if (result) {
                        addToLog('✅ تم رفع المنتجات إلى Firebase بنجاح', 'success');
                        await loadFirebaseProducts(); // Refresh display
                        updateStatusOverview();
                    } else {
                        addToLog('❌ فشل في رفع المنتجات إلى Firebase', 'error');
                    }
                } else {
                    addToLog('❌ وظيفة رفع المنتجات غير متاحة', 'error');
                }
            } catch (error) {
                addToLog(`❌ خطأ في رفع المنتجات: ${error.message}`, 'error');
            }
        }

        // Load products from Firebase
        async function loadProductsFromFirebase() {
            addToLog('📥 تحميل المنتجات من Firebase...', 'info');
            
            try {
                if (window.loadProductsFromFirebase && typeof window.loadProductsFromFirebase === 'function') {
                    const result = await window.loadProductsFromFirebase();
                    if (result) {
                        addToLog('✅ تم تحميل المنتجات من Firebase بنجاح', 'success');
                        await loadLocalProducts(); // Refresh display
                        updateStatusOverview();
                    } else {
                        addToLog('⚠️ لا توجد منتجات في Firebase', 'warning');
                    }
                } else {
                    addToLog('❌ وظيفة تحميل المنتجات غير متاحة', 'error');
                }
            } catch (error) {
                addToLog(`❌ خطأ في تحميل المنتجات: ${error.message}`, 'error');
            }
        }

        // Bidirectional sync
        async function bidirectionalSync() {
            addToLog('🔄 مزامنة ثنائية الاتجاه...', 'info');
            
            try {
                if (window.syncProductsBidirectional && typeof window.syncProductsBidirectional === 'function') {
                    const result = await window.syncProductsBidirectional();
                    if (result) {
                        addToLog('✅ تمت المزامنة ثنائية الاتجاه بنجاح', 'success');
                        await loadLocalProducts();
                        await loadFirebaseProducts();
                        updateStatusOverview();
                    } else {
                        addToLog('❌ فشل في المزامنة ثنائية الاتجاه', 'error');
                    }
                } else {
                    addToLog('❌ وظيفة المزامنة ثنائية الاتجاه غير متاحة', 'error');
                }
            } catch (error) {
                addToLog(`❌ خطأ في المزامنة ثنائية الاتجاه: ${error.message}`, 'error');
            }
        }

        // Add to log
        function addToLog(message, type = 'info') {
            const log = document.getElementById('diagnosticLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.innerHTML = `[${timestamp}] ${message}`;
            
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        // Clear log
        function clearLog() {
            document.getElementById('diagnosticLog').innerHTML = '';
            addToLog('تم مسح السجل', 'info');
        }
    </script>
</body>
</html>
