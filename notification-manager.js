// 📢 مدير الإشعارات المحسن - Enhanced Notification Manager
// يمنع الإشعارات المتكررة ويدير عرضها بذكاء

class NotificationManager {
    constructor() {
        this.notifications = new Map();
        this.notificationQueue = [];
        this.isProcessing = false;
        this.defaultCooldown = 5000; // 5 seconds
        this.maxNotifications = 3; // Maximum notifications shown at once
        this.notificationDuration = 4000; // 4 seconds display time
        this.groupedNotifications = new Map(); // For grouping similar notifications
        this.groupingDelay = 2000; // 2 seconds to group similar notifications
        this.productsNotificationsEnabled = true; // Enable/disable products notifications
        this.productsNotificationCooldown = 20000; // 20 seconds cooldown for products notifications
        
        // Notification types and their settings
        this.notificationTypes = {
            'sync': { cooldown: 15000, priority: 1 }, // 15 seconds for sync notifications
            'error': { cooldown: 3000, priority: 3 }, // 3 seconds for errors
            'success': { cooldown: 10000, priority: 2 }, // 10 seconds for success (products sync)
            'info': { cooldown: 15000, priority: 1 }, // 15 seconds for info (products updates)
            'warning': { cooldown: 5000, priority: 2 } // 5 seconds for warnings
        };
        
        this.init();
    }

    // Initialize notification manager
    init() {
        console.log('📢 تهيئة مدير الإشعارات المحسن...');
        
        // Override utils.showNotification if it exists
        if (typeof window.utils !== 'undefined' && window.utils.showNotification) {
            this.originalShowNotification = window.utils.showNotification;
            window.utils.showNotification = (message, type, duration) => {
                this.showNotification(message, type, duration);
            };
        }
        
        // Create notification container if it doesn't exist
        this.createNotificationContainer();
        
        console.log('✅ تم تهيئة مدير الإشعارات');
    }

    // Create notification container
    createNotificationContainer() {
        let container = document.getElementById('enhanced-notifications');
        if (!container) {
            container = document.createElement('div');
            container.id = 'enhanced-notifications';
            container.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                max-width: 400px;
                pointer-events: none;
            `;
            document.body.appendChild(container);
        }
        this.container = container;
    }

    // Show notification with smart filtering
    showNotification(message, type = 'info', duration = null) {
        // Check if products notifications are disabled
        if (!this.productsNotificationsEnabled && this.isProductsNotification(message)) {
            console.log(`🔕 إشعارات المنتجات معطلة: ${message}`);
            return;
        }

        // Generate notification key based on message content
        const notificationKey = this.generateNotificationKey(message, type);

        // Check if this notification was recently shown
        if (this.isRecentlyShown(notificationKey, type)) {
            console.log(`⏸️ تجاهل الإشعار المتكرر: ${message}`);
            return;
        }

        // Check for grouping similar notifications
        if (this.shouldGroupNotification(notificationKey, message)) {
            console.log(`📦 تجميع الإشعار مع إشعارات مشابهة: ${message}`);
            return;
        }

        // Create notification object
        const notification = {
            id: Date.now() + Math.random(),
            message,
            type,
            duration: duration || this.notificationDuration,
            timestamp: Date.now(),
            key: notificationKey
        };

        // Add to queue
        this.addToQueue(notification);

        // Process queue
        this.processQueue();
    }

    // Generate notification key for deduplication
    generateNotificationKey(message, type) {
        // Extract key parts from message
        let key = message.toLowerCase();

        // Normalize sync messages
        if (key.includes('تحديث البيانات') || key.includes('مزامنة')) {
            key = 'sync_update';
        } else if (key.includes('تم إضافة') || key.includes('added')) {
            key = 'data_added';
        } else if (key.includes('تم حذف') || key.includes('deleted')) {
            key = 'data_deleted';
        } else if (key.includes('firebase')) {
            key = 'firebase_operation';
        } else if (key.includes('تم تحميل') && key.includes('منتج') && key.includes('السحابة')) {
            key = 'products_loaded_from_cloud';
        } else if (key.includes('تم تحديث المنتجات من السحابة')) {
            key = 'products_updated_from_cloud';
        } else if (key.includes('تم رفع') && key.includes('منتج') && key.includes('السحابة')) {
            key = 'products_uploaded_to_cloud';
        } else if (key.includes('منتج') && key.includes('السحابة')) {
            key = 'products_cloud_sync';
        }

        return `${type}_${key}`;
    }

    // Check if notification was recently shown
    isRecentlyShown(notificationKey, type) {
        const now = Date.now();
        const lastShown = this.notifications.get(notificationKey);

        if (!lastShown) {
            return false;
        }

        const typeSettings = this.notificationTypes[type] || { cooldown: this.defaultCooldown };
        const cooldownPeriod = typeSettings.cooldown;

        return (now - lastShown) < cooldownPeriod;
    }

    // Check if notification should be grouped with similar ones
    shouldGroupNotification(notificationKey, message) {
        const now = Date.now();

        // Check if there's a pending grouped notification
        if (this.groupedNotifications.has(notificationKey)) {
            const groupData = this.groupedNotifications.get(notificationKey);

            // If within grouping delay, increment count
            if (now - groupData.firstTimestamp < this.groupingDelay) {
                groupData.count++;
                groupData.lastMessage = message;
                groupData.lastTimestamp = now;

                // Clear existing timeout and set new one
                if (groupData.timeout) {
                    clearTimeout(groupData.timeout);
                }

                groupData.timeout = setTimeout(() => {
                    this.showGroupedNotification(notificationKey);
                }, this.groupingDelay);

                return true;
            }
        } else {
            // Start grouping for products-related notifications
            if (notificationKey.includes('products_') || notificationKey.includes('cloud')) {
                this.groupedNotifications.set(notificationKey, {
                    count: 1,
                    firstMessage: message,
                    lastMessage: message,
                    firstTimestamp: now,
                    lastTimestamp: now,
                    timeout: setTimeout(() => {
                        this.showGroupedNotification(notificationKey);
                    }, this.groupingDelay)
                });
                return true;
            }
        }

        return false;
    }

    // Show grouped notification
    showGroupedNotification(notificationKey) {
        const groupData = this.groupedNotifications.get(notificationKey);
        if (!groupData) return;

        let message;
        if (groupData.count > 1) {
            if (notificationKey.includes('products_loaded_from_cloud')) {
                message = `تم تحميل المنتجات من السحابة (${groupData.count} عمليات)`;
            } else if (notificationKey.includes('products_updated_from_cloud')) {
                message = `تم تحديث المنتجات من السحابة (${groupData.count} تحديثات)`;
            } else if (notificationKey.includes('products_uploaded_to_cloud')) {
                message = `تم رفع المنتجات إلى السحابة (${groupData.count} عمليات)`;
            } else {
                message = `${groupData.lastMessage} (${groupData.count} مرات)`;
            }
        } else {
            message = groupData.lastMessage;
        }

        // Determine type based on key
        let type = 'info';
        if (notificationKey.includes('success_')) type = 'success';
        else if (notificationKey.includes('error_')) type = 'error';
        else if (notificationKey.includes('warning_')) type = 'warning';

        // Create notification object
        const notification = {
            id: Date.now() + Math.random(),
            message,
            type,
            duration: this.notificationDuration,
            timestamp: Date.now(),
            key: notificationKey + '_grouped'
        };

        // Mark as shown to prevent immediate duplicates
        this.notifications.set(notificationKey, Date.now());

        // Add to queue
        this.addToQueue(notification);

        // Process queue
        this.processQueue();

        // Clean up
        this.groupedNotifications.delete(notificationKey);
    }

    // Add notification to queue
    addToQueue(notification) {
        // Remove duplicates from queue
        this.notificationQueue = this.notificationQueue.filter(n => n.key !== notification.key);
        
        // Add new notification
        this.notificationQueue.push(notification);
        
        // Sort by priority
        this.notificationQueue.sort((a, b) => {
            const priorityA = this.notificationTypes[a.type]?.priority || 1;
            const priorityB = this.notificationTypes[b.type]?.priority || 1;
            return priorityB - priorityA; // Higher priority first
        });
        
        // Limit queue size
        if (this.notificationQueue.length > 10) {
            this.notificationQueue = this.notificationQueue.slice(0, 10);
        }
    }

    // Process notification queue
    async processQueue() {
        if (this.isProcessing || this.notificationQueue.length === 0) {
            return;
        }
        
        this.isProcessing = true;
        
        try {
            // Get current visible notifications
            const visibleNotifications = this.container.children.length;
            
            // Show notifications up to the limit
            while (this.notificationQueue.length > 0 && visibleNotifications < this.maxNotifications) {
                const notification = this.notificationQueue.shift();
                await this.displayNotification(notification);
            }
        } finally {
            this.isProcessing = false;
        }
        
        // Process remaining queue after a delay
        if (this.notificationQueue.length > 0) {
            setTimeout(() => this.processQueue(), 1000);
        }
    }

    // Display individual notification
    async displayNotification(notification) {
        // Mark as shown
        this.notifications.set(notification.key, notification.timestamp);

        // Add to interactive notifications hub
        if (typeof addNotificationToHistory === 'function') {
            addNotificationToHistory(notification.message, notification.type);
        }

        // Check if this is a sync/cloud notification - only show in hub, not as popup
        const isSyncNotification = notification.message.includes('مزامنة') ||
                                  notification.message.includes('السحابة') ||
                                  notification.message.includes('Firebase') ||
                                  notification.message.includes('تم تحميل') ||
                                  notification.message.includes('تم رفع') ||
                                  notification.message.includes('تم تحديث');

        if (isSyncNotification) {
            console.log(`📱 إشعار مزامنة أضيف للمركز فقط: ${notification.message}`);
            return; // Don't show popup for sync notifications
        }

        // Create notification element for non-sync notifications
        const element = this.createNotificationElement(notification);

        // Add to container
        this.container.appendChild(element);

        // Animate in
        setTimeout(() => {
            element.style.transform = 'translateX(0)';
            element.style.opacity = '1';
        }, 10);
        
        // Auto remove after duration
        setTimeout(() => {
            this.removeNotification(element);
        }, notification.duration);
        
        // Also try original notification system as fallback
        if (this.originalShowNotification && notification.type === 'error') {
            try {
                this.originalShowNotification(notification.message, notification.type);
            } catch (error) {
                console.warn('خطأ في النظام الأصلي للإشعارات:', error);
            }
        }
    }

    // Create notification element
    createNotificationElement(notification) {
        const element = document.createElement('div');
        element.className = `notification notification-${notification.type}`;
        element.style.cssText = `
            background: ${this.getNotificationColor(notification.type)};
            color: white;
            padding: 12px 16px;
            margin-bottom: 10px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease;
            pointer-events: auto;
            cursor: pointer;
            font-size: 14px;
            line-height: 1.4;
            max-width: 100%;
            word-wrap: break-word;
        `;
        
        // Add icon based on type
        const icon = this.getNotificationIcon(notification.type);
        element.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <span style="font-size: 16px;">${icon}</span>
                <span style="flex: 1;">${notification.message}</span>
                <span style="opacity: 0.7; cursor: pointer; font-size: 18px;" onclick="this.parentElement.parentElement.remove()">×</span>
            </div>
        `;
        
        // Click to dismiss
        element.addEventListener('click', () => {
            this.removeNotification(element);
        });
        
        return element;
    }

    // Get notification color based on type
    getNotificationColor(type) {
        const colors = {
            success: '#28a745',
            error: '#dc3545',
            warning: '#ffc107',
            info: '#17a2b8',
            sync: '#6f42c1'
        };
        return colors[type] || colors.info;
    }

    // Get notification icon based on type
    getNotificationIcon(type) {
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️',
            sync: '🔄'
        };
        return icons[type] || icons.info;
    }

    // Remove notification with animation
    removeNotification(element) {
        if (!element || !element.parentNode) return;
        
        element.style.transform = 'translateX(100%)';
        element.style.opacity = '0';
        
        setTimeout(() => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
            
            // Process queue if there are pending notifications
            if (this.notificationQueue.length > 0) {
                setTimeout(() => this.processQueue(), 500);
            }
        }, 300);
    }

    // Clear all notifications
    clearAll() {
        while (this.container.firstChild) {
            this.container.removeChild(this.container.firstChild);
        }
        this.notificationQueue = [];
        this.notifications.clear();

        // Clear grouped notifications and their timeouts
        this.groupedNotifications.forEach((groupData) => {
            if (groupData.timeout) {
                clearTimeout(groupData.timeout);
            }
        });
        this.groupedNotifications.clear();

        console.log('🧹 تم مسح جميع الإشعارات والإشعارات المجمعة');
    }

    // Check if notification is products-related
    isProductsNotification(message) {
        const lowerMessage = message.toLowerCase();
        return lowerMessage.includes('منتج') ||
               lowerMessage.includes('المنتجات') ||
               lowerMessage.includes('السحابة') ||
               lowerMessage.includes('firebase') ||
               lowerMessage.includes('تحميل') ||
               lowerMessage.includes('تحديث') ||
               lowerMessage.includes('رفع');
    }

    // Toggle products notifications
    toggleProductsNotifications() {
        this.productsNotificationsEnabled = !this.productsNotificationsEnabled;
        const status = this.productsNotificationsEnabled ? 'مفعلة' : 'معطلة';
        console.log(`📦 إشعارات المنتجات ${status}`);

        // Show status notification
        if (this.productsNotificationsEnabled) {
            this.showNotification('تم تفعيل إشعارات المنتجات', 'success');
        } else {
            this.showNotification('تم تعطيل إشعارات المنتجات', 'warning');
        }

        return this.productsNotificationsEnabled;
    }

    // Disable products notifications temporarily
    disableProductsNotifications(duration = 300000) { // 5 minutes default
        this.productsNotificationsEnabled = false;
        console.log(`🔕 تعطيل إشعارات المنتجات لمدة ${duration/1000} ثانية`);

        setTimeout(() => {
            this.productsNotificationsEnabled = true;
            console.log('🔔 تم تفعيل إشعارات المنتجات تلقائياً');
        }, duration);
    }

    // Pause notifications temporarily
    pauseNotifications(duration = 30000) { // 30 seconds default
        console.log(`⏸️ إيقاف الإشعارات مؤقتاً لمدة ${duration/1000} ثانية`);
        
        this.notificationsPaused = true;
        
        setTimeout(() => {
            this.notificationsPaused = false;
            console.log('▶️ تم استئناف الإشعارات');
        }, duration);
    }

    // Check if notifications are paused
    areNotificationsPaused() {
        return this.notificationsPaused || false;
    }

    // Get notification statistics
    getStats() {
        return {
            totalShown: this.notifications.size,
            queueLength: this.notificationQueue.length,
            visibleCount: this.container.children.length,
            isPaused: this.areNotificationsPaused()
        };
    }
}

// Initialize notification manager
window.notificationManager = new NotificationManager();

// Export for use in other files
window.NotificationManager = NotificationManager;

// Add global functions for easy access
window.pauseNotifications = (duration) => {
    window.notificationManager.pauseNotifications(duration);
};

window.clearAllNotifications = () => {
    window.notificationManager.clearAll();
};

window.getNotificationStats = () => {
    return window.notificationManager.getStats();
};

// Products notifications control
window.toggleProductsNotifications = () => {
    return window.notificationManager.toggleProductsNotifications();
};

window.disableProductsNotifications = (duration) => {
    window.notificationManager.disableProductsNotifications(duration);
};

window.enableProductsNotifications = () => {
    window.notificationManager.productsNotificationsEnabled = true;
    console.log('🔔 تم تفعيل إشعارات المنتجات');
};

console.log('📢 مدير الإشعارات المحسن جاهز للاستخدام');
