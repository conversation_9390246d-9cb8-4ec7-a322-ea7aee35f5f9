<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>عرض بيانات Firebase</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            margin: 20px;
            background: #f0f2f5;
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 25px;
            margin: 10px 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover { background: #45a049; }
        .btn.danger { background: #f44336; }
        .btn.danger:hover { background: #da190b; }
        
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .data-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h1>🔥 عرض بيانات Firebase</h1>
        
        <div class="section">
            <h3>🔧 أدوات العرض</h3>
            <button class="btn" onclick="loadAllData()">📥 تحميل جميع البيانات</button>
            <button class="btn" onclick="loadProducts()">📦 عرض المنتجات</button>
            <button class="btn" onclick="loadCustomers()">👥 عرض العملاء</button>
            <button class="btn" onclick="loadUsers()">🔐 عرض المستخدمين</button>
            <button class="btn" onclick="loadSettings()">⚙️ عرض الإعدادات</button>
            <button class="btn danger" onclick="clearAllFirebaseData()">🗑️ مسح جميع بيانات Firebase</button>
        </div>

        <div id="status" class="status info">جاري التحقق من Firebase...</div>

        <div class="section">
            <h3>📦 المنتجات في Firebase</h3>
            <div id="products-data" class="data-display">لم يتم التحميل بعد...</div>
        </div>

        <div class="section">
            <h3>👥 العملاء في Firebase</h3>
            <div id="customers-data" class="data-display">لم يتم التحميل بعد...</div>
        </div>

        <div class="section">
            <h3>🔐 المستخدمين في Firebase</h3>
            <div id="users-data" class="data-display">لم يتم التحميل بعد...</div>
        </div>

        <div class="section">
            <h3>⚙️ الإعدادات في Firebase</h3>
            <div id="settings-data" class="data-display">لم يتم التحميل بعد...</div>
        </div>

        <div class="section">
            <h3>🔑 بيانات الدخول في Firebase</h3>
            <div id="credentials-data" class="data-display">لم يتم التحميل بعد...</div>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore-compat.js"></script>
    
    <!-- Firebase Configuration -->
    <script src="firebase-config.js"></script>

    <script>
        const statusElement = document.getElementById('status');
        
        // Check Firebase status on load
        window.addEventListener('load', function() {
            setTimeout(checkFirebaseStatus, 1000);
        });

        function checkFirebaseStatus() {
            if (window.firebaseService && window.firebaseService.isFirebaseReady()) {
                statusElement.className = 'status success';
                statusElement.textContent = '✅ Firebase متصل وجاهز';
                loadAllData();
            } else {
                statusElement.className = 'status error';
                statusElement.textContent = '❌ Firebase غير متصل';
            }
        }

        async function loadAllData() {
            console.log('📥 تحميل جميع البيانات من Firebase...');
            await loadProducts();
            await loadCustomers();
            await loadUsers();
            await loadSettings();
            await loadCredentials();
        }

        async function loadProducts() {
            const productsDiv = document.getElementById('products-data');
            
            try {
                if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
                    throw new Error('Firebase not ready');
                }
                
                productsDiv.textContent = 'جاري التحميل...';
                
                const products = await window.firebaseService.loadProducts();
                
                if (products && products.length > 0) {
                    productsDiv.textContent = `عدد المنتجات: ${products.length}\n\n` + 
                        JSON.stringify(products, null, 2);
                } else {
                    productsDiv.textContent = 'لا توجد منتجات في Firebase';
                }
                
            } catch (error) {
                productsDiv.textContent = `خطأ في تحميل المنتجات: ${error.message}`;
                console.error('Error loading products:', error);
            }
        }

        async function loadCustomers() {
            const customersDiv = document.getElementById('customers-data');
            
            try {
                if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
                    throw new Error('Firebase not ready');
                }
                
                customersDiv.textContent = 'جاري التحميل...';
                
                const customers = await window.firebaseService.loadCustomers();
                
                if (customers && customers.length > 0) {
                    customersDiv.textContent = `عدد العملاء: ${customers.length}\n\n` + 
                        JSON.stringify(customers, null, 2);
                } else {
                    customersDiv.textContent = 'لا توجد عملاء في Firebase';
                }
                
            } catch (error) {
                customersDiv.textContent = `خطأ في تحميل العملاء: ${error.message}`;
                console.error('Error loading customers:', error);
            }
        }

        async function loadUsers() {
            const usersDiv = document.getElementById('users-data');
            
            try {
                if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
                    throw new Error('Firebase not ready');
                }
                
                usersDiv.textContent = 'جاري التحميل...';
                
                const users = await window.firebaseService.loadUsers();
                
                if (users && users.length > 0) {
                    usersDiv.textContent = `عدد المستخدمين: ${users.length}\n\n` + 
                        JSON.stringify(users, null, 2);
                } else {
                    usersDiv.textContent = 'لا توجد مستخدمين في Firebase';
                }
                
            } catch (error) {
                usersDiv.textContent = `خطأ في تحميل المستخدمين: ${error.message}`;
                console.error('Error loading users:', error);
            }
        }

        async function loadSettings() {
            const settingsDiv = document.getElementById('settings-data');
            
            try {
                if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
                    throw new Error('Firebase not ready');
                }
                
                settingsDiv.textContent = 'جاري التحميل...';
                
                const settings = await window.firebaseService.loadSettings();
                
                if (settings && Object.keys(settings).length > 0) {
                    settingsDiv.textContent = `عدد الإعدادات: ${Object.keys(settings).length}\n\n` + 
                        JSON.stringify(settings, null, 2);
                } else {
                    settingsDiv.textContent = 'لا توجد إعدادات في Firebase';
                }
                
            } catch (error) {
                settingsDiv.textContent = `خطأ في تحميل الإعدادات: ${error.message}`;
                console.error('Error loading settings:', error);
            }
        }

        async function loadCredentials() {
            const credentialsDiv = document.getElementById('credentials-data');
            
            try {
                if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
                    throw new Error('Firebase not ready');
                }
                
                credentialsDiv.textContent = 'جاري التحميل...';
                
                const credentials = await window.firebaseService.loadLoginCredentials();
                
                if (credentials && Object.keys(credentials).length > 0) {
                    credentialsDiv.textContent = `عدد بيانات الدخول: ${Object.keys(credentials).length}\n\n` + 
                        JSON.stringify(credentials, null, 2);
                } else {
                    credentialsDiv.textContent = 'لا توجد بيانات دخول في Firebase';
                }
                
            } catch (error) {
                credentialsDiv.textContent = `خطأ في تحميل بيانات الدخول: ${error.message}`;
                console.error('Error loading credentials:', error);
            }
        }

        async function clearAllFirebaseData() {
            if (!confirm('هل أنت متأكد من مسح جميع البيانات في Firebase؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                return;
            }
            
            try {
                if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
                    throw new Error('Firebase not ready');
                }
                
                console.log('🗑️ مسح جميع البيانات من Firebase...');
                
                // Delete all collections
                await window.firebaseService.db.collection('products').doc('data').delete();
                await window.firebaseService.db.collection('customers').doc('data').delete();
                await window.firebaseService.db.collection('users').doc('data').delete();
                await window.firebaseService.db.collection('settings').doc('data').delete();
                await window.firebaseService.db.collection('loginCredentials').doc('data').delete();
                
                console.log('✅ تم مسح جميع البيانات من Firebase');
                
                statusElement.className = 'status success';
                statusElement.textContent = '✅ تم مسح جميع البيانات من Firebase';
                
                // Refresh display
                loadAllData();
                
            } catch (error) {
                console.error('❌ خطأ في مسح البيانات:', error);
                statusElement.className = 'status error';
                statusElement.textContent = `❌ خطأ في مسح البيانات: ${error.message}`;
            }
        }
    </script>
</body>
</html>
