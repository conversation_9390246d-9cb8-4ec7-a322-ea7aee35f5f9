// 📦 نظام مزامنة المنتجات - Products Sync System
// يدير مزامنة المنتجات بين localStorage و Firebase

// Global sync functions for products
window.syncProductsToFirebase = async function() {
    console.log('📤 رفع المنتجات إلى Firebase...');
    
    try {
        // Check if Firebase is ready
        if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
            console.log('⚠️ Firebase غير متاح للمزامنة');
            return false;
        }

        // Check permissions
        if (window.hasPermission && !window.hasPermission('products_write')) {
            console.log('⚠️ ليس لديك صلاحية رفع المنتجات');
            return false;
        }

        // Get products from localStorage or global variable
        let products = [];
        
        if (typeof window.products !== 'undefined' && Array.isArray(window.products)) {
            products = window.products;
        } else {
            const productsData = localStorage.getItem('inventory_products');
            if (productsData) {
                products = JSON.parse(productsData);
            }
        }

        if (products.length === 0) {
            console.log('ℹ️ لا توجد منتجات للرفع');
            return true; // Not an error, just no data
        }

        // Upload to Firebase
        const result = await window.firebaseService.saveProducts(products);
        
        if (result) {
            console.log(`✅ تم رفع ${products.length} منتج إلى Firebase بنجاح`);
            
            // Show notification
            if (typeof utils !== 'undefined' && utils.showNotification) {
                utils.showNotification(`تم رفع ${products.length} منتج إلى السحابة`, 'success');
            }
            
            return true;
        } else {
            console.log('❌ فشل في رفع المنتجات إلى Firebase');
            return false;
        }
        
    } catch (error) {
        console.error('❌ خطأ في رفع المنتجات إلى Firebase:', error);
        return false;
    }
};

window.loadProductsFromFirebase = async function() {
    console.log('📥 تحميل المنتجات من Firebase...');
    
    try {
        // Check if Firebase is ready
        if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
            console.log('⚠️ Firebase غير متاح للتحميل');
            return false;
        }

        // Check permissions
        if (window.hasPermission && !window.hasPermission('products_read')) {
            console.log('⚠️ ليس لديك صلاحية تحميل المنتجات');
            return false;
        }

        // Load from Firebase
        const firebaseProducts = await window.firebaseService.loadProducts();
        
        if (firebaseProducts && firebaseProducts.length > 0) {
            console.log(`📥 تم تحميل ${firebaseProducts.length} منتج من Firebase`);
            
            // Update localStorage
            localStorage.setItem('inventory_products', JSON.stringify(firebaseProducts));
            
            // Update global variable
            if (typeof window.products !== 'undefined') {
                window.products.length = 0;
                window.products.push(...firebaseProducts);
            }
            
            // Update UI
            if (typeof loadProductsTable === 'function') {
                loadProductsTable();
            }
            if (typeof updateDashboardStats === 'function') {
                updateDashboardStats();
            }
            
            // Show notification
            if (typeof utils !== 'undefined' && utils.showNotification) {
                utils.showNotification(`تم تحميل ${firebaseProducts.length} منتج من السحابة`, 'success');
            }
            
            console.log('✅ تم تحديث المنتجات من Firebase بنجاح');
            return true;
        } else {
            console.log('ℹ️ لا توجد منتجات في Firebase');
            return false;
        }
        
    } catch (error) {
        console.error('❌ خطأ في تحميل المنتجات من Firebase:', error);
        return false;
    }
};

window.syncProductsBidirectional = async function() {
    console.log('🔄 مزامنة المنتجات ثنائية الاتجاه...');
    
    try {
        // Check if Firebase is ready
        if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
            console.log('⚠️ Firebase غير متاح للمزامنة');
            return false;
        }

        // Get local products
        let localProducts = [];
        if (typeof window.products !== 'undefined' && Array.isArray(window.products)) {
            localProducts = window.products;
        } else {
            const productsData = localStorage.getItem('inventory_products');
            if (productsData) {
                localProducts = JSON.parse(productsData);
            }
        }

        // Get Firebase products
        const firebaseProducts = await window.firebaseService.loadProducts();
        
        console.log(`📊 المنتجات المحلية: ${localProducts.length}`);
        console.log(`📊 المنتجات في Firebase: ${firebaseProducts ? firebaseProducts.length : 0}`);
        
        // Determine sync direction
        if (!firebaseProducts || firebaseProducts.length === 0) {
            // No products in Firebase, upload local products
            if (localProducts.length > 0) {
                console.log('📤 رفع المنتجات المحلية إلى Firebase...');
                return await window.syncProductsToFirebase();
            } else {
                console.log('ℹ️ لا توجد منتجات للمزامنة');
                return true;
            }
        } else if (localProducts.length === 0) {
            // No local products, download from Firebase
            console.log('📥 تحميل المنتجات من Firebase...');
            return await window.loadProductsFromFirebase();
        } else {
            // Both have products, compare timestamps or use Firebase as source of truth
            console.log('🔄 كلا المكانين يحتوي على منتجات، استخدام Firebase كمصدر الحقيقة...');
            return await window.loadProductsFromFirebase();
        }
        
    } catch (error) {
        console.error('❌ خطأ في المزامنة ثنائية الاتجاه:', error);
        return false;
    }
};

// Auto-sync products when they change
window.autoSyncProducts = function() {
    console.log('🔄 تفعيل المزامنة التلقائية للمنتجات...');
    
    // Override the storage.saveProducts function to include auto-sync
    if (typeof window.storage !== 'undefined' && window.storage.saveProducts) {
        const originalSaveProducts = window.storage.saveProducts;
        
        window.storage.saveProducts = function() {
            // Call original save function
            originalSaveProducts();
            
            // Auto-sync to Firebase
            setTimeout(() => {
                window.syncProductsToFirebase();
            }, 1000);
        };
        
        console.log('✅ تم تفعيل المزامنة التلقائية للمنتجات');
    }
};

// Initialize products sync on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('📦 تهيئة نظام مزامنة المنتجات...');
    
    // Wait for other systems to load
    setTimeout(() => {
        // Auto-sync products if Firebase is available
        if (window.firebaseService && window.firebaseService.isFirebaseReady()) {
            window.syncProductsBidirectional();
        }
        
        // Enable auto-sync
        window.autoSyncProducts();
        
        console.log('✅ تم تهيئة نظام مزامنة المنتجات');
    }, 3000);
});

// Setup realtime listeners for products
window.setupProductsRealtimeSync = function() {
    console.log('🔗 إعداد المزامنة اللحظية للمنتجات...');
    
    try {
        if (!window.firebaseService || !window.firebaseService.isFirebaseReady()) {
            console.log('⚠️ Firebase غير متاح للمزامنة اللحظية');
            return false;
        }

        // Listen for products changes in Firebase
        const productsCollection = window.firebaseService.db.collection('products');
        
        const unsubscribe = productsCollection.onSnapshot((snapshot) => {
            console.log('🔄 تغيير في المنتجات في Firebase...');
            
            snapshot.docChanges().forEach((change) => {
                if (change.type === 'modified' || change.type === 'added') {
                    const data = change.doc.data();
                    if (data.products && Array.isArray(data.products)) {
                        console.log(`📥 تحديث المنتجات: ${data.products.length} منتج`);
                        
                        // Update local storage
                        localStorage.setItem('inventory_products', JSON.stringify(data.products));
                        
                        // Update global variable
                        if (typeof window.products !== 'undefined') {
                            window.products.length = 0;
                            window.products.push(...data.products);
                        }
                        
                        // Update UI
                        if (typeof loadProductsTable === 'function') {
                            loadProductsTable();
                        }
                        if (typeof updateDashboardStats === 'function') {
                            updateDashboardStats();
                        }
                        
                        // Show notification
                        if (typeof utils !== 'undefined' && utils.showNotification) {
                            utils.showNotification('تم تحديث المنتجات من السحابة', 'info');
                        }
                    }
                }
            });
        }, (error) => {
            console.error('❌ خطأ في مستمع المنتجات:', error);
        });
        
        // Store unsubscribe function
        window.productsRealtimeUnsubscribe = unsubscribe;
        
        console.log('✅ تم إعداد المزامنة اللحظية للمنتجات');
        return true;
        
    } catch (error) {
        console.error('❌ خطأ في إعداد المزامنة اللحظية للمنتجات:', error);
        return false;
    }
};

// Setup realtime sync when Firebase is ready
setTimeout(() => {
    if (window.firebaseService && window.firebaseService.isFirebaseReady()) {
        window.setupProductsRealtimeSync();
    }
}, 5000);

console.log('📦 نظام مزامنة المنتجات جاهز للاستخدام');
