<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔗 فحص ربط Netlify مع Firebase</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border-radius: 10px;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: bold;
        }

        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
            font-weight: bold;
        }

        .btn.primary { background: #007bff; color: white; }
        .btn.success { background: #28a745; color: white; }
        .btn.warning { background: #ffc107; color: #212529; }
        .btn.danger { background: #dc3545; color: white; }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
        }

        .config-display {
            background: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 10px 0;
            overflow-x: auto;
        }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            text-align: center;
        }

        .card-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }

        .card-label {
            font-size: 14px;
            color: #666;
        }

        .log {
            background: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔗 فحص ربط Netlify مع Firebase</h1>
            <p>تحقق من حالة الاتصال بين موقعك على Netlify وقاعدة بيانات Firebase</p>
        </div>

        <!-- Overall Status -->
        <div class="section">
            <h3>📊 الحالة العامة</h3>
            <div id="overallStatus" class="status info">جاري الفحص...</div>
            <button class="btn primary" onclick="runFullCheck()">🔍 فحص شامل</button>
            <button class="btn success" onclick="testConnection()">🧪 اختبار الاتصال</button>
        </div>

        <!-- Environment Info -->
        <div class="section">
            <h3>🌐 معلومات البيئة</h3>
            <div class="grid" id="environmentGrid">
                <!-- Will be populated by JavaScript -->
            </div>
        </div>

        <!-- Firebase Configuration -->
        <div class="section">
            <h3>🔥 إعدادات Firebase</h3>
            <div id="firebaseConfig" class="config-display">جاري التحميل...</div>
            <button class="btn warning" onclick="showFirebaseConfig()">📋 عرض الإعدادات</button>
        </div>

        <!-- Connection Tests -->
        <div class="section">
            <h3>🧪 اختبارات الاتصال</h3>
            <div id="connectionTests">
                <button class="btn primary" onclick="testFirebaseInit()">🔥 اختبار تهيئة Firebase</button>
                <button class="btn success" onclick="testFirestoreRead()">📖 اختبار قراءة Firestore</button>
                <button class="btn warning" onclick="testFirestoreWrite()">✍️ اختبار كتابة Firestore</button>
                <button class="btn danger" onclick="testAuth()">🔐 اختبار المصادقة</button>
            </div>
            <div id="testResults"></div>
        </div>

        <!-- Data Sync Test -->
        <div class="section">
            <h3>🔄 اختبار مزامنة البيانات</h3>
            <button class="btn success" onclick="testDataSync()">🔄 اختبار مزامنة المنتجات</button>
            <div id="syncResults"></div>
        </div>

        <!-- Log -->
        <div class="section">
            <h3>📝 سجل العمليات</h3>
            <button class="btn danger" onclick="clearLog()">🗑️ مسح السجل</button>
            <div class="log" id="log">
                <div>[INFO] مرحباً بك في أداة فحص ربط Netlify مع Firebase</div>
            </div>
        </div>
    </div>

    <!-- Firebase Scripts -->
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.0.0/firebase-auth-compat.js"></script>
    <script src="firebase-config.js"></script>

    <script>
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 بدء فحص ربط Netlify مع Firebase...', 'info');
            
            setTimeout(() => {
                updateEnvironmentInfo();
                runFullCheck();
            }, 2000);
        });

        // Run full check
        async function runFullCheck() {
            log('🔍 بدء الفحص الشامل...', 'info');
            updateStatus('جاري الفحص الشامل...', 'info');

            try {
                // Check environment
                const envInfo = getEnvironmentInfo();
                log(`🌐 البيئة: ${envInfo.isNetlify ? 'Netlify' : 'محلي'}`, 'info');
                log(`🌐 النطاق: ${envInfo.domain}`, 'info');

                // Check Firebase
                const firebaseStatus = await checkFirebaseStatus();
                log(`🔥 Firebase: ${firebaseStatus ? 'متصل' : 'غير متصل'}`, firebaseStatus ? 'success' : 'error');

                // Check Firestore
                const firestoreStatus = await checkFirestoreStatus();
                log(`📊 Firestore: ${firestoreStatus ? 'يعمل' : 'لا يعمل'}`, firestoreStatus ? 'success' : 'error');

                // Overall status
                if (firebaseStatus && firestoreStatus) {
                    updateStatus('✅ الربط يعمل بشكل مثالي!', 'success');
                    log('✅ جميع الاختبارات نجحت - الربط يعمل بشكل مثالي!', 'success');
                } else {
                    updateStatus('❌ هناك مشاكل في الربط', 'error');
                    log('❌ هناك مشاكل في الربط تحتاج إلى حل', 'error');
                }

            } catch (error) {
                log(`❌ خطأ في الفحص الشامل: ${error.message}`, 'error');
                updateStatus('❌ خطأ في الفحص', 'error');
            }
        }

        // Get environment info
        function getEnvironmentInfo() {
            const hostname = window.location.hostname;
            const protocol = window.location.protocol;
            const port = window.location.port;
            
            return {
                hostname: hostname,
                domain: window.location.origin,
                protocol: protocol,
                port: port,
                isNetlify: hostname.includes('netlify') || hostname.includes('.app'),
                isLocalhost: hostname === 'localhost' || hostname === '127.0.0.1',
                isHTTPS: protocol === 'https:',
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString()
            };
        }

        // Update environment info display
        function updateEnvironmentInfo() {
            const envInfo = getEnvironmentInfo();
            const grid = document.getElementById('environmentGrid');
            
            grid.innerHTML = `
                <div class="card">
                    <div class="card-value">${envInfo.isNetlify ? '✅' : '❌'}</div>
                    <div class="card-label">Netlify</div>
                </div>
                <div class="card">
                    <div class="card-value">${envInfo.isHTTPS ? '✅' : '❌'}</div>
                    <div class="card-label">HTTPS</div>
                </div>
                <div class="card">
                    <div class="card-value">${envInfo.hostname}</div>
                    <div class="card-label">النطاق</div>
                </div>
                <div class="card">
                    <div class="card-value">${envInfo.protocol}</div>
                    <div class="card-label">البروتوكول</div>
                </div>
            `;
        }

        // Check Firebase status
        async function checkFirebaseStatus() {
            try {
                if (typeof firebase === 'undefined') {
                    log('❌ Firebase SDK غير محمل', 'error');
                    return false;
                }

                if (firebase.apps.length === 0) {
                    log('❌ Firebase غير مُهيأ', 'error');
                    return false;
                }

                const app = firebase.apps[0];
                if (!app) {
                    log('❌ Firebase App غير متاح', 'error');
                    return false;
                }

                log('✅ Firebase مُهيأ ويعمل', 'success');
                return true;

            } catch (error) {
                log(`❌ خطأ في فحص Firebase: ${error.message}`, 'error');
                return false;
            }
        }

        // Check Firestore status
        async function checkFirestoreStatus() {
            try {
                if (!firebase.firestore) {
                    log('❌ Firestore غير متاح', 'error');
                    return false;
                }

                const db = firebase.firestore();
                if (!db) {
                    log('❌ Firestore غير مُهيأ', 'error');
                    return false;
                }

                // Try to read from Firestore
                const testDoc = await db.collection('test').doc('connection').get();
                log('✅ Firestore متصل ويعمل', 'success');
                return true;

            } catch (error) {
                if (error.code === 'permission-denied') {
                    log('✅ Firestore متصل (رفض الصلاحية طبيعي)', 'success');
                    return true;
                } else {
                    log(`❌ خطأ في Firestore: ${error.message}`, 'error');
                    return false;
                }
            }
        }

        // Show Firebase configuration
        function showFirebaseConfig() {
            const configDiv = document.getElementById('firebaseConfig');
            
            if (firebase.apps.length > 0) {
                const app = firebase.apps[0];
                const config = app.options;
                
                configDiv.innerHTML = `
                    <div style="color: #00ff00;">Firebase Configuration:</div>
                    <div>Project ID: ${config.projectId || 'غير محدد'}</div>
                    <div>Auth Domain: ${config.authDomain || 'غير محدد'}</div>
                    <div>API Key: ${config.apiKey ? config.apiKey.substring(0, 10) + '...' : 'غير محدد'}</div>
                    <div>Storage Bucket: ${config.storageBucket || 'غير محدد'}</div>
                    <div>Messaging Sender ID: ${config.messagingSenderId || 'غير محدد'}</div>
                    <div>App ID: ${config.appId ? config.appId.substring(0, 20) + '...' : 'غير محدد'}</div>
                `;
                
                log('📋 تم عرض إعدادات Firebase', 'info');
            } else {
                configDiv.innerHTML = '<div style="color: red;">Firebase غير مُهيأ</div>';
                log('❌ لا يمكن عرض إعدادات Firebase - غير مُهيأ', 'error');
            }
        }

        // Test connection
        async function testConnection() {
            log('🧪 اختبار الاتصال...', 'info');
            updateStatus('جاري اختبار الاتصال...', 'info');

            try {
                // Test internet connection
                const response = await fetch('https://www.google.com/favicon.ico', {
                    method: 'HEAD',
                    mode: 'no-cors'
                });
                log('✅ الاتصال بالإنترنت يعمل', 'success');

                // Test Firebase connection
                if (firebase.apps.length > 0) {
                    const db = firebase.firestore();
                    await db.collection('test').doc('connection').get();
                    log('✅ الاتصال بـ Firebase يعمل', 'success');
                    updateStatus('✅ جميع الاتصالات تعمل', 'success');
                } else {
                    log('❌ Firebase غير مُهيأ', 'error');
                    updateStatus('❌ Firebase غير مُهيأ', 'error');
                }

            } catch (error) {
                log(`❌ خطأ في اختبار الاتصال: ${error.message}`, 'error');
                updateStatus('❌ خطأ في الاتصال', 'error');
            }
        }

        // Test Firebase initialization
        function testFirebaseInit() {
            log('🔥 اختبار تهيئة Firebase...', 'info');
            
            const results = document.getElementById('testResults');
            let html = '<h4>نتائج اختبار Firebase:</h4>';

            // Check Firebase SDK
            if (typeof firebase !== 'undefined') {
                html += '<div class="status success">✅ Firebase SDK محمل</div>';
                log('✅ Firebase SDK محمل', 'success');
            } else {
                html += '<div class="status error">❌ Firebase SDK غير محمل</div>';
                log('❌ Firebase SDK غير محمل', 'error');
                results.innerHTML = html;
                return;
            }

            // Check Firebase apps
            if (firebase.apps.length > 0) {
                html += '<div class="status success">✅ Firebase مُهيأ</div>';
                log('✅ Firebase مُهيأ', 'success');
                
                const app = firebase.apps[0];
                const config = app.options;
                html += `<div class="status info">📋 Project ID: ${config.projectId}</div>`;
                log(`📋 Project ID: ${config.projectId}`, 'info');
            } else {
                html += '<div class="status error">❌ Firebase غير مُهيأ</div>';
                log('❌ Firebase غير مُهيأ', 'error');
            }

            results.innerHTML = html;
        }

        // Test Firestore read
        async function testFirestoreRead() {
            log('📖 اختبار قراءة Firestore...', 'info');
            
            try {
                const db = firebase.firestore();
                const testDoc = await db.collection('products').limit(1).get();
                
                log('✅ قراءة Firestore تعمل', 'success');
                
                const results = document.getElementById('testResults');
                results.innerHTML = '<div class="status success">✅ قراءة Firestore تعمل بنجاح</div>';
                
            } catch (error) {
                log(`❌ خطأ في قراءة Firestore: ${error.message}`, 'error');
                
                const results = document.getElementById('testResults');
                results.innerHTML = `<div class="status error">❌ خطأ في قراءة Firestore: ${error.message}</div>`;
            }
        }

        // Test Firestore write
        async function testFirestoreWrite() {
            log('✍️ اختبار كتابة Firestore...', 'info');
            
            try {
                const db = firebase.firestore();
                const testData = {
                    test: true,
                    timestamp: new Date(),
                    source: 'netlify-firebase-check'
                };
                
                await db.collection('test').doc('write-test').set(testData);
                
                log('✅ كتابة Firestore تعمل', 'success');
                
                const results = document.getElementById('testResults');
                results.innerHTML = '<div class="status success">✅ كتابة Firestore تعمل بنجاح</div>';
                
            } catch (error) {
                log(`❌ خطأ في كتابة Firestore: ${error.message}`, 'error');
                
                const results = document.getElementById('testResults');
                results.innerHTML = `<div class="status error">❌ خطأ في كتابة Firestore: ${error.message}</div>`;
            }
        }

        // Test authentication
        async function testAuth() {
            log('🔐 اختبار المصادقة...', 'info');
            
            try {
                if (!firebase.auth) {
                    throw new Error('Firebase Auth غير متاح');
                }

                const auth = firebase.auth();
                const user = auth.currentUser;
                
                if (user) {
                    log(`✅ مستخدم مسجل دخول: ${user.email}`, 'success');
                } else {
                    log('ℹ️ لا يوجد مستخدم مسجل دخول', 'info');
                }
                
                const results = document.getElementById('testResults');
                results.innerHTML = `<div class="status success">✅ Firebase Auth يعمل - ${user ? 'مسجل دخول' : 'غير مسجل دخول'}</div>`;
                
            } catch (error) {
                log(`❌ خطأ في المصادقة: ${error.message}`, 'error');
                
                const results = document.getElementById('testResults');
                results.innerHTML = `<div class="status error">❌ خطأ في المصادقة: ${error.message}</div>`;
            }
        }

        // Test data sync
        async function testDataSync() {
            log('🔄 اختبار مزامنة البيانات...', 'info');
            
            try {
                const db = firebase.firestore();
                
                // Test reading products
                const productsSnapshot = await db.collection('products').limit(5).get();
                const productsCount = productsSnapshot.size;
                
                log(`📊 تم العثور على ${productsCount} منتج في Firebase`, 'info');
                
                // Test local storage
                const localProducts = localStorage.getItem('inventory_products');
                const localCount = localProducts ? JSON.parse(localProducts).length : 0;
                
                log(`📱 تم العثور على ${localCount} منتج محلي`, 'info');
                
                const results = document.getElementById('syncResults');
                results.innerHTML = `
                    <div class="status info">📊 المنتجات في Firebase: ${productsCount}</div>
                    <div class="status info">📱 المنتجات المحلية: ${localCount}</div>
                    <div class="status success">✅ مزامنة البيانات تعمل</div>
                `;
                
                log('✅ اختبار مزامنة البيانات نجح', 'success');
                
            } catch (error) {
                log(`❌ خطأ في اختبار مزامنة البيانات: ${error.message}`, 'error');
                
                const results = document.getElementById('syncResults');
                results.innerHTML = `<div class="status error">❌ خطأ في مزامنة البيانات: ${error.message}</div>`;
            }
        }

        // Update status
        function updateStatus(message, type) {
            const statusEl = document.getElementById('overallStatus');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }

        // Log function
        function log(message, type = 'info') {
            const logEl = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                info: '#00ff00',
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107'
            };
            
            const logEntry = document.createElement('div');
            logEntry.style.color = colors[type] || colors.info;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            
            logEl.appendChild(logEntry);
            logEl.scrollTop = logEl.scrollHeight;

            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // Clear log
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            log('تم مسح السجل', 'info');
        }

        // Auto-run on load
        setTimeout(() => {
            showFirebaseConfig();
        }, 3000);
    </script>
</body>
</html>
