/* 📢 مركز الإشعارات التفاعلي */

/* أيقونة مركز الإشعارات المحسنة */
.notifications-hub {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 55px;
    height: 55px;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    margin-right: 15px;
    box-shadow: 0 8px 25px rgba(79, 172, 254, 0.3);
    border: 3px solid rgba(255, 255, 255, 0.2);
}

.notifications-hub:hover {
    transform: translateY(-3px) scale(1.08);
    box-shadow: 0 15px 35px rgba(79, 172, 254, 0.4);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.notification-icon-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notifications-hub i {
    color: white;
    font-size: 22px;
    transition: all 0.3s ease;
    z-index: 2;
    position: relative;
}

.notifications-hub:hover i {
    transform: scale(1.15) rotate(15deg);
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.5));
}

/* موجات الإشعارات */
.notification-waves {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.wave {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border: 2px solid rgba(255, 255, 255, 0.4);
    border-radius: 50%;
    opacity: 0;
}

.notifications-hub.has-notifications .wave {
    animation: waveAnimation 2s infinite;
}

.wave-1 {
    width: 60px;
    height: 60px;
    animation-delay: 0s;
}

.wave-2 {
    width: 80px;
    height: 80px;
    animation-delay: 0.5s;
}

.wave-3 {
    width: 100px;
    height: 100px;
    animation-delay: 1s;
}

@keyframes waveAnimation {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.5);
    }
    50% {
        opacity: 0.6;
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(1.2);
    }
}

/* عداد الإشعارات المحسن */
.notification-count {
    position: absolute;
    top: -10px;
    right: -10px;
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    font-size: 12px;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid white;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0;
    transform: scale(0);
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
}

.notification-count.active {
    opacity: 1;
    transform: scale(1);
}

.notification-count.pulse {
    animation: countPulse 0.8s ease-in-out;
}

@keyframes countPulse {
    0% {
        transform: scale(1);
        box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
    }
    50% {
        transform: scale(1.4);
        box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
    }
}

/* نقطة حالة الإشعارات المحسنة */
.notification-status-dot {
    position: absolute;
    bottom: -5px;
    left: -5px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: linear-gradient(135deg, #2ed573 0%, #17a2b8 100%);
    border: 3px solid white;
    transition: all 0.4s ease;
    box-shadow: 0 2px 8px rgba(46, 213, 115, 0.3);
}

.notification-status-dot.has-notifications {
    background: linear-gradient(135deg, #ffa502 0%, #ff6348 100%);
    animation: statusPulse 2s infinite;
    box-shadow: 0 2px 8px rgba(255, 165, 2, 0.4);
}

.notification-status-dot.has-errors {
    background: linear-gradient(135deg, #ff4757 0%, #c44569 100%);
    animation: statusPulse 1s infinite;
    box-shadow: 0 2px 8px rgba(255, 71, 87, 0.5);
}

@keyframes statusPulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
        box-shadow: 0 2px 8px rgba(255, 165, 2, 0.4);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.3);
        box-shadow: 0 4px 16px rgba(255, 165, 2, 0.6);
    }
}

/* لوحة الإشعارات */
.notifications-panel {
    position: fixed;
    top: 80px;
    right: 20px;
    width: 400px;
    max-height: 600px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    z-index: 10000;
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
    pointer-events: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notifications-panel.active {
    opacity: 1;
    transform: translateY(0) scale(1);
    pointer-events: all;
}

/* رأس اللوحة */
.notifications-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.notifications-panel-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.notifications-panel-header h3 i {
    color: #667eea;
    font-size: 20px;
}

.panel-controls {
    display: flex;
    gap: 10px;
}

.panel-btn {
    width: 35px;
    height: 35px;
    border: none;
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.panel-btn:hover {
    background: rgba(102, 126, 234, 0.2);
    transform: scale(1.1);
}

/* محتوى اللوحة */
.notifications-panel-content {
    max-height: 450px;
    overflow-y: auto;
    padding: 15px 25px;
}

.notifications-panel-content::-webkit-scrollbar {
    width: 6px;
}

.notifications-panel-content::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.05);
    border-radius: 3px;
}

.notifications-panel-content::-webkit-scrollbar-thumb {
    background: rgba(102, 126, 234, 0.3);
    border-radius: 3px;
}

.notifications-panel-content::-webkit-scrollbar-thumb:hover {
    background: rgba(102, 126, 234, 0.5);
}

/* رسالة عدم وجود إشعارات */
.no-notifications {
    text-align: center;
    padding: 40px 20px;
    color: #999;
}

.no-notifications i {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
}

.no-notifications p {
    margin: 0;
    font-size: 16px;
}

/* عنصر الإشعار في اللوحة */
.panel-notification {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    padding: 15px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.panel-notification:hover {
    transform: translateX(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.panel-notification::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: #667eea;
}

.panel-notification.success::before { background: #2ed573; }
.panel-notification.error::before { background: #ff4757; }
.panel-notification.warning::before { background: #ffa502; }
.panel-notification.info::before { background: #3742fa; }

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.notification-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 12px;
    background: #667eea;
}

.notification-icon.success { background: #2ed573; }
.notification-icon.error { background: #ff4757; }
.notification-icon.warning { background: #ffa502; }
.notification-icon.info { background: #3742fa; }

.notification-time {
    font-size: 11px;
    color: #999;
}

.notification-message {
    color: #333;
    font-size: 14px;
    line-height: 1.4;
    margin: 0;
}

/* ذيل اللوحة */
.notifications-panel-footer {
    padding: 15px 25px 20px;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    background: rgba(102, 126, 234, 0.05);
    border-radius: 0 0 20px 20px;
}

.notification-stats {
    text-align: center;
    color: #667eea;
    font-size: 13px;
    font-weight: 500;
}

/* زر إشعارات المنتجات */
#productsNotificationToggle {
    position: relative;
}

#productsNotificationToggle i {
    transition: color 0.3s ease;
}

/* حالة مفعل - أخضر */
#productsNotificationToggle.enabled i {
    color: #28a745 !important;
}

/* حالة معطل - أحمر */
#productsNotificationToggle.disabled i {
    color: #dc3545 !important;
}

/* مؤشر حالة الإشعارات */
.notification-status-indicator {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #28a745;
    border: 2px solid white;
    transition: background-color 0.3s ease;
}

.notification-status-indicator.disabled {
    background: #dc3545;
}

/* تحسين مظهر الإشعارات */
.notification-container {
    z-index: 10000;
}

.notification {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
}

/* إشعارات المنتجات المجمعة */
.notification.grouped {
    border-left: 4px solid #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
}

.notification.grouped .notification-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

/* تحسين النصوص العربية */
.notification-message {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.4;
    text-align: right;
    direction: rtl;
}

/* أنيميشن للإشعارات المجمعة */
@keyframes groupedPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.notification.grouped {
    animation: groupedPulse 2s ease-in-out;
}

/* تحسين الألوان للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .notification {
        background: rgba(30, 30, 30, 0.95);
        color: #ffffff;
        border-color: rgba(255,255,255,0.1);
    }
    
    .notification.grouped {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
    }
}

/* تحسين الاستجابة للهواتف */
@media (max-width: 768px) {
    .notifications-hub {
        width: 48px;
        height: 48px;
        margin-right: 10px;
    }

    .notifications-hub i {
        font-size: 20px;
    }

    .notification-count {
        width: 20px;
        height: 20px;
        font-size: 11px;
        top: -8px;
        right: -8px;
    }

    .notification-status-dot {
        width: 14px;
        height: 14px;
        bottom: -4px;
        left: -4px;
    }

    .notifications-panel {
        right: 10px;
        left: 10px;
        width: auto;
        max-height: 70vh;
        top: 70px;
    }

    .notifications-panel-header {
        padding: 15px 20px 10px;
    }

    .notifications-panel-header h3 {
        font-size: 16px;
    }

    .notifications-panel-content {
        padding: 10px 20px;
        max-height: 50vh;
    }

    .panel-notification {
        padding: 12px;
        margin-bottom: 8px;
    }

    .notification-message {
        font-size: 13px;
    }

    .notification {
        margin: 5px;
        padding: 12px;
        font-size: 14px;
    }

    .notification-container {
        right: 10px;
        top: 70px;
        max-width: calc(100vw - 20px);
    }
}

/* تأثيرات خاصة للإشعارات المهمة */
.notification.products-sync {
    border-left: 4px solid #17a2b8;
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(0, 123, 255, 0.1));
}

.notification.products-error {
    border-left: 4px solid #dc3545;
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(255, 0, 0, 0.1));
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* تحسين tooltip للأزرار */
.btn-icon[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    pointer-events: none;
}

/* تحسين العداد للإشعارات المجمعة */
.notification-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    border: 2px solid white;
}

/* تحسين الأيقونات */
.header-actions .btn-icon i {
    font-size: 16px;
    transition: all 0.3s ease;
}

.header-actions .btn-icon:hover i {
    transform: scale(1.1);
}

/* تحسين الألوان الافتراضية */
.btn-icon {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: #333;
    border-radius: 8px;
    padding: 10px;
    margin: 0 3px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-icon:hover {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.3);
}

/* تحسين الوضع المظلم للأزرار */
@media (prefers-color-scheme: dark) {
    .btn-icon {
        background: rgba(0,0,0,0.3);
        border-color: rgba(255,255,255,0.1);
        color: #ffffff;
    }
    
    .btn-icon:hover {
        background: rgba(0,0,0,0.5);
        border-color: rgba(255,255,255,0.2);
    }
}
