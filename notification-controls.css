/* 📢 تحسينات أزرار التحكم في الإشعارات */

/* تحسين أزرار الهيدر */
.header-actions .btn-icon {
    position: relative;
    transition: all 0.3s ease;
}

/* تأثيرات hover للأزرار */
.header-actions .btn-icon:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

/* زر إشعارات المنتجات */
#productsNotificationToggle {
    position: relative;
}

#productsNotificationToggle i {
    transition: color 0.3s ease;
}

/* حالة مفعل - أخضر */
#productsNotificationToggle.enabled i {
    color: #28a745 !important;
}

/* حالة معطل - أحمر */
#productsNotificationToggle.disabled i {
    color: #dc3545 !important;
}

/* مؤشر حالة الإشعارات */
.notification-status-indicator {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #28a745;
    border: 2px solid white;
    transition: background-color 0.3s ease;
}

.notification-status-indicator.disabled {
    background: #dc3545;
}

/* تحسين مظهر الإشعارات */
.notification-container {
    z-index: 10000;
}

.notification {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    box-shadow: 0 8px 32px rgba(0,0,0,0.3);
}

/* إشعارات المنتجات المجمعة */
.notification.grouped {
    border-left: 4px solid #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
}

.notification.grouped .notification-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

/* تحسين النصوص العربية */
.notification-message {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.4;
    text-align: right;
    direction: rtl;
}

/* أنيميشن للإشعارات المجمعة */
@keyframes groupedPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.notification.grouped {
    animation: groupedPulse 2s ease-in-out;
}

/* تحسين الألوان للوضع المظلم */
@media (prefers-color-scheme: dark) {
    .notification {
        background: rgba(30, 30, 30, 0.95);
        color: #ffffff;
        border-color: rgba(255,255,255,0.1);
    }
    
    .notification.grouped {
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
    }
}

/* تحسين الاستجابة للهواتف */
@media (max-width: 768px) {
    .header-actions .btn-icon {
        padding: 8px;
        margin: 0 2px;
    }
    
    .notification {
        margin: 5px;
        padding: 12px;
        font-size: 14px;
    }
    
    .notification-container {
        right: 10px;
        top: 70px;
        max-width: calc(100vw - 20px);
    }
}

/* تأثيرات خاصة للإشعارات المهمة */
.notification.products-sync {
    border-left: 4px solid #17a2b8;
    background: linear-gradient(135deg, rgba(23, 162, 184, 0.1), rgba(0, 123, 255, 0.1));
}

.notification.products-error {
    border-left: 4px solid #dc3545;
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1), rgba(255, 0, 0, 0.1));
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* تحسين tooltip للأزرار */
.btn-icon[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: -35px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    pointer-events: none;
}

/* تحسين العداد للإشعارات المجمعة */
.notification-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #dc3545;
    color: white;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    font-size: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    border: 2px solid white;
}

/* تحسين الأيقونات */
.header-actions .btn-icon i {
    font-size: 16px;
    transition: all 0.3s ease;
}

.header-actions .btn-icon:hover i {
    transform: scale(1.1);
}

/* تحسين الألوان الافتراضية */
.btn-icon {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: #333;
    border-radius: 8px;
    padding: 10px;
    margin: 0 3px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-icon:hover {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.3);
}

/* تحسين الوضع المظلم للأزرار */
@media (prefers-color-scheme: dark) {
    .btn-icon {
        background: rgba(0,0,0,0.3);
        border-color: rgba(255,255,255,0.1);
        color: #ffffff;
    }
    
    .btn-icon:hover {
        background: rgba(0,0,0,0.5);
        border-color: rgba(255,255,255,0.2);
    }
}
