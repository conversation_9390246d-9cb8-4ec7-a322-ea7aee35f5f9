# 🌐 دليل تكامل APIs - تطبيق النسور الماسية

## 🎯 نظرة عامة

تم تطوير نظام تكامل APIs شامل يدعم عدة أنواع من الاتصالات الخارجية:

### 📋 APIs المدعومة:
1. **🔵 Google Drive API** - التخزين السحابي (مفعل افتراضياً)
2. **🟢 REST API** - تكامل مع خوادم خارجية
3. **🟡 Email API** - إرسال الإشعارات بالبريد الإلكتروني
4. **🟠 Webhooks** - إشعارات فورية للأنظمة الخارجية

---

## 🚀 الوصول لإدارة APIs

### من التطبيق الرئيسي:
1. اذهب إلى **قسم الإعدادات**
2. ابحث عن **"التخزين السحابي"**
3. اضغ<PERSON> على **"إدارة APIs"**

### أو مباشرة:
```
افتح: api-management.html
```

---

## 🔵 Google Drive API

### ✅ **مفعل افتراضياً**
- **الوظيفة:** حفظ ومزامنة البيانات في Google Drive
- **البيانات المحفوظة:** المنتجات، العملاء، المستخدمين
- **المزامنة:** تلقائية كل 5 دقائق

### ⚙️ **الإعدادات:**
```
Client ID: 176747659614-0mcl1ub04jmckvqahvtrolhneh0i4f01.apps.googleusercontent.com
API Key: AIzaSyCCEM2W1qq9nVcqD8K2YvYDB6r5sWj9DW8
```

### 🔧 **الاستخدام:**
- تسجيل الدخول بحساب Google
- المزامنة التلقائية
- النسخ الاحتياطي اليومي

---

## 🟢 REST API

### 📝 **الوصف:**
تكامل مع خوادم خارجية لمزامنة البيانات مع أنظمة أخرى.

### ⚙️ **الإعداد:**
1. **فعّل REST API** في واجهة الإدارة
2. **أدخل Base URL:** `https://your-api-server.com/api`
3. **أدخل API Key:** مفتاح API الخاص بك
4. **اختبر الاتصال**

### 📡 **نقاط النهاية المدعومة:**
```
GET  /health          - فحص حالة الخادم
POST /products        - رفع المنتجات
POST /customers       - رفع العملاء
POST /users          - رفع المستخدمين
POST /sync           - مزامنة شاملة
```

### 💻 **مثال على الاستخدام:**
```javascript
// رفع المنتجات إلى REST API
await syncDataToAllAPIs('products', productsData);
```

---

## 🟡 Email API

### 📧 **الوصف:**
إرسال إشعارات بالبريد الإلكتروني للأحداث المهمة.

### ⚙️ **الإعداد (EmailJS):**
1. **إنشاء حساب:** [emailjs.com](https://emailjs.com)
2. **إنشاء Service:** Gmail, Outlook, إلخ
3. **إنشاء Template:** قالب البريد الإلكتروني
4. **نسخ البيانات:**
   - Service ID
   - Template ID  
   - Public Key

### 📨 **الإشعارات المدعومة:**
- نفاد المخزون
- إضافة منتجات جديدة
- تحديثات العملاء
- تقارير يومية

### 💻 **مثال على الاستخدام:**
```javascript
// إرسال إشعار نفاد مخزون
await sendEmailNotification({
    product_name: 'بطارية 12V',
    current_stock: 2,
    min_stock: 5,
    to_email: '<EMAIL>'
});
```

---

## 🟠 Webhooks

### 🔔 **الوصف:**
إرسال إشعارات فورية للأنظمة الخارجية عند حدوث تغييرات.

### ⚙️ **الإعداد:**
1. **إنشاء Webhook URL:** استخدم [webhook.site](https://webhook.site) للاختبار
2. **تكوين URLs:**
   - Product Updates: `https://webhook.site/your-unique-id`
   - Customer Updates: `https://webhook.site/your-unique-id`
3. **اختبار الإرسال**

### 📡 **البيانات المرسلة:**
```json
{
    "type": "productUpdate",
    "timestamp": "2024-12-15T10:30:00Z",
    "data": {
        "action": "add",
        "product": {
            "id": "123",
            "name": "بطارية 12V",
            "stock": 50
        }
    }
}
```

### 💻 **الأحداث المدعومة:**
- إضافة/تعديل/حذف منتج
- إضافة/تعديل عميل
- نفاد المخزون
- تسجيل دخول مستخدم جديد

---

## 🔄 المزامنة الشاملة

### 📊 **أنواع المزامنة:**

#### 1. **مزامنة المنتجات:**
```javascript
await syncProducts();
```

#### 2. **مزامنة العملاء:**
```javascript
await syncCustomers();
```

#### 3. **مزامنة شاملة:**
```javascript
await syncAll();
```

#### 4. **تحميل من السحابة:**
```javascript
await downloadAll();
```

### ⚡ **المزامنة التلقائية:**
- **Google Drive:** كل 5 دقائق
- **REST API:** عند كل تغيير
- **Webhooks:** فوري
- **Email:** حسب الحدث

---

## 📊 مراقبة الحالة

### 🟢 **مؤشرات الحالة:**
- **متصل:** API يعمل بشكل صحيح
- **غير متصل:** مشكلة في الاتصال أو الإعدادات

### 📝 **سجل العمليات:**
- جميع العمليات مسجلة مع الوقت
- رسائل النجاح والخطأ
- إمكانية مسح السجل

### 🔍 **اختبار الاتصال:**
كل API له زر "اختبار الاتصال" للتحقق من:
- صحة الإعدادات
- الاتصال بالخادم
- صلاحية المفاتيح

---

## 🛠️ استكشاف الأخطاء

### ❌ **مشاكل شائعة:**

#### Google Drive:
- **المشكلة:** "فشل في تسجيل الدخول"
- **الحل:** تحقق من Client ID و API Key

#### REST API:
- **المشكلة:** "خطأ 401 Unauthorized"
- **الحل:** تحقق من API Key

#### Email API:
- **المشكلة:** "فشل في الإرسال"
- **الحل:** تحقق من Service ID و Template ID

#### Webhooks:
- **المشكلة:** "لا يتم استقبال البيانات"
- **الحل:** تحقق من صحة URL

### 🔧 **أدوات التشخيص:**
1. **اختبار الاتصال** لكل API
2. **سجل العمليات** المفصل
3. **مؤشرات الحالة** الفورية

---

## 🚀 التطوير المستقبلي

### 📋 **APIs إضافية مخططة:**
- **Slack Integration** - إشعارات في Slack
- **WhatsApp Business API** - رسائل للعملاء
- **SMS API** - إشعارات نصية
- **Telegram Bot** - تقارير تلقائية

### 🔄 **تحسينات مخططة:**
- مزامنة في الوقت الفعلي
- ضغط البيانات
- تشفير متقدم
- نسخ احتياطي متعدد

---

## 📞 الدعم

### 🆘 **في حالة المشاكل:**
1. تحقق من **سجل العمليات**
2. استخدم **اختبار الاتصال**
3. راجع **إعدادات API**
4. أعد تحميل الصفحة

### 📧 **للدعم التقني:**
استخدم أدوات التشخيص المدمجة في واجهة إدارة APIs.

---

**🌟 الآن لديك نظام APIs شامل ومتطور لتطبيق النسور الماسية!**
