<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة APIs - النسور الماسية</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f6fa;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }
        .api-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .api-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #ddd;
        }
        .api-card.connected {
            border-left-color: #28a745;
        }
        .api-card.disconnected {
            border-left-color: #dc3545;
        }
        .api-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .api-title {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 18px;
            font-weight: bold;
        }
        .api-status {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .api-status.connected {
            background: #d4edda;
            color: #155724;
        }
        .api-status.disconnected {
            background: #f8d7da;
            color: #721c24;
        }
        .api-config {
            margin: 15px 0;
        }
        .config-item {
            margin: 10px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .config-item label {
            min-width: 120px;
            font-weight: 500;
        }
        .config-item input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .config-item input:focus {
            outline: none;
            border-color: #667eea;
        }
        .api-actions {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        button {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
        }
        .btn-primary {
            background: #667eea;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .sync-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .sync-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .sync-item {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 2px solid transparent;
            transition: all 0.3s;
        }
        .sync-item:hover {
            border-color: #667eea;
        }
        .sync-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            color: #667eea;
        }
        .logs-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .logs-container {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 14px;
            line-height: 1.5;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .log-success {
            background: #d4edda;
            color: #155724;
        }
        .log-error {
            background: #f8d7da;
            color: #721c24;
        }
        .log-info {
            background: #d1ecf1;
            color: #0c5460;
        }
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 50px;
            height: 24px;
        }
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 24px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider {
            background-color: #28a745;
        }
        input:checked + .slider:before {
            transform: translateX(26px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-plug"></i> إدارة تكامل APIs</h1>
            <p>إدارة وتكوين جميع الاتصالات الخارجية للتطبيق</p>
        </div>

        <!-- API Status Cards -->
        <div class="api-grid">
            <!-- Google Drive API -->
            <div class="api-card" id="googleDriveCard">
                <div class="api-header">
                    <div class="api-title">
                        <i class="fab fa-google-drive" style="color: #4285f4;"></i>
                        Google Drive API
                    </div>
                    <div class="api-status" id="googleDriveStatus">غير متصل</div>
                </div>
                <div class="api-config">
                    <div class="config-item">
                        <label class="toggle-switch">
                            <input type="checkbox" id="googleDriveEnabled" checked>
                            <span class="slider"></span>
                        </label>
                        <span>تفعيل Google Drive</span>
                    </div>
                    <div class="config-item">
                        <label>Client ID:</label>
                        <input type="text" id="googleClientId" placeholder="Google Client ID">
                    </div>
                    <div class="config-item">
                        <label>API Key:</label>
                        <input type="text" id="googleApiKey" placeholder="Google API Key">
                    </div>
                </div>
                <div class="api-actions">
                    <button class="btn-primary" onclick="console.log('Button clicked'); testGoogleDriveAPI();">
                        <i class="fas fa-vial"></i> اختبار الاتصال
                    </button>
                    <button class="btn-success" onclick="saveGoogleDriveConfig()">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                    <button class="btn-danger" onclick="fixGoogleDriveAPI()">
                        <i class="fas fa-wrench"></i> إصلاح
                    </button>
                    <button class="btn-secondary" onclick="resetGoogleDriveAPI()">
                        <i class="fas fa-redo"></i> إعادة تعيين
                    </button>
                    <button class="btn-secondary" onclick="simpleTest()" style="background: #ffc107; color: #000;">
                        <i class="fas fa-bug"></i> اختبار JS
                    </button>
                    <button class="btn-secondary" onclick="simpleGoogleDriveTest()" style="background: #17a2b8; color: #fff;">
                        <i class="fas fa-search"></i> فحص بسيط
                    </button>
                    <button class="btn-secondary" onclick="openGoogleDriveFix()" style="background: #28a745; color: #fff;">
                        <i class="fas fa-tools"></i> أداة الإصلاح
                    </button>
                </div>
            </div>

            <!-- REST API -->
            <div class="api-card" id="restApiCard">
                <div class="api-header">
                    <div class="api-title">
                        <i class="fas fa-server" style="color: #17a2b8;"></i>
                        REST API
                    </div>
                    <div class="api-status" id="restApiStatus">غير متصل</div>
                </div>
                <div class="api-config">
                    <div class="config-item">
                        <label class="toggle-switch">
                            <input type="checkbox" id="restApiEnabled">
                            <span class="slider"></span>
                        </label>
                        <span>تفعيل REST API</span>
                    </div>
                    <div class="config-item">
                        <label>Base URL:</label>
                        <input type="text" id="restApiUrl" placeholder="https://api.example.com">
                    </div>
                    <div class="config-item">
                        <label>API Key:</label>
                        <input type="text" id="restApiKey" placeholder="API Key">
                    </div>
                </div>
                <div class="api-actions">
                    <button class="btn-primary" onclick="testRestAPI()">
                        <i class="fas fa-vial"></i> اختبار الاتصال
                    </button>
                    <button class="btn-success" onclick="saveRestAPIConfig()">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                </div>
            </div>

            <!-- Email API -->
            <div class="api-card" id="emailApiCard">
                <div class="api-header">
                    <div class="api-title">
                        <i class="fas fa-envelope" style="color: #28a745;"></i>
                        Email API
                    </div>
                    <div class="api-status" id="emailStatus">غير متصل</div>
                </div>
                <div class="api-config">
                    <div class="config-item">
                        <label class="toggle-switch">
                            <input type="checkbox" id="emailEnabled">
                            <span class="slider"></span>
                        </label>
                        <span>تفعيل Email API</span>
                    </div>
                    <div class="config-item">
                        <label>Service ID:</label>
                        <input type="text" id="emailServiceId" placeholder="EmailJS Service ID">
                    </div>
                    <div class="config-item">
                        <label>Template ID:</label>
                        <input type="text" id="emailTemplateId" placeholder="Template ID">
                    </div>
                    <div class="config-item">
                        <label>Public Key:</label>
                        <input type="text" id="emailPublicKey" placeholder="Public Key">
                    </div>
                </div>
                <div class="api-actions">
                    <button class="btn-primary" onclick="testEmailAPI()">
                        <i class="fas fa-vial"></i> اختبار الإرسال
                    </button>
                    <button class="btn-success" onclick="saveEmailConfig()">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                </div>
            </div>

            <!-- Webhooks -->
            <div class="api-card" id="webhooksCard">
                <div class="api-header">
                    <div class="api-title">
                        <i class="fas fa-webhook" style="color: #ffc107;"></i>
                        Webhooks
                    </div>
                    <div class="api-status" id="webhooksStatus">غير متصل</div>
                </div>
                <div class="api-config">
                    <div class="config-item">
                        <label class="toggle-switch">
                            <input type="checkbox" id="webhooksEnabled">
                            <span class="slider"></span>
                        </label>
                        <span>تفعيل Webhooks</span>
                    </div>
                    <div class="config-item">
                        <label>Product Updates:</label>
                        <input type="text" id="webhookProducts" placeholder="https://webhook.site/...">
                    </div>
                    <div class="config-item">
                        <label>Customer Updates:</label>
                        <input type="text" id="webhookCustomers" placeholder="https://webhook.site/...">
                    </div>
                </div>
                <div class="api-actions">
                    <button class="btn-primary" onclick="testWebhooks()">
                        <i class="fas fa-vial"></i> اختبار Webhook
                    </button>
                    <button class="btn-success" onclick="saveWebhooksConfig()">
                        <i class="fas fa-save"></i> حفظ
                    </button>
                </div>
            </div>
        </div>

        <!-- Data Sync Section -->
        <div class="sync-section">
            <h2><i class="fas fa-sync-alt"></i> مزامنة البيانات</h2>
            <p>مزامنة البيانات مع جميع APIs المتصلة</p>
            
            <div class="sync-grid">
                <div class="sync-item" onclick="syncProducts()">
                    <div class="sync-icon"><i class="fas fa-box"></i></div>
                    <h4>مزامنة المنتجات</h4>
                    <p>رفع بيانات المنتجات</p>
                </div>
                <div class="sync-item" onclick="syncCustomers()">
                    <div class="sync-icon"><i class="fas fa-users"></i></div>
                    <h4>مزامنة العملاء</h4>
                    <p>رفع بيانات العملاء</p>
                </div>
                <div class="sync-item" onclick="syncAll()">
                    <div class="sync-icon"><i class="fas fa-cloud-upload-alt"></i></div>
                    <h4>مزامنة شاملة</h4>
                    <p>رفع جميع البيانات</p>
                </div>
                <div class="sync-item" onclick="downloadAll()">
                    <div class="sync-icon"><i class="fas fa-cloud-download-alt"></i></div>
                    <h4>تحميل البيانات</h4>
                    <p>تحميل من السحابة</p>
                </div>
            </div>
        </div>

        <!-- API Logs -->
        <div class="logs-section">
            <h2><i class="fas fa-list-alt"></i> سجل العمليات</h2>
            <div class="api-actions" style="margin-bottom: 15px;">
                <button class="btn-secondary" onclick="clearLogs()">
                    <i class="fas fa-trash"></i> مسح السجل
                </button>
                <button class="btn-primary" onclick="refreshLogs()">
                    <i class="fas fa-refresh"></i> تحديث
                </button>
            </div>
            <div class="logs-container" id="apiLogs">
                <div class="log-entry log-info">[معلومات] تم تحميل نظام إدارة APIs</div>
            </div>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button class="btn-primary" onclick="goToMainApp()" style="padding: 15px 30px; font-size: 16px;">
                <i class="fas fa-arrow-right"></i> العودة إلى التطبيق الرئيسي
            </button>
        </div>
    </div>

    <!-- Load API Integration System -->
    <script src="api-integration.js"></script>
    
    <script>
        console.log('🔧 تحميل واجهة إدارة APIs...');

        // Simple test function to verify JavaScript is working
        function simpleTest() {
            console.log('✅ JavaScript يعمل بشكل صحيح');
            alert('✅ JavaScript يعمل بشكل صحيح');
        }

        // Simple Google Drive test function
        function simpleGoogleDriveTest() {
            console.log('🔍 بدء اختبار Google Drive بسيط...');

            let report = '🔍 تقرير اختبار Google Drive:\n\n';

            // Check if gapi is loaded
            if (typeof gapi !== 'undefined') {
                report += '✅ Google API محمل\n';

                // Check auth2
                if (gapi.auth2) {
                    report += '✅ Google Auth2 متاح\n';

                    const authInstance = gapi.auth2.getAuthInstance();
                    if (authInstance) {
                        report += '✅ Auth Instance متاح\n';

                        if (authInstance.isSignedIn.get()) {
                            const user = authInstance.currentUser.get();
                            const profile = user.getBasicProfile();
                            report += `✅ مسجل دخول: ${profile.getName()}\n`;
                            report += `📧 البريد: ${profile.getEmail()}\n`;
                        } else {
                            report += '⚠️ غير مسجل دخول\n';
                        }
                    } else {
                        report += '❌ Auth Instance غير متاح\n';
                    }
                } else {
                    report += '❌ Google Auth2 غير متاح\n';
                }
            } else {
                report += '❌ Google API غير محمل\n';
            }

            // Check window variables
            report += '\n🔍 متغيرات النافذة:\n';
            report += `isGoogleDriveConnected: ${window.isGoogleDriveConnected || 'غير محدد'}\n`;
            report += `isGoogleDriveReady: ${window.isGoogleDriveReady || 'غير محدد'}\n`;
            report += `googleDriveUser: ${window.googleDriveUser ? 'موجود' : 'غير موجود'}\n`;

            // Check functions
            report += '\n🔍 الوظائف المتاحة:\n';
            report += `connectToGoogleDrive: ${typeof connectToGoogleDrive !== 'undefined' ? 'متاح' : 'غير متاح'}\n`;
            report += `initializeGoogleDriveSync: ${typeof initializeGoogleDriveSync !== 'undefined' ? 'متاح' : 'غير متاح'}\n`;

            console.log(report);
            alert(report);

            // Try to add to log if available
            if (typeof addLog === 'function') {
                addLog('تم إجراء اختبار Google Drive البسيط', 'info');
            }
        }

        // Make functions available globally
        window.simpleTest = simpleTest;
        window.simpleGoogleDriveTest = simpleGoogleDriveTest;

        // Load API configurations from localStorage
        function loadAPIConfigurations() {
            try {
                const savedConfig = localStorage.getItem('apiConfig');
                let config = {};

                if (savedConfig) {
                    config = JSON.parse(savedConfig);
                }

                // Load Google Drive config with defaults from existing system
                const googleEnabled = config.googleDrive?.enabled !== false; // Default to true
                const googleClientId = config.googleDrive?.clientId ||
                                     (typeof GOOGLE_DRIVE_CONFIG !== 'undefined' ? GOOGLE_DRIVE_CONFIG.CLIENT_ID : '') ||
                                     '176747659614-0mcl1ub04jmckvqahvtrolhneh0i4f01.apps.googleusercontent.com';
                const googleApiKey = config.googleDrive?.apiKey ||
                                   (typeof GOOGLE_DRIVE_CONFIG !== 'undefined' ? GOOGLE_DRIVE_CONFIG.API_KEY : '') ||
                                   'AIzaSyCCEM2W1qq9nVcqD8K2YvYDB6r5sWj9DW8';

                document.getElementById('googleDriveEnabled').checked = googleEnabled;
                document.getElementById('googleClientId').value = googleClientId;
                document.getElementById('googleApiKey').value = googleApiKey;

                // Load REST API config
                document.getElementById('restApiEnabled').checked = config.restApi?.enabled || false;
                document.getElementById('restApiUrl').value = config.restApi?.baseUrl || '';
                document.getElementById('restApiKey').value = config.restApi?.apiKey || '';

                // Load Email config
                document.getElementById('emailEnabled').checked = config.email?.enabled || false;
                document.getElementById('emailServiceId').value = config.email?.serviceId || '';
                document.getElementById('emailTemplateId').value = config.email?.templateId || '';
                document.getElementById('emailPublicKey').value = config.email?.publicKey || '';

                // Load Webhooks config
                document.getElementById('webhooksEnabled').checked = config.webhooks?.enabled || false;
                document.getElementById('webhookProducts').value = config.webhooks?.urls?.productUpdate || '';
                document.getElementById('webhookCustomers').value = config.webhooks?.urls?.customerUpdate || '';

                addLog('✅ تم تحميل إعدادات APIs', 'success');
            } catch (error) {
                console.error('خطأ في تحميل إعدادات API:', error);
                addLog(`❌ خطأ في تحميل الإعدادات: ${error.message}`, 'error');
            }
        }

        // Update API status display
        function updateAPIStatusDisplay() {
            // Check Google Drive status specifically
            checkGoogleDriveConnectionStatus();

            if (typeof apiStatus !== 'undefined') {
                // Update Google Drive status
                updateStatusCard('googleDrive', apiStatus.googleDrive);
                updateStatusCard('restApi', apiStatus.restApi);
                updateStatusCard('email', apiStatus.email);
                updateStatusCard('webhooks', apiStatus.webhooks);
            }
        }

        // Check Google Drive connection status
        function checkGoogleDriveConnectionStatus() {
            try {
                let isConnected = false;

                // Check multiple indicators
                if (window.isGoogleDriveConnected) {
                    isConnected = true;
                    addLog('✅ Google Drive متصل (window.isGoogleDriveConnected)', 'success');
                } else if (window.isGoogleDriveReady) {
                    isConnected = true;
                    addLog('✅ Google Drive جاهز (window.isGoogleDriveReady)', 'success');
                } else if (typeof gapi !== 'undefined' && gapi.auth2) {
                    const authInstance = gapi.auth2.getAuthInstance();
                    if (authInstance && authInstance.isSignedIn.get()) {
                        isConnected = true;
                        addLog('✅ Google Drive متصل (gapi.auth2)', 'success');
                    }
                }

                if (!isConnected) {
                    addLog('⚠️ Google Drive غير متصل', 'info');
                }

                // Update global status
                if (typeof apiStatus !== 'undefined') {
                    apiStatus.googleDrive = isConnected;
                }

                return isConnected;
            } catch (error) {
                addLog(`❌ خطأ في فحص Google Drive: ${error.message}`, 'error');
                return false;
            }
        }

        // Update individual status card
        function updateStatusCard(apiName, isConnected) {
            const card = document.getElementById(`${apiName}Card`);
            const status = document.getElementById(`${apiName}Status`);

            if (card && status) {
                card.className = `api-card ${isConnected ? 'connected' : 'disconnected'}`;
                status.className = `api-status ${isConnected ? 'connected' : 'disconnected'}`;
                status.textContent = isConnected ? 'متصل' : 'غير متصل';
            }
        }

        // Helper function for logging
        function logMessage(message, type = 'info') {
            console.log(message);
            if (typeof addLog === 'function') {
                addLog(message, type);
            }
        }

        // Test Google Drive API
        async function testGoogleDriveAPI() {
            console.log('🔍 تم الضغط على زر اختبار Google Drive API');
            logMessage('🔍 بدء اختبار Google Drive API...', 'info');

            try {
                // Step 1: Check if Google API is loaded
                if (typeof gapi === 'undefined') {
                    logMessage('❌ Google API غير محمل', 'error');
                    logMessage('💡 تحقق من تحميل google-drive-sync.js', 'info');
                    updateStatusCard('googleDrive', false);
                    return;
                }

                logMessage('✅ Google API محمل', 'success');

                // Step 2: Check if auth2 is available
                if (!gapi.auth2) {
                    logMessage('⚠️ Google Auth2 غير مهيأ - محاولة التهيئة...', 'info');

                    // Try to initialize
                    if (typeof initializeGoogleDrive === 'function') {
                        await initializeGoogleDrive();
                        logMessage('✅ تم تهيئة Google Drive', 'success');
                    } else if (typeof initializeGoogleDriveSync === 'function') {
                        await initializeGoogleDriveSync();
                        logMessage('✅ تم تهيئة Google Drive Sync', 'success');
                    } else {
                        logMessage('❌ وظائف التهيئة غير متاحة', 'error');
                        updateStatusCard('googleDrive', false);
                        return;
                    }
                }

                // Step 3: Check auth instance
                const authInstance = gapi.auth2.getAuthInstance();
                if (!authInstance) {
                    logMessage('❌ Google Auth Instance غير متاح', 'error');
                    updateStatusCard('googleDrive', false);
                    return;
                }

                logMessage('✅ Google Auth Instance متاح', 'success');

                // Step 4: Check if signed in
                if (authInstance.isSignedIn.get()) {
                    logMessage('✅ المستخدم مسجل دخول في Google Drive', 'success');
                    const user = authInstance.currentUser.get();
                    const profile = user.getBasicProfile();
                    logMessage(`👤 المستخدم: ${profile.getName()} (${profile.getEmail()})`, 'info');
                    updateStatusCard('googleDrive', true);
                } else {
                    logMessage('⚠️ المستخدم غير مسجل دخول - محاولة تسجيل الدخول...', 'info');

                    // Try to sign in
                    if (typeof connectToGoogleDrive === 'function') {
                        const result = await connectToGoogleDrive();
                        if (result) {
                            logMessage('✅ تم تسجيل الدخول بنجاح', 'success');
                            updateStatusCard('googleDrive', true);
                        } else {
                            logMessage('❌ فشل في تسجيل الدخول', 'error');
                            updateStatusCard('googleDrive', false);
                        }
                    } else {
                        logMessage('❌ وظيفة تسجيل الدخول غير متاحة', 'error');
                        updateStatusCard('googleDrive', false);
                    }
                }

                // Final status check
                checkGoogleDriveConnectionStatus();

            } catch (error) {
                logMessage(`❌ خطأ في اختبار Google Drive: ${error.message}`, 'error');
                updateStatusCard('googleDrive', false);
            }
        }

        // Test REST API
        async function testRestAPI() {
            const baseUrl = document.getElementById('restApiUrl').value;
            const apiKey = document.getElementById('restApiKey').value;

            if (!baseUrl || !apiKey) {
                addLog('⚠️ يرجى إدخال URL و API Key أولاً', 'error');
                return;
            }

            addLog('جاري اختبار REST API...', 'info');

            try {
                const response = await fetch(`${baseUrl}/health`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    addLog('✅ REST API يعمل بشكل صحيح', 'success');
                    updateStatusCard('restApi', true);
                } else {
                    addLog(`❌ REST API خطأ: ${response.status}`, 'error');
                    updateStatusCard('restApi', false);
                }
            } catch (error) {
                addLog(`❌ خطأ في REST API: ${error.message}`, 'error');
                updateStatusCard('restApi', false);
            }
        }

        // Test Email API
        async function testEmailAPI() {
            const serviceId = document.getElementById('emailServiceId').value;
            const templateId = document.getElementById('emailTemplateId').value;
            const publicKey = document.getElementById('emailPublicKey').value;

            if (!serviceId || !templateId || !publicKey) {
                addLog('⚠️ يرجى إدخال جميع بيانات Email API أولاً', 'error');
                return;
            }

            addLog('جاري اختبار Email API...', 'info');

            try {
                // This would require EmailJS to be loaded
                addLog('✅ تم حفظ إعدادات Email API (اختبار الإرسال يتطلب EmailJS)', 'success');
                updateStatusCard('email', true);
            } catch (error) {
                addLog(`❌ خطأ في Email API: ${error.message}`, 'error');
                updateStatusCard('email', false);
            }
        }

        // Test Webhooks
        async function testWebhooks() {
            const productUrl = document.getElementById('webhookProducts').value;
            const customerUrl = document.getElementById('webhookCustomers').value;

            if (!productUrl && !customerUrl) {
                addLog('⚠️ يرجى إدخال URL واحد على الأقل للاختبار', 'error');
                return;
            }

            addLog('جاري اختبار Webhooks...', 'info');

            try {
                const testData = {
                    type: 'test',
                    timestamp: new Date().toISOString(),
                    message: 'اختبار Webhook من تطبيق النسور الماسية'
                };

                if (productUrl) {
                    const response = await fetch(productUrl, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(testData)
                    });

                    if (response.ok) {
                        addLog('✅ Product Webhook يعمل بشكل صحيح', 'success');
                    } else {
                        addLog(`❌ Product Webhook خطأ: ${response.status}`, 'error');
                    }
                }

                updateStatusCard('webhooks', true);
            } catch (error) {
                addLog(`❌ خطأ في Webhooks: ${error.message}`, 'error');
                updateStatusCard('webhooks', false);
            }
        }

        // Save API configurations
        function saveGoogleDriveConfig() {
            const config = {
                enabled: document.getElementById('googleDriveEnabled').checked,
                clientId: document.getElementById('googleClientId').value,
                apiKey: document.getElementById('googleApiKey').value
            };

            saveAPIConfig('googleDrive', config);
            addLog('✅ تم حفظ إعدادات Google Drive', 'success');
        }

        function saveRestAPIConfig() {
            const config = {
                enabled: document.getElementById('restApiEnabled').checked,
                baseUrl: document.getElementById('restApiUrl').value,
                apiKey: document.getElementById('restApiKey').value
            };

            saveAPIConfig('restApi', config);
            addLog('✅ تم حفظ إعدادات REST API', 'success');
        }

        function saveEmailConfig() {
            const config = {
                enabled: document.getElementById('emailEnabled').checked,
                serviceId: document.getElementById('emailServiceId').value,
                templateId: document.getElementById('emailTemplateId').value,
                publicKey: document.getElementById('emailPublicKey').value
            };

            saveAPIConfig('email', config);
            addLog('✅ تم حفظ إعدادات Email API', 'success');
        }

        function saveWebhooksConfig() {
            const config = {
                enabled: document.getElementById('webhooksEnabled').checked,
                urls: {
                    productUpdate: document.getElementById('webhookProducts').value,
                    customerUpdate: document.getElementById('webhookCustomers').value
                }
            };

            saveAPIConfig('webhooks', config);
            addLog('✅ تم حفظ إعدادات Webhooks', 'success');
        }

        // Save API config to localStorage
        function saveAPIConfig(apiName, config) {
            try {
                let savedConfig = {};
                const existing = localStorage.getItem('apiConfig');
                if (existing) {
                    savedConfig = JSON.parse(existing);
                }

                savedConfig[apiName] = config;
                localStorage.setItem('apiConfig', JSON.stringify(savedConfig));

                // Update global API config if available
                if (typeof updateAPIConfig === 'function') {
                    updateAPIConfig(savedConfig);
                }
            } catch (error) {
                console.error('خطأ في حفظ إعدادات API:', error);
            }
        }

        // Data sync functions
        async function syncProducts() {
            addLog('🔄 بدء مزامنة المنتجات...', 'info');

            try {
                const products = JSON.parse(localStorage.getItem('products') || '[]');

                if (typeof syncDataToAllAPIs === 'function') {
                    const results = await syncDataToAllAPIs('products', products);

                    let successCount = 0;
                    Object.keys(results).forEach(api => {
                        if (results[api]) {
                            successCount++;
                            addLog(`✅ تم رفع المنتجات إلى ${api}`, 'success');
                        } else {
                            addLog(`❌ فشل في رفع المنتجات إلى ${api}`, 'error');
                        }
                    });

                    addLog(`📊 تم مزامنة المنتجات مع ${successCount} من APIs`, 'info');
                } else {
                    addLog('⚠️ نظام المزامنة غير متاح', 'error');
                }
            } catch (error) {
                addLog(`❌ خطأ في مزامنة المنتجات: ${error.message}`, 'error');
            }
        }

        async function syncCustomers() {
            addLog('🔄 بدء مزامنة العملاء...', 'info');

            try {
                const customers = JSON.parse(localStorage.getItem('customers') || '[]');

                if (typeof syncDataToAllAPIs === 'function') {
                    const results = await syncDataToAllAPIs('customers', customers);

                    let successCount = 0;
                    Object.keys(results).forEach(api => {
                        if (results[api]) {
                            successCount++;
                            addLog(`✅ تم رفع العملاء إلى ${api}`, 'success');
                        } else {
                            addLog(`❌ فشل في رفع العملاء إلى ${api}`, 'error');
                        }
                    });

                    addLog(`📊 تم مزامنة العملاء مع ${successCount} من APIs`, 'info');
                } else {
                    addLog('⚠️ نظام المزامنة غير متاح', 'error');
                }
            } catch (error) {
                addLog(`❌ خطأ في مزامنة العملاء: ${error.message}`, 'error');
            }
        }

        async function syncAll() {
            addLog('🚀 بدء المزامنة الشاملة...', 'info');
            await syncProducts();
            await syncCustomers();
            addLog('✅ تم الانتهاء من المزامنة الشاملة', 'success');
        }

        async function downloadAll() {
            addLog('📥 بدء تحميل البيانات من السحابة...', 'info');

            try {
                if (typeof downloadAllFromGoogleDrive === 'function') {
                    await downloadAllFromGoogleDrive();
                    addLog('✅ تم تحميل البيانات من Google Drive', 'success');
                } else {
                    addLog('⚠️ وظيفة التحميل غير متاحة', 'error');
                }
            } catch (error) {
                addLog(`❌ خطأ في تحميل البيانات: ${error.message}`, 'error');
            }
        }

        // Logging functions
        function addLog(message, type = 'info') {
            const logsContainer = document.getElementById('apiLogs');
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${type}`;

            const timestamp = new Date().toLocaleTimeString();
            logEntry.textContent = `[${timestamp}] ${message}`;

            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;

            // Keep only last 100 log entries
            while (logsContainer.children.length > 100) {
                logsContainer.removeChild(logsContainer.firstChild);
            }
        }

        function clearLogs() {
            document.getElementById('apiLogs').innerHTML = '';
            addLog('تم مسح السجل', 'info');
        }

        function refreshLogs() {
            addLog('تم تحديث السجل', 'info');
            updateAPIStatusDisplay();
        }

        // Fix Google Drive API
        async function fixGoogleDriveAPI() {
            addLog('🔧 بدء إصلاح Google Drive API...', 'info');

            try {
                // Step 1: Check if fix functions are available
                if (typeof fixGoogleDriveConnection === 'function') {
                    addLog('🔄 استخدام وظيفة الإصلاح المدمجة...', 'info');
                    await fixGoogleDriveConnection();
                    addLog('✅ تم تشغيل وظيفة الإصلاح', 'success');
                } else if (typeof emergencyFixGoogleLogin === 'function') {
                    addLog('🚨 استخدام الإصلاح الطارئ...', 'info');
                    emergencyFixGoogleLogin();
                    addLog('✅ تم تشغيل الإصلاح الطارئ', 'success');
                } else {
                    addLog('⚠️ وظائف الإصلاح غير متاحة - إصلاح يدوي...', 'info');

                    // Manual fix
                    window.isGoogleDriveConnected = false;
                    window.isGoogleDriveReady = false;

                    // Try to reload Google Drive system
                    if (typeof initializeGoogleDrive === 'function') {
                        await initializeGoogleDrive();
                        addLog('✅ تم إعادة تهيئة Google Drive', 'success');
                    }
                }

                // Test after fix
                setTimeout(() => {
                    testGoogleDriveAPI();
                }, 2000);

            } catch (error) {
                addLog(`❌ خطأ في إصلاح Google Drive: ${error.message}`, 'error');
            }
        }

        // Reset Google Drive API
        function resetGoogleDriveAPI() {
            if (confirm('هل أنت متأكد من إعادة تعيين Google Drive API؟\nسيتم قطع الاتصال وإعادة التهيئة.')) {
                addLog('🔄 إعادة تعيين Google Drive API...', 'info');

                try {
                    // Reset all Google Drive states
                    window.isGoogleDriveConnected = false;
                    window.isGoogleDriveReady = false;
                    window.googleDriveUser = null;

                    // Sign out if signed in
                    if (typeof gapi !== 'undefined' && gapi.auth2) {
                        const authInstance = gapi.auth2.getAuthInstance();
                        if (authInstance && authInstance.isSignedIn.get()) {
                            authInstance.signOut();
                            addLog('🚪 تم تسجيل الخروج من Google', 'info');
                        }
                    }

                    // Update UI
                    updateStatusCard('googleDrive', false);
                    addLog('✅ تم إعادة تعيين Google Drive API', 'success');
                    addLog('💡 استخدم "اختبار الاتصال" لإعادة التهيئة', 'info');

                } catch (error) {
                    addLog(`❌ خطأ في إعادة التعيين: ${error.message}`, 'error');
                }
            }
        }

        function openGoogleDriveFix() {
            window.open('google-drive-fix.html', '_blank');
        }

        function goToMainApp() {
            window.location.href = 'index.html';
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🚀 بدء تحميل واجهة إدارة APIs...', 'info');

            // Load configurations first
            loadAPIConfigurations();

            // Wait a bit for other scripts to load, then check status
            setTimeout(() => {
                addLog('🔍 فحص حالة APIs...', 'info');
                updateAPIStatusDisplay();

                // Check Google Drive specifically
                if (typeof checkGoogleDriveStatus === 'function') {
                    checkGoogleDriveStatus();
                }

                addLog('✅ تم تحميل واجهة إدارة APIs بنجاح', 'success');
            }, 2000);

            // Auto-refresh status every 30 seconds
            setInterval(() => {
                updateAPIStatusDisplay();
            }, 30000);
        });
    </script>
</body>
</html>
