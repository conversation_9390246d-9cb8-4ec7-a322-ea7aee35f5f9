<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إصلاح أسماء localStorage</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            margin: 20px;
            background: #f0f2f5;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 25px;
            margin: 10px 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover { background: #45a049; }
        .btn.danger { background: #f44336; }
        .btn.danger:hover { background: #da190b; }
        .btn.warning { background: #ff9800; }
        .btn.warning:hover { background: #e68900; }
        
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        #log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        .data-table th, .data-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: right;
        }
        .data-table th {
            background: #f2f2f2;
            font-weight: bold;
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h1>🔧 إصلاح أسماء localStorage</h1>
        
        <div class="section">
            <h3>⚠️ المشكلة المكتشفة</h3>
            <p>هناك تضارب في أسماء localStorage المستخدمة في التطبيق:</p>
            <ul>
                <li><strong>المنتجات:</strong> أحياناً <code>products</code> وأحياناً <code>inventory_products</code></li>
                <li><strong>العملاء:</strong> أحياناً <code>customers</code> وأحياناً <code>inventory_customers</code></li>
            </ul>
            <p>هذا يسبب عدم تزامن البيانات بين أجزاء التطبيق المختلفة.</p>
        </div>

        <div class="section">
            <h3>🔧 أدوات الإصلاح</h3>
            <button class="btn" onclick="checkCurrentData()">🔍 فحص البيانات الحالية</button>
            <button class="btn warning" onclick="migrateToStandardNames()">🔄 توحيد الأسماء</button>
            <button class="btn" onclick="testAfterMigration()">✅ اختبار بعد الإصلاح</button>
            <button class="btn danger" onclick="clearAllLocalStorage()">🗑️ مسح جميع البيانات المحلية</button>
        </div>

        <div class="section">
            <h3>📊 حالة البيانات الحالية</h3>
            <div id="current-data"></div>
        </div>

        <div class="section">
            <h3>📋 سجل العمليات</h3>
            <div id="log"></div>
        </div>
    </div>

    <script>
        const logElement = document.getElementById('log');
        
        function addToLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const logMessage = `[${timestamp}] ${message}\n`;
            logElement.textContent += logMessage;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        // Check current data in localStorage
        function checkCurrentData() {
            addToLog('🔍 فحص البيانات الحالية في localStorage...');
            
            const currentDataDiv = document.getElementById('current-data');
            
            // Check all possible localStorage keys
            const keys = [
                'products', 'inventory_products',
                'customers', 'inventory_customers',
                'systemUsers', 'systemSettings', 'loginCredentials'
            ];
            
            let html = '<table class="data-table"><tr><th>المفتاح</th><th>موجود</th><th>العدد/الحجم</th></tr>';
            
            keys.forEach(key => {
                const data = localStorage.getItem(key);
                let count = 'غير موجود';
                let exists = 'لا';
                
                if (data) {
                    exists = 'نعم';
                    try {
                        const parsed = JSON.parse(data);
                        if (Array.isArray(parsed)) {
                            count = `${parsed.length} عنصر`;
                        } else if (typeof parsed === 'object') {
                            count = `${Object.keys(parsed).length} مفتاح`;
                        } else {
                            count = 'بيانات نصية';
                        }
                    } catch (error) {
                        count = 'بيانات غير صالحة';
                    }
                }
                
                html += `<tr><td>${key}</td><td>${exists}</td><td>${count}</td></tr>`;
                
                if (data) {
                    addToLog(`✅ ${key}: ${count}`);
                } else {
                    addToLog(`❌ ${key}: غير موجود`);
                }
            });
            
            html += '</table>';
            currentDataDiv.innerHTML = html;
            
            addToLog('✅ انتهى فحص البيانات الحالية');
        }

        // Migrate to standard names
        function migrateToStandardNames() {
            addToLog('🔄 بدء توحيد أسماء localStorage...');
            
            try {
                // Migrate products: products -> inventory_products
                const oldProducts = localStorage.getItem('products');
                const newProducts = localStorage.getItem('inventory_products');
                
                if (oldProducts && !newProducts) {
                    localStorage.setItem('inventory_products', oldProducts);
                    localStorage.removeItem('products');
                    addToLog('✅ تم نقل المنتجات من products إلى inventory_products');
                } else if (oldProducts && newProducts) {
                    // Both exist, merge them
                    try {
                        const oldData = JSON.parse(oldProducts);
                        const newData = JSON.parse(newProducts);
                        
                        if (oldData.length > newData.length) {
                            localStorage.setItem('inventory_products', oldProducts);
                            addToLog('✅ تم استخدام البيانات الأكبر من products');
                        }
                        localStorage.removeItem('products');
                        addToLog('✅ تم حذف products المكرر');
                    } catch (error) {
                        addToLog('⚠️ خطأ في دمج بيانات المنتجات: ' + error.message);
                    }
                } else if (!oldProducts && !newProducts) {
                    addToLog('ℹ️ لا توجد بيانات منتجات للنقل');
                } else {
                    addToLog('ℹ️ inventory_products موجود بالفعل');
                }

                // Migrate customers: customers -> inventory_customers
                const oldCustomers = localStorage.getItem('customers');
                const newCustomers = localStorage.getItem('inventory_customers');
                
                if (oldCustomers && !newCustomers) {
                    localStorage.setItem('inventory_customers', oldCustomers);
                    localStorage.removeItem('customers');
                    addToLog('✅ تم نقل العملاء من customers إلى inventory_customers');
                } else if (oldCustomers && newCustomers) {
                    // Both exist, merge them
                    try {
                        const oldData = JSON.parse(oldCustomers);
                        const newData = JSON.parse(newCustomers);
                        
                        if (oldData.length > newData.length) {
                            localStorage.setItem('inventory_customers', oldCustomers);
                            addToLog('✅ تم استخدام البيانات الأكبر من customers');
                        }
                        localStorage.removeItem('customers');
                        addToLog('✅ تم حذف customers المكرر');
                    } catch (error) {
                        addToLog('⚠️ خطأ في دمج بيانات العملاء: ' + error.message);
                    }
                } else if (!oldCustomers && !newCustomers) {
                    addToLog('ℹ️ لا توجد بيانات عملاء للنقل');
                } else {
                    addToLog('ℹ️ inventory_customers موجود بالفعل');
                }

                addToLog('✅ انتهى توحيد أسماء localStorage');
                
                // Refresh data display
                checkCurrentData();
                
            } catch (error) {
                addToLog('❌ خطأ في توحيد الأسماء: ' + error.message);
            }
        }

        // Test after migration
        function testAfterMigration() {
            addToLog('✅ اختبار البيانات بعد الإصلاح...');
            
            try {
                // Check that we only have the correct keys
                const products = localStorage.getItem('inventory_products');
                const customers = localStorage.getItem('inventory_customers');
                const users = localStorage.getItem('systemUsers');
                
                // Check that old keys are gone
                const oldProducts = localStorage.getItem('products');
                const oldCustomers = localStorage.getItem('customers');
                
                if (oldProducts) {
                    addToLog('⚠️ تحذير: products ما زال موجود');
                } else {
                    addToLog('✅ products تم حذفه بنجاح');
                }
                
                if (oldCustomers) {
                    addToLog('⚠️ تحذير: customers ما زال موجود');
                } else {
                    addToLog('✅ customers تم حذفه بنجاح');
                }
                
                if (products) {
                    const productsData = JSON.parse(products);
                    addToLog(`✅ inventory_products: ${productsData.length} منتج`);
                } else {
                    addToLog('ℹ️ inventory_products: فارغ');
                }
                
                if (customers) {
                    const customersData = JSON.parse(customers);
                    addToLog(`✅ inventory_customers: ${customersData.length} عميل`);
                } else {
                    addToLog('ℹ️ inventory_customers: فارغ');
                }
                
                if (users) {
                    const usersData = JSON.parse(users);
                    addToLog(`✅ systemUsers: ${usersData.length} مستخدم`);
                } else {
                    addToLog('ℹ️ systemUsers: فارغ');
                }
                
                addToLog('✅ انتهى الاختبار - الأسماء موحدة الآن');
                
            } catch (error) {
                addToLog('❌ خطأ في الاختبار: ' + error.message);
            }
        }

        // Clear all localStorage
        function clearAllLocalStorage() {
            if (confirm('هل أنت متأكد من مسح جميع البيانات المحلية؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                addToLog('🗑️ مسح جميع البيانات المحلية...');
                
                const keys = [
                    'products', 'inventory_products',
                    'customers', 'inventory_customers',
                    'systemUsers', 'systemSettings', 'loginCredentials'
                ];
                
                keys.forEach(key => {
                    if (localStorage.getItem(key)) {
                        localStorage.removeItem(key);
                        addToLog(`🗑️ تم حذف ${key}`);
                    }
                });
                
                addToLog('✅ تم مسح جميع البيانات المحلية');
                checkCurrentData();
            }
        }

        // Auto-check on load
        window.addEventListener('load', function() {
            setTimeout(checkCurrentData, 500);
        });
    </script>
</body>
</html>
