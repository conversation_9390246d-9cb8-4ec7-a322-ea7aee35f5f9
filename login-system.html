<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - شركة النسور الماسية للتجارة</title>

    <!-- Force desktop mode on mobile -->
    <script>
        // Force desktop viewport on mobile devices
        if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
            console.log('📱 تم اكتشاف جهاز محمول - تفعيل وضع سطح المكتب');

            // Update viewport for desktop mode
            const viewport = document.querySelector('meta[name="viewport"]');
            if (viewport) {
                viewport.setAttribute('content', 'width=1200, initial-scale=0.8, user-scalable=yes');
            }

            // Add desktop mode class to body
            document.addEventListener('DOMContentLoaded', function() {
                document.body.classList.add('desktop-mode-mobile');
                console.log('🖥️ تم تفعيل وضع سطح المكتب للهاتف');
            });
        }
    </script>
    
    <!-- Fonts & Icons -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            width: 100%;
            max-width: 400px;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }

        .login-header .logo {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
        }

        .login-header h1 {
            font-size: 1.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .login-header p {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .login-form {
            padding: 40px 30px;
        }

        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 0.9rem;
        }

        .form-group input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-group input:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .password-toggle {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
            transition: all 0.3s ease;
            z-index: 10;
            font-size: 16px;
        }

        .password-toggle:hover {
            color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .password-toggle:focus {
            outline: none;
            color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .form-group i {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            font-size: 1.1rem;
        }

        .form-group input {
            padding-left: 50px;
        }

        .login-btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .login-btn:active {
            transform: translateY(0);
        }

        .error-message {
            background: #fee;
            color: #c33;
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #c33;
            font-size: 0.9rem;
            display: none;
        }

        .success-message {
            background: #efe;
            color: #363;
            padding: 12px 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #363;
            font-size: 0.9rem;
            display: none;
        }

        .forgot-password {
            text-align: center;
            margin-top: 20px;
        }

        .forgot-password a {
            color: #667eea;
            text-decoration: none;
            font-size: 0.9rem;
        }

        .forgot-password a:hover {
            text-decoration: underline;
        }



        .divider {
            position: relative;
            margin: 20px 0;
            text-align: center;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e1e5e9;
        }

        .divider span {
            background: white;
            padding: 0 15px;
            color: #999;
            font-size: 0.9rem;
        }


        }

        .loading {
            display: none;
            text-align: center;
            margin-top: 20px;
        }

        .loading i {
            animation: spin 1s linear infinite;
            color: #667eea;
            font-size: 1.5rem;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .login-container {
                margin: 0;
                border-radius: 15px;
                max-width: none;
                width: 100%;
            }

            .login-header {
                padding: 25px 20px;
            }

            .login-form {
                padding: 25px 20px;
            }

            .login-header h1 {
                font-size: 1.4rem;
                line-height: 1.3;
            }

            .login-header p {
                font-size: 0.85rem;
            }

            .form-group input {
                padding: 16px 50px 16px 20px;
                font-size: 16px; /* Prevents zoom on iOS */
            }

            .login-btn {
                padding: 16px;
                font-size: 1rem;
            }
        }

        @media (max-width: 480px) {
            .login-header .logo {
                width: 60px;
                height: 60px;
                font-size: 1.5rem;
            }

            .login-header h1 {
                font-size: 1.2rem;
            }

            .login-header p {
                font-size: 0.8rem;
            }

            .form-group input {
                padding: 14px 45px 14px 18px;
            }

            .form-group i {
                left: 12px;
                font-size: 1rem;
            }
        }

        /* Touch-friendly improvements */
        @media (hover: none) and (pointer: coarse) {
            .login-btn {
                min-height: 48px;
            }

            .form-group input {
                min-height: 48px;
            }

            .forgot-password a {
                padding: 10px;
                display: inline-block;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="logo">
                <i class="fas fa-gem"></i>
            </div>
            <h1>شركة النسور الماسية للتجارة</h1>
            <p>نظام إدارة مخزون بطاريات الدواجن</p>
        </div>
        
        <div class="login-form">
            <div class="error-message" id="errorMessage">
                <i class="fas fa-exclamation-triangle"></i>
                <span id="errorText"></span>
            </div>
            
            <div class="success-message" id="successMessage">
                <i class="fas fa-check-circle"></i>
                <span id="successText"></span>
            </div>
            
            <form id="loginForm">
                <div class="form-group">
                    <label for="email">البريد الإلكتروني</label>
                    <div style="position: relative;">
                        <i class="fas fa-envelope"></i>
                        <input type="email" id="email" name="email" placeholder="أدخل البريد الإلكتروني" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password">كلمة المرور</label>
                    <div style="position: relative;">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="password" name="password" placeholder="أدخل كلمة المرور" required>
                        <button type="button" class="password-toggle" onclick="togglePassword()" title="إظهار/إخفاء كلمة المرور">
                            <i class="fas fa-eye" id="passwordToggleIcon"></i>
                        </button>
                    </div>
                </div>
                
                <button type="submit" class="login-btn" id="loginBtn">
                    <i class="fas fa-sign-in-alt"></i>
                    تسجيل الدخول
                </button>
            </form>



            <div class="loading" id="loading">
                <i class="fas fa-spinner"></i>
                <p>جاري التحقق من البيانات...</p>
            </div>

            <div class="forgot-password">
                <a href="#" onclick="showForgotPassword()" style="color: #667eea; text-decoration: none; font-weight: bold;">
                    <i class="fas fa-key"></i> نسيت كلمة المرور؟
                </a>
                <br><br>
                <a href="reset-password.html" style="color: #28a745; text-decoration: none; font-weight: bold; display: inline-block; padding: 8px 16px; border: 2px solid #28a745; border-radius: 5px; transition: all 0.3s ease;"
                   onmouseover="this.style.background='#28a745'; this.style.color='white';"
                   onmouseout="this.style.background='transparent'; this.style.color='#28a745';">
                    <i class="fas fa-envelope"></i> استرداد كلمة المرور عبر البريد
                </a>
            </div>


        </div>
    </div>

    <!-- Load User Management System -->
    <script src="user-management.js"></script>

    <script>
        // Default login credentials
        const DEFAULT_CREDENTIALS = {
            email: '<EMAIL>',
            password: '2030'
        };

        // Load saved credentials from localStorage
        function loadSavedCredentials() {
            const saved = localStorage.getItem('loginCredentials');
            if (saved) {
                try {
                    return JSON.parse(saved);
                } catch (error) {
                    console.error('خطأ في قراءة بيانات الدخول المحفوظة:', error);
                }
            }
            return DEFAULT_CREDENTIALS;
        }

        // Show error message
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            const successDiv = document.getElementById('successMessage');
            
            successDiv.style.display = 'none';
            errorText.textContent = message;
            errorDiv.style.display = 'block';
            
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 5000);
        }

        // Show success message
        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            const successText = document.getElementById('successText');
            const errorDiv = document.getElementById('errorMessage');
            
            errorDiv.style.display = 'none';
            successText.textContent = message;
            successDiv.style.display = 'block';
        }

        // Show loading state
        function showLoading(show) {
            const loading = document.getElementById('loading');
            const loginBtn = document.getElementById('loginBtn');
            const form = document.getElementById('loginForm');
            
            if (show) {
                loading.style.display = 'block';
                loginBtn.style.display = 'none';
                form.style.opacity = '0.7';
            } else {
                loading.style.display = 'none';
                loginBtn.style.display = 'block';
                form.style.opacity = '1';
            }
        }

        // Handle login form submission
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const email = document.getElementById('email').value.trim();
            const password = document.getElementById('password').value;

            if (!email || !password) {
                showError('يرجى إدخال البريد الإلكتروني وكلمة المرور');
                return;
            }

            showLoading(true);
            console.log('🔐 محاولة تسجيل الدخول في login-system...');
            console.log('📧 البريد:', email);

            // Simulate loading delay and ensure user system is ready
            setTimeout(async () => {
                try {
                    // Always ensure default admin exists first
                    ensureDefaultAdminExists();

                    // Try multiple authentication methods
                    let authResult = null;

                    // Method 1: Use userManager if available
                    if (typeof userManager !== 'undefined') {
                        console.log('✅ استخدام نظام إدارة المستخدمين');
                        authResult = await userManager.authenticateUser(email, password);
                    }

                    // Method 2: Direct localStorage check if userManager failed
                    if (!authResult || !authResult.success) {
                        console.log('🔄 محاولة المصادقة المباشرة...');
                        authResult = await authenticateDirectly(email, password);
                    }

                    // Method 3: Check against hardcoded credentials as fallback
                    if (!authResult || !authResult.success) {
                        console.log('🔄 فحص البيانات الافتراضية...');
                        if (email.toLowerCase() === '<EMAIL>' && password === '2030') {
                            authResult = {
                                success: true,
                                user: {
                                    id: 'admin_default',
                                    name: 'المدير الرئيسي',
                                    email: '<EMAIL>',
                                    role: 'admin',
                                    permissions: [],
                                    isActive: true
                                }
                            };
                            console.log('✅ تم تسجيل الدخول بالبيانات الافتراضية');
                        }
                    }

                    if (authResult && authResult.success) {
                        console.log('✅ تم تسجيل الدخول بنجاح:', authResult.user.name);
                        showSuccess(`مرحباً ${authResult.user.name}، تم تسجيل الدخول بنجاح`);

                        // Save login session
                        localStorage.setItem('isLoggedIn', 'true');
                        localStorage.setItem('currentUser', JSON.stringify(authResult.user));
                        localStorage.setItem('loginTime', new Date().toISOString());

                        // Update last login in systemUsers if exists
                        updateUserLastLogin(authResult.user.email);

                        // Set flag for single page load sync only
                        localStorage.setItem('syncOnLogin', 'true');

                        // Remove continuous sync flags
                        localStorage.removeItem('triggerImmediateSync');
                        localStorage.removeItem('enableAutoSync');

                        console.log('📄 تم تعيين المزامنة السحابية عند تحميل الصفحة فقط');

                        // Sync data from Firebase after successful login
                        syncDataAfterLogin().then(() => {
                            console.log('✅ تمت المزامنة بنجاح، الانتقال للتطبيق...');
                            // Redirect to main app after sync
                            setTimeout(() => {
                                window.location.href = 'index.html';
                            }, 1000);
                        }).catch((error) => {
                            console.warn('⚠️ فشل في مزامنة البيانات، سيتم الانتقال للتطبيق:', error);
                            // Redirect anyway even if sync fails
                            setTimeout(() => {
                                window.location.href = 'index.html';
                            }, 1000);
                        });
                    } else {
                        console.log('❌ فشل في تسجيل الدخول');
                        showLoading(false);
                        showError('البريد الإلكتروني أو كلمة المرور غير صحيحة');


                    }
                } catch (error) {
                    console.error('❌ خطأ في تسجيل الدخول:', error);
                    showLoading(false);
                    showError('حدث خطأ في تسجيل الدخول');
                }
            }, 1500);
        });

        // Ensure default admin exists
        function ensureDefaultAdminExists() {
            try {
                const systemUsersData = localStorage.getItem('systemUsers');
                let systemUsers = [];

                if (systemUsersData) {
                    try {
                        systemUsers = JSON.parse(systemUsersData);
                    } catch (error) {
                        console.log('⚠️ بيانات المستخدمين تالفة - إعادة إنشاء');
                        systemUsers = [];
                    }
                }

                // Check if default admin exists
                const defaultAdmin = systemUsers.find(u =>
                    u.email === '<EMAIL>' && u.isActive
                );

                if (!defaultAdmin) {
                    console.log('⚠️ المدير الافتراضي غير موجود - إنشاء جديد...');
                    const newAdmin = {
                        id: 'admin_' + Date.now(),
                        name: 'المدير الرئيسي',
                        email: '<EMAIL>',
                        password: '2030',
                        role: 'admin',
                        permissions: [],
                        isActive: true,
                        createdAt: new Date().toISOString(),
                        lastLogin: null
                    };

                    systemUsers.push(newAdmin);
                    localStorage.setItem('systemUsers', JSON.stringify(systemUsers));
                    console.log('✅ تم إنشاء المدير الافتراضي');
                }
            } catch (error) {
                console.error('❌ خطأ في ضمان وجود المدير الافتراضي:', error);
            }
        }

        // Direct authentication method with Firebase sync
        async function authenticateDirectly(email, password) {
            try {
                console.log('🔐 بدء المصادقة للمستخدم:', email);

                // First, try to sync users from Firebase
                await syncUsersFromFirebase();

                const systemUsersData = localStorage.getItem('systemUsers');
                if (!systemUsersData) {
                    console.log('⚠️ لا توجد بيانات مستخدمين محلية');
                    return { success: false, error: 'لا توجد بيانات مستخدمين' };
                }

                const systemUsers = JSON.parse(systemUsersData);
                console.log(`🔍 البحث عن المستخدم في ${systemUsers.length} مستخدم محفوظ`);

                const user = systemUsers.find(u =>
                    u.email === email.toLowerCase().trim() &&
                    u.password === password.trim() &&
                    u.isActive
                );

                if (user) {
                    console.log('✅ تم العثور على المستخدم:', user.name);
                    return { success: true, user: user };
                } else {
                    console.log('❌ لم يتم العثور على المستخدم أو بيانات غير صحيحة');

                    // Log available users for debugging (without passwords)
                    const availableUsers = systemUsers.map(u => ({
                        email: u.email,
                        name: u.name,
                        isActive: u.isActive
                    }));
                    console.log('👥 المستخدمون المتاحون:', availableUsers);

                    return { success: false, error: 'بيانات غير صحيحة' };
                }
            } catch (error) {
                console.error('❌ خطأ في المصادقة المباشرة:', error);
                return { success: false, error: 'خطأ في النظام' };
            }
        }

        // Sync users from Firebase before authentication
        async function syncUsersFromFirebase() {
            console.log('👥 مزامنة المستخدمين من Firebase...');

            try {
                // Check if Firebase is available
                if (typeof window.firebaseService === 'undefined' || !window.firebaseService.isFirebaseReady()) {
                    console.log('⚠️ Firebase غير متاح، استخدام البيانات المحلية');
                    return false;
                }

                // Show loading message
                showLoading(true);
                const statusElement = document.querySelector('.loading-text');
                if (statusElement) {
                    statusElement.textContent = 'جاري تحميل المستخدمين من السحابة...';
                }

                // Load users from Firebase
                const firebaseUsers = await window.firebaseService.loadUsers();

                if (firebaseUsers && firebaseUsers.length > 0) {
                    console.log(`📥 تم تحميل ${firebaseUsers.length} مستخدم من Firebase`);

                    // Update local storage with Firebase users
                    localStorage.setItem('systemUsers', JSON.stringify(firebaseUsers));

                    // Log all loaded users for debugging
                    console.log('👥 المستخدمون المحملون من Firebase:');
                    firebaseUsers.forEach((user, index) => {
                        console.log(`  ${index + 1}. ${user.name} (${user.email}) - نشط: ${user.isActive}`);
                    });

                    // Show success message
                    console.log('✅ تم تحديث المستخدمين من Firebase');

                    if (statusElement) {
                        statusElement.textContent = `تم تحميل ${firebaseUsers.length} مستخدم من السحابة`;
                    }

                    return true;
                } else {
                    console.log('ℹ️ لا توجد مستخدمين في Firebase، استخدام البيانات المحلية');

                    if (statusElement) {
                        statusElement.textContent = 'لا توجد مستخدمين في السحابة، استخدام البيانات المحلية';
                    }

                    return false;
                }

            } catch (error) {
                console.error('❌ خطأ في مزامنة المستخدمين من Firebase:', error);

                const statusElement = document.querySelector('.loading-text');
                if (statusElement) {
                    statusElement.textContent = 'خطأ في تحميل المستخدمين من السحابة';
                }

                return false;
            }
        }

        // Update user last login
        function updateUserLastLogin(email) {
            try {
                const systemUsersData = localStorage.getItem('systemUsers');
                if (systemUsersData) {
                    const systemUsers = JSON.parse(systemUsersData);
                    const userIndex = systemUsers.findIndex(u => u.email === email);

                    if (userIndex !== -1) {
                        systemUsers[userIndex].lastLogin = new Date().toISOString();
                        localStorage.setItem('systemUsers', JSON.stringify(systemUsers));
                    }
                }
            } catch (error) {
                console.error('❌ خطأ في تحديث آخر تسجيل دخول:', error);
            }
        }





        // Show forgot password options
        function showForgotPassword() {
            // Create modal for password reset options
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            `;

            const modalContent = document.createElement('div');
            modalContent.style.cssText = `
                background: white;
                padding: 30px;
                border-radius: 15px;
                max-width: 400px;
                width: 90%;
                text-align: center;
                box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            `;

            modalContent.innerHTML = `
                <h3 style="color: #333; margin-bottom: 20px;">
                    <i class="fas fa-key"></i> استرداد كلمة المرور
                </h3>
                <p style="color: #666; margin-bottom: 25px; line-height: 1.5;">
                    اختر طريقة استرداد كلمة المرور:
                </p>

                <button onclick="goToEmailReset()" style="
                    width: 100%;
                    padding: 15px;
                    background: linear-gradient(135deg, #28a745, #20c997);
                    color: white;
                    border: none;
                    border-radius: 8px;
                    font-size: 16px;
                    font-weight: bold;
                    cursor: pointer;
                    margin-bottom: 15px;
                    transition: all 0.3s ease;
                " onmouseover="this.style.transform='translateY(-2px)'" onmouseout="this.style.transform='translateY(0)'">
                    <i class="fas fa-envelope"></i> استرداد عبر البريد الإلكتروني
                </button>



                <button onclick="closeModal()" style="
                    width: 100%;
                    padding: 12px;
                    background: transparent;
                    color: #666;
                    border: 1px solid #dee2e6;
                    border-radius: 8px;
                    font-size: 14px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                " onmouseover="this.style.background='#f8f9fa'" onmouseout="this.style.background='transparent'">
                    <i class="fas fa-times"></i> إغلاق
                </button>
            `;

            modal.appendChild(modalContent);
            document.body.appendChild(modal);

            // Close modal when clicking outside
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeModal();
                }
            });

            // Global functions for modal buttons
            window.goToEmailReset = function() {
                window.location.href = 'reset-password.html';
            };

            window.closeModal = function() {
                if (modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
            };
        }

        // Check if already logged in
        window.addEventListener('load', function() {
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            if (isLoggedIn === 'true') {
                // Check if login is still valid (24 hours)
                const loginTime = localStorage.getItem('loginTime');
                if (loginTime) {
                    const loginDate = new Date(loginTime);
                    const now = new Date();
                    const hoursDiff = (now - loginDate) / (1000 * 60 * 60);
                    
                    if (hoursDiff < 24) {
                        // Still logged in, redirect to main app
                        window.location.href = 'index.html';
                        return;
                    }
                }
                
                // Login expired, clear session
                localStorage.removeItem('isLoggedIn');
                localStorage.removeItem('currentUser');
                localStorage.removeItem('loginTime');
            }
        });

        // Function to sync data from Firebase after login
        async function syncDataAfterLogin() {
            console.log('🔄 بدء مزامنة البيانات بعد تسجيل الدخول...');

            try {
                // Check if Firebase is available and ready
                if (typeof window.firebaseService === 'undefined') {
                    console.log('⚠️ Firebase service not available, skipping sync');
                    return;
                }

                if (!window.firebaseService.isFirebaseReady()) {
                    console.log('⚠️ Firebase not ready, skipping sync');
                    return;
                }

                console.log('✅ Firebase ready, starting data sync...');

                // Show sync message to user
                const statusElement = document.querySelector('.status-message');
                if (statusElement) {
                    statusElement.textContent = 'جاري مزامنة البيانات...';
                    statusElement.className = 'status-message info';
                }

                // Sync all data from Firebase
                const syncResult = await window.firebaseService.syncAllFromFirebase();

                if (syncResult) {
                    console.log('✅ تم تحميل البيانات من Firebase بنجاح');

                    // Update status message
                    if (statusElement) {
                        statusElement.textContent = 'تم تحميل البيانات بنجاح - جاري تحديث الواجهة...';
                        statusElement.className = 'status-message success';
                    }

                    // Force UI refresh after successful sync
                    setTimeout(() => {
                        if (typeof window.forceUIRefresh === 'function') {
                            window.forceUIRefresh();
                            console.log('✅ تم تحديث الواجهة بعد تسجيل الدخول');
                        }

                        // Also load logo from Firebase
                        if (typeof loadLogoFromFirebase === 'function') {
                            loadLogoFromFirebase();
                        }
                    }, 500);
                } else {
                    console.log('⚠️ لم يتم العثور على بيانات في Firebase أو فشل التحميل');

                    // Check if we have local data to upload
                    const localProducts = JSON.parse(localStorage.getItem('inventory_products') || '[]');
                    const localCustomers = JSON.parse(localStorage.getItem('inventory_customers') || '[]');

                    if (localProducts.length > 0 || localCustomers.length > 0) {
                        console.log('📤 رفع البيانات المحلية إلى Firebase...');
                        await window.firebaseService.syncAllToFirebase();
                        console.log('✅ تم رفع البيانات المحلية إلى Firebase');
                    }
                }

                // Small delay to show the success message
                await new Promise(resolve => setTimeout(resolve, 500));

            } catch (error) {
                console.error('❌ خطأ في مزامنة البيانات:', error);

                // Update status message
                const statusElement = document.querySelector('.status-message');
                if (statusElement) {
                    statusElement.textContent = 'تحذير: فشل في مزامنة البيانات';
                    statusElement.className = 'status-message warning';
                }

                // Don't throw error, just log it
                throw error;
            }
        }





        function showDefaultCredentials() {
            alert('🔑 بيانات تسجيل الدخول الافتراضية:\n\nالبريد الإلكتروني: <EMAIL>\nكلمة المرور: 2030');
        }

        // Toggle password visibility
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('passwordToggleIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
                console.log('👁️ تم إظهار كلمة المرور');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
                console.log('🙈 تم إخفاء كلمة المرور');
            }
        }

        // Initialize login system
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔐 تهيئة نظام تسجيل الدخول...');

            // Initialize login system with user sync
            initializeLoginSystem();
        });

        // Initialize login system with user sync
        async function initializeLoginSystem() {
            try {
                // Show initial loading
                showLoading(true);
                const statusElement = document.querySelector('.loading-text');
                if (statusElement) {
                    statusElement.textContent = 'جاري تهيئة نظام تسجيل الدخول...';
                }

                // Wait for Firebase to be ready
                await waitForFirebaseReady();

                // Sync users from Firebase first
                console.log('🔄 تحميل المستخدمين من Firebase...');
                if (statusElement) {
                    statusElement.textContent = 'جاري تحميل المستخدمين من السحابة...';
                }

                await syncUsersFromFirebase();

                // Ensure default admin exists
                ensureDefaultAdminExists();

                // Check if already logged in
                checkExistingLogin();

                // Setup form validation
                setupFormValidation();

                // Hide loading
                showLoading(false);

                console.log('✅ تم تهيئة نظام تسجيل الدخول مع تحميل المستخدمين');

            } catch (error) {
                console.error('❌ خطأ في تهيئة نظام تسجيل الدخول:', error);

                // Fallback: ensure default admin and continue
                ensureDefaultAdminExists();
                checkExistingLogin();
                setupFormValidation();
                showLoading(false);
            }
        }

        // Wait for Firebase to be ready
        async function waitForFirebaseReady() {
            return new Promise((resolve) => {
                let attempts = 0;
                const maxAttempts = 20;

                const checkFirebase = () => {
                    attempts++;

                    if (window.firebaseService && window.firebaseService.isFirebaseReady()) {
                        console.log('✅ Firebase جاهز لتحميل المستخدمين');
                        resolve();
                    } else if (attempts >= maxAttempts) {
                        console.log('⚠️ Firebase غير متاح، سيتم استخدام البيانات المحلية');
                        resolve();
                    } else {
                        setTimeout(checkFirebase, 500);
                    }
                };

                checkFirebase();
            });
        }

        // Check existing login
        function checkExistingLogin() {
            const isLoggedIn = localStorage.getItem('isLoggedIn');
            if (isLoggedIn === 'true') {
                console.log('👤 المستخدم مسجل دخول مسبقاً، إعادة توجيه...');
                window.location.href = 'index.html';
            }
        }

        // Setup form validation
        function setupFormValidation() {
            const form = document.getElementById('loginForm');
            const emailInput = document.getElementById('email');
            const passwordInput = document.getElementById('password');

            if (emailInput) {
                emailInput.addEventListener('input', function() {
                    this.setCustomValidity('');
                });
            }

            if (passwordInput) {
                passwordInput.addEventListener('input', function() {
                    this.setCustomValidity('');
                });
            }
        }

    </script>
</body>
</html>
