# 💾 دليل حفظ البيانات في تطبيق النسور الماسية

## 🎯 **نظرة عامة:**
يستخدم تطبيق النسور الماسية نظام تخزين مزدوج لضمان أمان البيانات وإمكانية الوصول إليها:

## 📍 **أماكن حفظ البيانات:**

### **1. التخزين المحلي (localStorage)** 💻
```
📁 متصفح الويب → localStorage
├── 📄 inventory_products (بيانات المنتجات)
├── 📄 inventory_customers (بيانات العملاء)
├── 📄 systemSettings (إعدادات النظام)
├── 📄 systemUsers (بيانات المستخدمين)
├── 📄 isLoggedIn (حالة تسجيل الدخول)
├── 📄 currentUser (المستخدم الحالي)
├── 📄 loginCredentials (بيانات الدخول)
├── 📄 autoSyncEnabled (إعدادات المزامنة)
├── 📄 lastGoogleDriveSync (آخر مزامنة)
└── 📄 usedActivationCodes (أكواد التفعيل المستخدمة)
```

### **2. التخزين السحابي (Google Drive)** ☁️
```
📁 Google Drive → مجلد "النسور الماسية"
├── 📄 products_data.json (نسخة سحابية من المنتجات)
└── 📄 customers_data.json (نسخة سحابية من العملاء)
```

## 🔄 **كيف يعمل نظام التخزين:**

### **التخزين المحلي (localStorage):**
```javascript
// حفظ المنتجات محلياً
localStorage.setItem('inventory_products', JSON.stringify(products));

// حفظ العملاء محلياً
localStorage.setItem('inventory_customers', JSON.stringify(customers));

// حفظ الإعدادات محلياً
localStorage.setItem('systemSettings', JSON.stringify(settings));

// حفظ المستخدمين محلياً
localStorage.setItem('systemUsers', JSON.stringify(systemUsers));
```

### **التخزين السحابي (Google Drive):**
```javascript
// رفع المنتجات للسحابة
await uploadProductsToDrive();

// رفع العملاء للسحابة
await uploadCustomersToDrive();

// تحميل من السحابة
await downloadProductsFromDrive();
await downloadCustomersFromDrive();
```

## 📊 **تفاصيل البيانات المحفوظة:**

### **1. بيانات المنتجات (Products):**
```json
{
  "id": "unique_id",
  "manufacturer": "اسم الشركة المصنعة",
  "category": "انتاج/تربية",
  "floors": 4,
  "lines": 2,
  "nestsPerLine": 10,
  "nestWidth": 60,
  "nestDepth": 40,
  "totalNests": 160,
  "birdsPerNest": 6,
  "totalCapacity": 960,
  "status": "متاح/محجوز",
  "country": "البلد",
  "specifications": ["مواصفة 1", "مواصفة 2"],
  "warranty": ["ضمان 1", "ضمان 2"],
  "financialOffer": ["عرض 1", "عرض 2"],
  "includedItems": ["عنصر 1", "عنصر 2"],
  "excludedItems": ["عنصر 1", "عنصر 2"],
  "paymentMethod": ["طريقة 1", "طريقة 2"],
  "offerValidity": ["صالح لمدة 30 يوم"],
  "images": ["base64_image_1", "base64_image_2"],
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

### **2. بيانات العملاء (Customers):**
```json
{
  "id": "unique_id",
  "name": "اسم العميل",
  "email": "<EMAIL>",
  "phone": "+966501234567",
  "whatsapp": "+966501234567",
  "country": "السعودية",
  "city": "الرياض",
  "type": "عميل عادي/عميل محتمل",
  "requestType": "انتاج/تربية",
  "capacity": 1000,
  "notes": "ملاحظات",
  "createdAt": "2024-01-01T00:00:00.000Z",
  "updatedAt": "2024-01-01T00:00:00.000Z"
}
```

### **3. إعدادات النظام (System Settings):**
```json
{
  "company": {
    "name": "شركة النسور الماسية للتجارة",
    "commercialRegister": "1234567890",
    "taxNumber": "123456789012345",
    "phone": "+966501234567",
    "whatsapp": "+966501234567",
    "email": "<EMAIL>",
    "website": "www.company.com",
    "address": "العنوان",
    "country": "السعودية"
  },
  "financial": {
    "taxRate": 15,
    "currency": "SAR"
  },
  "theme": {
    "colorTheme": "default"
  },
  "dateFormat": "gregorian",
  "logo": {
    "name": "logo.png",
    "data": "base64_image_data"
  },
  "userManagement": {
    "adminEmail": "<EMAIL>",
    "adminPassword": "2030",
    "users": []
  }
}
```

### **4. بيانات المستخدمين (System Users):**
```json
[
  {
    "id": "unique_id",
    "name": "اسم المستخدم",
    "email": "<EMAIL>",
    "password": "hashed_password",
    "role": "admin/user",
    "permissions": {
      "dashboard": true,
      "products": true,
      "customers": true,
      "requests": true,
      "settings": true
    },
    "isActive": true,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "lastLogin": "2024-01-01T00:00:00.000Z"
  }
]
```

## 🔄 **نظام المزامنة التلقائية:**

### **كيف يعمل:**
```
1. البيانات تُحفظ محلياً أولاً (فوري)
2. كل 30 ثانية يتم رفعها للسحابة (تلقائي)
3. عند فتح التطبيق يتم تحميل أحدث البيانات من السحابة
4. في حالة عدم وجود اتصال، يعمل التطبيق محلياً
5. عند عودة الاتصال، تتم المزامنة تلقائياً
```

### **إعدادات المزامنة:**
```javascript
const AUTO_SYNC_CONFIG = {
    enabled: true,
    interval: 30000, // 30 ثانية
    onDataChange: true, // مزامنة عند تغيير البيانات
    showNotifications: true,
    maxRetries: 3,
    cloudOnly: true, // التركيز على التخزين السحابي
    autoSaveToCloud: true // حفظ تلقائي للسحابة
};
```

## 📁 **مواقع الملفات في Google Drive:**

### **هيكل المجلد:**
```
📁 Google Drive
└── 📁 النسور الماسية (مجلد التطبيق)
    ├── 📄 products_data.json
    │   ├── تاريخ الإنشاء: عند أول رفع
    │   ├── تاريخ التحديث: عند كل مزامنة
    │   └── الحجم: حسب عدد المنتجات
    │
    └── 📄 customers_data.json
        ├── تاريخ الإنشاء: عند أول رفع
        ├── تاريخ التحديث: عند كل مزامنة
        └── الحجم: حسب عدد العملاء
```

## 🔒 **أمان البيانات:**

### **التخزين المحلي:**
- محفوظ في متصفح المستخدم فقط
- لا يمكن الوصول إليه من متصفحات أخرى
- يُمسح عند مسح بيانات المتصفح

### **التخزين السحابي:**
- محمي بحساب Google الشخصي
- مشفر أثناء النقل والتخزين
- يمكن الوصول إليه من أي جهاز بنفس الحساب

## 🛠️ **إدارة البيانات:**

### **النسخ الاحتياطي:**
```
1. تلقائي: كل 30 ثانية إلى Google Drive
2. يدوي: زر "رفع جميع البيانات"
3. تصدير: زر "تصدير جميع البيانات" (ملف JSON)
```

### **الاستعادة:**
```
1. تلقائي: عند فتح التطبيق من Google Drive
2. يدوي: زر "تحميل جميع البيانات"
3. استيراد: رفع ملف JSON محفوظ مسبقاً
```

### **المزامنة بين الأجهزة:**
```
1. الجهاز A يضيف منتج → يُحفظ محلياً
2. خلال 30 ثانية → يُرفع لـ Google Drive
3. الجهاز B يفتح التطبيق → يحمل البيانات الجديدة
4. جميع الأجهزة تصبح متزامنة
```

## 📊 **مراقبة البيانات:**

### **في Developer Tools → Console:**
```
✅ تم حفظ المنتجات محلياً: 15 منتج
📤 رفع المنتجات إلى Google Drive...
✅ تم رفع المنتجات بنجاح
📥 تحميل البيانات من Google Drive...
✅ تم تحميل 15 منتج من Google Drive
🔄 آخر مزامنة: 2024-01-01 12:30:45
```

### **في واجهة التطبيق:**
```
⚙️ الإعدادات → نظام التخزين السحابي
├── حالة الاتصال: متصل ✅
├── آخر مزامنة: منذ دقيقتين
├── المزامنة التلقائية: مفعلة ✅
└── أزرار: رفع | تحميل | فحص الاتصال
```

## 🎯 **الخلاصة:**

### **البيانات محفوظة في:**
1. **localStorage** - للوصول السريع والعمل بدون إنترنت
2. **Google Drive** - للنسخ الاحتياطي والمزامنة بين الأجهزة

### **المزايا:**
- ✅ **أمان مضاعف** - نسختان من البيانات
- ✅ **عمل بدون إنترنت** - البيانات متاحة محلياً
- ✅ **مزامنة تلقائية** - تحديث مستمر للسحابة
- ✅ **وصول من أي مكان** - عبر Google Drive
- ✅ **نسخ احتياطية** - حماية من فقدان البيانات

**🌟 بياناتك محفوظة ومحمية في مكانين آمنين!**
