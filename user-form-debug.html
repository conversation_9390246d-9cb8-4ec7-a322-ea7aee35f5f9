<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 تشخيص نموذج إضافة المستخدم</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #dc3545, #c82333);
            border-radius: 10px;
        }
        .btn {
            padding: 15px 25px;
            margin: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn.primary { background: #007bff; color: white; }
        .btn.success { background: #28a745; color: white; }
        .btn.warning { background: #ffc107; color: #212529; }
        .btn.danger { background: #dc3545; color: white; }
        .btn:hover { transform: translateY(-2px); }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        .log {
            background: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
        }
        .test-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 تشخيص نموذج إضافة المستخدم</h1>
            <p>حل مشكلة عدم استجابة زر إضافة المستخدم</p>
        </div>

        <!-- Current Status -->
        <div class="test-section">
            <h3>📊 الحالة الحالية:</h3>
            <div id="currentStatus">جاري الفحص...</div>
        </div>

        <!-- Actions -->
        <div style="text-align: center; margin: 30px 0;">
            <button class="btn primary" onclick="openUserModal()">
                👤 فتح نموذج إضافة المستخدم
            </button>
            <button class="btn success" onclick="debugForm()">
                🔍 تشخيص النموذج
            </button>
            <button class="btn warning" onclick="testFormSubmission()">
                🧪 اختبار إرسال النموذج
            </button>
            <button class="btn danger" onclick="clearLog()">
                🗑️ مسح السجل
            </button>
        </div>

        <!-- Log -->
        <div class="log" id="log">
            <div>[INFO] مرحباً بك في أداة تشخيص نموذج إضافة المستخدم</div>
        </div>
    </div>

    <!-- Load required scripts -->
    <script src="user-management.js"></script>

    <script>
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 بدء تشخيص نموذج إضافة المستخدم...', 'info');
            setTimeout(() => {
                checkStatus();
            }, 1000);
        });

        // Open user modal
        function openUserModal() {
            log('👤 فتح نموذج إضافة المستخدم...', 'info');
            
            try {
                if (typeof showAddUserModal === 'function') {
                    showAddUserModal();
                    log('✅ تم فتح النموذج بنجاح', 'success');
                    
                    setTimeout(() => {
                        debugForm();
                    }, 500);
                } else {
                    log('❌ وظيفة showAddUserModal غير متاحة', 'error');
                }
            } catch (error) {
                log(`❌ خطأ في فتح النموذج: ${error.message}`, 'error');
            }
        }

        // Debug form
        function debugForm() {
            log('🔍 تشخيص النموذج...', 'info');
            
            const form = document.getElementById('addUserForm');
            if (!form) {
                log('❌ النموذج غير موجود', 'error');
                updateStatus('❌ النموذج غير موجود - افتح النموذج أولاً', 'error');
                return;
            }
            
            log('✅ النموذج موجود', 'success');
            
            // Check form attributes
            log(`📋 onsubmit: ${form.getAttribute('onsubmit')}`, 'info');
            log(`📋 action: ${form.action || 'غير محدد'}`, 'info');
            log(`📋 method: ${form.method || 'غير محدد'}`, 'info');
            
            // Check form elements
            const elements = ['userName', 'userEmail', 'userPassword', 'userRole', 'customPermissions'];
            let allElementsFound = true;
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    log(`✅ ${id}: موجود (${element.type || element.tagName})`, 'success');
                } else {
                    log(`❌ ${id}: غير موجود`, 'error');
                    allElementsFound = false;
                }
            });
            
            // Check custom permissions container
            const customContainer = document.getElementById('customPermissionsContainer');
            if (customContainer) {
                const permissionCheckboxes = customContainer.querySelectorAll('input[name="permissions"]');
                log(`✅ customPermissionsContainer: موجود مع ${permissionCheckboxes.length} صلاحية`, 'success');
            } else {
                log('❌ customPermissionsContainer: غير موجود', 'error');
                allElementsFound = false;
            }
            
            // Check submit button
            const submitButton = form.querySelector('button[type="submit"]');
            if (submitButton) {
                log('✅ زر الإرسال: موجود', 'success');
                log(`📋 نص الزر: "${submitButton.textContent.trim()}"`, 'info');
            } else {
                log('❌ زر الإرسال: غير موجود', 'error');
                allElementsFound = false;
            }
            
            // Check addNewUser function
            if (typeof addNewUser === 'function') {
                log('✅ وظيفة addNewUser: متاحة', 'success');
            } else if (typeof window.addNewUser === 'function') {
                log('✅ وظيفة window.addNewUser: متاحة', 'success');
            } else {
                log('❌ وظيفة addNewUser: غير متاحة', 'error');
                allElementsFound = false;
            }
            
            // Overall status
            if (allElementsFound) {
                updateStatus('✅ جميع العناصر موجودة - النموذج جاهز', 'success');
            } else {
                updateStatus('❌ بعض العناصر مفقودة - يحتاج إصلاح', 'error');
            }
        }

        // Test form submission
        function testFormSubmission() {
            log('🧪 اختبار إرسال النموذج...', 'info');
            
            const form = document.getElementById('addUserForm');
            if (!form) {
                log('❌ النموذج غير موجود - افتح النموذج أولاً', 'error');
                return;
            }
            
            // Fill form with test data
            const testData = {
                userName: 'مستخدم تجريبي ' + Date.now(),
                userEmail: 'test' + Date.now() + '@example.com',
                userPassword: 'test123',
                userRole: 'employee'
            };
            
            log('📝 ملء النموذج ببيانات تجريبية...', 'info');
            
            Object.keys(testData).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.value = testData[key];
                    log(`✅ ${key}: ${testData[key]}`, 'success');
                } else {
                    log(`❌ ${key}: العنصر غير موجود`, 'error');
                }
            });
            
            // Test custom permissions
            const customPermissions = document.getElementById('customPermissions');
            if (customPermissions) {
                customPermissions.checked = true;
                log('✅ تم تفعيل الصلاحيات المخصصة', 'success');
                
                // Trigger toggle function
                if (typeof toggleCustomPermissions === 'function') {
                    toggleCustomPermissions();
                    log('✅ تم استدعاء toggleCustomPermissions', 'success');
                } else if (typeof window.toggleCustomPermissions === 'function') {
                    window.toggleCustomPermissions();
                    log('✅ تم استدعاء window.toggleCustomPermissions', 'success');
                }
                
                // Select some permissions
                setTimeout(() => {
                    const permissionCheckboxes = document.querySelectorAll('input[name="permissions"]');
                    if (permissionCheckboxes.length > 0) {
                        permissionCheckboxes[0].checked = true;
                        if (permissionCheckboxes.length > 1) {
                            permissionCheckboxes[1].checked = true;
                        }
                        log(`✅ تم تحديد ${Math.min(2, permissionCheckboxes.length)} صلاحية`, 'success');
                    }
                    
                    // Now try to submit
                    setTimeout(() => {
                        log('🚀 محاولة إرسال النموذج...', 'info');
                        
                        try {
                            // Method 1: Direct function call
                            if (typeof addNewUser === 'function') {
                                log('📞 استدعاء addNewUser مباشرة...', 'info');
                                const fakeEvent = {
                                    preventDefault: () => log('✅ تم منع السلوك الافتراضي', 'info'),
                                    target: form
                                };
                                addNewUser(fakeEvent);
                            } else if (typeof window.addNewUser === 'function') {
                                log('📞 استدعاء window.addNewUser...', 'info');
                                const fakeEvent = {
                                    preventDefault: () => log('✅ تم منع السلوك الافتراضي', 'info'),
                                    target: form
                                };
                                window.addNewUser(fakeEvent);
                            } else {
                                log('❌ وظيفة addNewUser غير متاحة', 'error');
                            }
                        } catch (error) {
                            log(`❌ خطأ في إرسال النموذج: ${error.message}`, 'error');
                        }
                    }, 500);
                }, 500);
            }
        }

        // Check status
        function checkStatus() {
            log('📊 فحص الحالة العامة...', 'info');
            
            const status = document.getElementById('currentStatus');
            let statusHtml = '';
            
            // Check if user management is loaded
            if (typeof showAddUserModal === 'function') {
                statusHtml += '<div class="status success">✅ user-management.js محمل</div>';
            } else {
                statusHtml += '<div class="status error">❌ user-management.js غير محمل</div>';
            }
            
            // Check if addNewUser function exists
            if (typeof addNewUser === 'function') {
                statusHtml += '<div class="status success">✅ وظيفة addNewUser متاحة</div>';
            } else if (typeof window.addNewUser === 'function') {
                statusHtml += '<div class="status success">✅ وظيفة window.addNewUser متاحة</div>';
            } else {
                statusHtml += '<div class="status error">❌ وظيفة addNewUser غير متاحة</div>';
            }
            
            // Check if toggleCustomPermissions function exists
            if (typeof toggleCustomPermissions === 'function') {
                statusHtml += '<div class="status success">✅ وظيفة toggleCustomPermissions متاحة</div>';
            } else if (typeof window.toggleCustomPermissions === 'function') {
                statusHtml += '<div class="status success">✅ وظيفة window.toggleCustomPermissions متاحة</div>';
            } else {
                statusHtml += '<div class="status error">❌ وظيفة toggleCustomPermissions غير متاحة</div>';
            }
            
            // Check if form exists
            const form = document.getElementById('addUserForm');
            if (form) {
                statusHtml += '<div class="status success">✅ النموذج موجود</div>';
            } else {
                statusHtml += '<div class="status warning">⚠️ النموذج غير موجود (لم يتم فتحه بعد)</div>';
            }
            
            status.innerHTML = statusHtml;
        }

        // Update status
        function updateStatus(message, type) {
            const statusEl = document.getElementById('currentStatus');
            statusEl.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // Log function
        function log(message, type = 'info') {
            const logEl = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const colors = {
                info: '#00ff00',
                success: '#28a745',
                error: '#dc3545',
                warning: '#ffc107'
            };
            
            const logEntry = document.createElement('div');
            logEntry.style.color = colors[type] || colors.info;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            
            logEl.appendChild(logEntry);
            logEl.scrollTop = logEl.scrollHeight;

            console.log(`[${type.toUpperCase()}] ${message}`);
        }

        // Clear log
        function clearLog() {
            document.getElementById('log').innerHTML = '';
            log('تم مسح السجل', 'info');
        }

        // Global debug function
        window.debugUserForm = function() {
            debugForm();
        };
    </script>
</body>
</html>
