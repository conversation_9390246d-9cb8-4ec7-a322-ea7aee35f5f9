<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تحديث الواجهة</title>
    <style>
        body {
            font-family: 'Cairo', Arial, sans-serif;
            margin: 20px;
            background: #f0f2f5;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 15px 30px;
            margin: 10px 5px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 18px;
            font-weight: bold;
            width: 100%;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #45a049;
            transform: translateY(-2px);
        }
        .btn.warning { background: #ff9800; }
        .btn.warning:hover { background: #e68900; }
        .btn.info { background: #2196F3; }
        .btn.info:hover { background: #1976D2; }
        
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: bold;
            text-align: center;
        }
        .success { background: #d4edda; color: #155724; border: 2px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 2px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 2px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 2px solid #bee5eb; }
        
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        
        .data-count {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: white;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        
        .count-number {
            font-size: 24px;
            font-weight: bold;
            color: #4CAF50;
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <h1>🔄 تحديث الواجهة</h1>
        
        <div class="status info">
            هذه الأداة لإجبار تحديث الواجهة بعد تحميل البيانات من Firebase
        </div>

        <div class="section">
            <h3>📊 البيانات الحالية</h3>
            <div id="current-data">
                <div class="data-count">
                    <span>📦 المنتجات:</span>
                    <span id="products-count" class="count-number">-</span>
                </div>
                <div class="data-count">
                    <span>👥 العملاء:</span>
                    <span id="customers-count" class="count-number">-</span>
                </div>
                <div class="data-count">
                    <span>🔐 المستخدمون:</span>
                    <span id="users-count" class="count-number">-</span>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>🔄 أدوات التحديث</h3>
            <button class="btn" onclick="forceUIRefresh()">🔄 تحديث الواجهة فوراً</button>
            <button class="btn info" onclick="reloadGlobalVariables()">📥 إعادة تحميل المتغيرات</button>
            <button class="btn warning" onclick="refreshCurrentPage()">🔃 تحديث الصفحة الحالية</button>
        </div>

        <div id="status" class="status info">جاري فحص البيانات...</div>

        <div class="section">
            <h3>📋 تعليمات الاستخدام</h3>
            <ol>
                <li><strong>تحديث الواجهة فوراً:</strong> يحدث الواجهة بالبيانات الموجودة في localStorage</li>
                <li><strong>إعادة تحميل المتغيرات:</strong> يعيد تحميل المتغيرات العامة من localStorage</li>
                <li><strong>تحديث الصفحة:</strong> يحدث الصفحة الحالية للتطبيق الرئيسي</li>
            </ol>
        </div>
    </div>

    <script>
        const statusElement = document.getElementById('status');
        
        // Check data on load
        window.addEventListener('load', function() {
            checkCurrentData();
        });

        function checkCurrentData() {
            try {
                const products = JSON.parse(localStorage.getItem('inventory_products') || '[]');
                const customers = JSON.parse(localStorage.getItem('inventory_customers') || '[]');
                const users = JSON.parse(localStorage.getItem('systemUsers') || '[]');
                
                document.getElementById('products-count').textContent = products.length;
                document.getElementById('customers-count').textContent = customers.length;
                document.getElementById('users-count').textContent = users.length;
                
                statusElement.className = 'status success';
                statusElement.textContent = `✅ تم العثور على ${products.length} منتج، ${customers.length} عميل، ${users.length} مستخدم`;
                
            } catch (error) {
                statusElement.className = 'status error';
                statusElement.textContent = '❌ خطأ في قراءة البيانات: ' + error.message;
            }
        }

        function forceUIRefresh() {
            statusElement.className = 'status info';
            statusElement.textContent = '🔄 جاري تحديث الواجهة...';
            
            try {
                // Method 1: Use the global function if available
                if (typeof window.forceUIRefresh === 'function') {
                    window.forceUIRefresh();
                    statusElement.className = 'status success';
                    statusElement.textContent = '✅ تم تحديث الواجهة باستخدام الوظيفة العامة';
                    return;
                }
                
                // Method 2: Try to access parent window functions
                if (window.opener && typeof window.opener.forceUIRefresh === 'function') {
                    window.opener.forceUIRefresh();
                    statusElement.className = 'status success';
                    statusElement.textContent = '✅ تم تحديث الواجهة في النافذة الرئيسية';
                    return;
                }
                
                // Method 3: Manual refresh
                reloadGlobalVariables();
                
                statusElement.className = 'status warning';
                statusElement.textContent = '⚠️ تم تحديث المتغيرات، قد تحتاج لتحديث الصفحة الرئيسية يدوياً';
                
            } catch (error) {
                statusElement.className = 'status error';
                statusElement.textContent = '❌ خطأ في تحديث الواجهة: ' + error.message;
            }
        }

        function reloadGlobalVariables() {
            statusElement.className = 'status info';
            statusElement.textContent = '📥 جاري إعادة تحميل المتغيرات...';
            
            try {
                // Reload data from localStorage
                const products = JSON.parse(localStorage.getItem('inventory_products') || '[]');
                const customers = JSON.parse(localStorage.getItem('inventory_customers') || '[]');
                const users = JSON.parse(localStorage.getItem('systemUsers') || '[]');
                const settings = JSON.parse(localStorage.getItem('systemSettings') || '{}');
                
                // Try to update global variables in parent window
                if (window.opener) {
                    if (typeof window.opener.products !== 'undefined') {
                        window.opener.products = products;
                    }
                    if (typeof window.opener.customers !== 'undefined') {
                        window.opener.customers = customers;
                    }
                    if (typeof window.opener.systemUsers !== 'undefined') {
                        window.opener.systemUsers = users;
                    }
                    if (typeof window.opener.settings !== 'undefined') {
                        window.opener.settings = settings;
                    }
                }
                
                // Update local display
                checkCurrentData();
                
                statusElement.className = 'status success';
                statusElement.textContent = '✅ تم إعادة تحميل المتغيرات بنجاح';
                
            } catch (error) {
                statusElement.className = 'status error';
                statusElement.textContent = '❌ خطأ في إعادة تحميل المتغيرات: ' + error.message;
            }
        }

        function refreshCurrentPage() {
            statusElement.className = 'status info';
            statusElement.textContent = '🔃 جاري تحديث الصفحة الرئيسية...';
            
            try {
                if (window.opener) {
                    window.opener.location.reload();
                    statusElement.className = 'status success';
                    statusElement.textContent = '✅ تم تحديث الصفحة الرئيسية';
                    
                    // Close this window after a delay
                    setTimeout(() => {
                        window.close();
                    }, 2000);
                } else {
                    statusElement.className = 'status warning';
                    statusElement.textContent = '⚠️ لا يمكن الوصول للصفحة الرئيسية، حدثها يدوياً';
                }
                
            } catch (error) {
                statusElement.className = 'status error';
                statusElement.textContent = '❌ خطأ في تحديث الصفحة: ' + error.message;
            }
        }

        // Auto-refresh data every 5 seconds
        setInterval(checkCurrentData, 5000);
    </script>
</body>
</html>
