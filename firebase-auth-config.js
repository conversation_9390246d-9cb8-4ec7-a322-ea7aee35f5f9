// 🔐 إعدادات Firebase Auth للمصادقة واسترداد كلمة المرور
// Firebase Authentication Configuration

// Firebase Auth settings
const authSettings = {
    // Email verification settings
    emailVerification: {
        enabled: false, // تعطيل التحقق من البريد الإلكتروني للآن
        continueUrl: window.location.origin + '/login-system.html'
    },
    
    // Password reset settings
    passwordReset: {
        enabled: true,
        continueUrl: window.location.origin + '/reset-password.html',
        handleCodeInApp: true
    },
    
    // Sign-in settings
    signIn: {
        enableMultipleAccounts: false,
        requireDisplayName: false
    }
};

// Initialize Firebase Auth settings
function initializeFirebaseAuth() {
    console.log('🔐 تهيئة إعدادات Firebase Auth...');
    
    try {
        if (!firebase.auth) {
            console.warn('⚠️ Firebase Auth غير متاح');
            return false;
        }

        // Configure auth settings
        const auth = firebase.auth();
        
        // Set language to Arabic
        auth.languageCode = 'ar';
        
        // Configure action code settings for password reset
        window.actionCodeSettings = {
            url: authSettings.passwordReset.continueUrl,
            handleCodeInApp: authSettings.passwordReset.handleCodeInApp
        };
        
        console.log('✅ تم تهيئة إعدادات Firebase Auth');
        return true;
        
    } catch (error) {
        console.error('❌ خطأ في تهيئة Firebase Auth:', error);
        return false;
    }
}

// Enhanced password reset function
window.sendPasswordResetEmailEnhanced = async function(email) {
    console.log('📧 إرسال بريد استرداد كلمة المرور المحسن إلى:', email);
    
    try {
        // Validate email format
        if (!isValidEmail(email)) {
            throw new Error('البريد الإلكتروني غير صحيح');
        }

        // Check if Firebase Auth is ready
        if (!firebase.auth) {
            throw new Error('Firebase Auth غير متاح');
        }

        const auth = firebase.auth();
        
        // Send password reset email with custom settings
        await auth.sendPasswordResetEmail(email, window.actionCodeSettings);
        
        console.log('✅ تم إرسال بريد استرداد كلمة المرور بنجاح');
        
        return {
            success: true,
            message: `تم إرسال رابط استرداد كلمة المرور إلى ${email}. يرجى التحقق من بريدك الإلكتروني وصندوق الرسائل غير المرغوب فيها.`,
            email: email
        };
        
    } catch (error) {
        console.error('❌ خطأ في إرسال بريد استرداد كلمة المرور:', error);
        
        return {
            success: false,
            message: getAuthErrorMessage(error),
            error: error.code
        };
    }
};

// Enhanced password reset verification
window.verifyPasswordResetCodeEnhanced = async function(actionCode) {
    console.log('🔍 التحقق المحسن من رمز استرداد كلمة المرور...');
    
    try {
        if (!firebase.auth) {
            throw new Error('Firebase Auth غير متاح');
        }

        const auth = firebase.auth();
        
        // Verify the action code and get the email
        const email = await auth.verifyPasswordResetCode(actionCode);
        
        console.log('✅ رمز استرداد كلمة المرور صحيح للبريد:', email);
        
        return {
            success: true,
            email: email,
            message: 'رمز استرداد كلمة المرور صحيح'
        };
        
    } catch (error) {
        console.error('❌ خطأ في التحقق من رمز الاسترداد:', error);
        
        return {
            success: false,
            message: getAuthErrorMessage(error),
            error: error.code
        };
    }
};

// Enhanced password reset confirmation
window.confirmPasswordResetEnhanced = async function(actionCode, newPassword) {
    console.log('🔐 تأكيد تغيير كلمة المرور المحسن...');
    
    try {
        // Validate password strength
        if (!isValidPassword(newPassword)) {
            throw new Error('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
        }

        if (!firebase.auth) {
            throw new Error('Firebase Auth غير متاح');
        }

        const auth = firebase.auth();
        
        // Confirm the password reset
        await auth.confirmPasswordReset(actionCode, newPassword);
        
        console.log('✅ تم تغيير كلمة المرور بنجاح');
        
        return {
            success: true,
            message: 'تم تغيير كلمة المرور بنجاح! يمكنك الآن تسجيل الدخول بكلمة المرور الجديدة.'
        };
        
    } catch (error) {
        console.error('❌ خطأ في تغيير كلمة المرور:', error);
        
        return {
            success: false,
            message: getAuthErrorMessage(error),
            error: error.code
        };
    }
};

// Get user-friendly error messages
function getAuthErrorMessage(error) {
    const errorMessages = {
        'auth/user-not-found': 'البريد الإلكتروني غير مسجل في النظام',
        'auth/invalid-email': 'البريد الإلكتروني غير صحيح',
        'auth/too-many-requests': 'تم إرسال عدد كبير من الطلبات. يرجى المحاولة لاحقاً',
        'auth/network-request-failed': 'خطأ في الاتصال بالإنترنت',
        'auth/expired-action-code': 'رابط استرداد كلمة المرور منتهي الصلاحية',
        'auth/invalid-action-code': 'رابط استرداد كلمة المرور غير صحيح',
        'auth/user-disabled': 'هذا الحساب معطل',
        'auth/weak-password': 'كلمة المرور ضعيفة جداً. يجب أن تكون 6 أحرف على الأقل',
        'auth/operation-not-allowed': 'هذه العملية غير مسموحة',
        'auth/invalid-credential': 'بيانات الاعتماد غير صحيحة'
    };
    
    return errorMessages[error.code] || error.message || 'حدث خطأ غير متوقع';
}

// Validate email format
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Validate password strength
function isValidPassword(password) {
    return password && password.length >= 6;
}

// Check if user is authenticated
window.checkAuthState = function() {
    return new Promise((resolve) => {
        if (!firebase.auth) {
            resolve(null);
            return;
        }

        firebase.auth().onAuthStateChanged((user) => {
            resolve(user);
        });
    });
};

// Sign out user
window.signOutUser = async function() {
    try {
        if (!firebase.auth) {
            throw new Error('Firebase Auth غير متاح');
        }

        await firebase.auth().signOut();
        console.log('✅ تم تسجيل الخروج بنجاح');
        
        return {
            success: true,
            message: 'تم تسجيل الخروج بنجاح'
        };
        
    } catch (error) {
        console.error('❌ خطأ في تسجيل الخروج:', error);
        
        return {
            success: false,
            message: getAuthErrorMessage(error)
        };
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Wait for Firebase to be ready
    setTimeout(() => {
        initializeFirebaseAuth();
    }, 1000);
});

// Export settings for use in other files
window.authSettings = authSettings;

console.log('🔐 تم تحميل إعدادات Firebase Auth');
